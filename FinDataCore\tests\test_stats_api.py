"""
统计API测试
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch
from app.main import app

client = TestClient(app)

class TestStatsAPI:
    """统计API测试类"""
    
    @pytest.fixture
    def limited_auth_headers(self):
        """有限权限认证头"""
        response = client.post(
            "/api/v1/auth/login",
            json={"password": "TT2024"}
        )
        token = response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    
    @pytest.fixture
    def standard_auth_headers(self):
        """标准权限认证头"""
        response = client.post(
            "/api/v1/auth/login",
            json={"password": "881017"}
        )
        token = response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    
    @pytest.fixture
    def full_auth_headers(self):
        """完全权限认证头"""
        response = client.post(
            "/api/v1/auth/login",
            json={"password": "Doolin"}
        )
        token = response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    
    def test_customer_summary_with_standard_permission(self, standard_auth_headers):
        """测试标准权限用户访问客户汇总"""
        with patch('app.services.data_service.data_service.get_customer_summary') as mock_service:
            mock_service.return_value = {
                "customer_info": {"name": "张三", "phone": "13800138000"},
                "summary_stats": {"total_orders": 5, "total_amount": 50000},
                "receivable_by_periods": [],
                "order_details": [],
                "financial_flow": []
            }
            
            response = client.get(
                "/api/v1/stats/customer_summary",
                params={"customer_name": "张三"},
                headers=standard_auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "customer_info" in data
            assert data["customer_info"]["name"] == "张三"
            mock_service.assert_called_once_with("张三")
    
    def test_customer_summary_with_limited_permission(self, limited_auth_headers):
        """测试有限权限用户访问客户汇总（应该被拒绝）"""
        response = client.get(
            "/api/v1/stats/customer_summary",
            params={"customer_name": "张三"},
            headers=limited_auth_headers
        )
        
        assert response.status_code == 403  # Forbidden
    
    def test_customer_summary_missing_name(self, standard_auth_headers):
        """测试缺少客户名参数"""
        response = client.get(
            "/api/v1/stats/customer_summary",
            headers=standard_auth_headers
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_summary_data_valid_params(self, limited_auth_headers):
        """测试有效的数据汇总参数"""
        with patch('app.services.data_service.data_service.get_summary_data') as mock_service:
            mock_service.return_value = {
                "summary_stats": {"total": 100, "average": 50},
                "period_data": [{"period": "2025-01", "count": 10}],
                "chart_data": {"labels": ["Jan"], "values": [10]}
            }
            
            response = client.get(
                "/api/v1/stats/summary_data",
                params={
                    "start_date": "2025-01-01",
                    "end_date": "2025-01-31"
                },
                headers=limited_auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "summary_stats" in data
            assert "period_data" in data
            mock_service.assert_called_once_with("2025-01-01", "2025-01-31")
    
    def test_summary_data_invalid_date_range(self, limited_auth_headers):
        """测试无效的日期范围"""
        response = client.get(
            "/api/v1/stats/summary_data",
            params={
                "start_date": "2025-01-31",
                "end_date": "2025-01-01"  # 结束日期早于开始日期
            },
            headers=limited_auth_headers
        )
        
        # 这里的行为取决于我们的验证逻辑
        # 可能返回400或422
        assert response.status_code in [400, 422]
    
    def test_order_summary_valid_date(self, limited_auth_headers):
        """测试有效的订单汇总"""
        with patch('app.services.data_service.data_service.get_order_summary') as mock_service:
            mock_service.return_value = {
                "monthly_summary": [
                    {"month": "2025-01", "orders": 100, "amount": 1000000}
                ],
                "total_stats": {"total_orders": 100, "total_amount": 1000000}
            }
            
            response = client.get(
                "/api/v1/stats/order_summary",
                params={"end_date": "2025-01-31"},
                headers=limited_auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "monthly_summary" in data
            assert "total_stats" in data
            mock_service.assert_called_once_with("2025-01-31")
    
    def test_compatibility_summary_route(self, limited_auth_headers):
        """测试兼容性汇总路由"""
        with patch('app.services.data_service.data_service.get_summary_data') as mock_service:
            mock_service.return_value = {
                "summary_stats": {},
                "period_data": [],
                "chart_data": None
            }
            
            response = client.get(
                "/api/v1/stats/summary",
                params={
                    "start_date": "2025-01-01",
                    "end_date": "2025-01-31"
                },
                headers=limited_auth_headers
            )
            
            assert response.status_code == 200
    
    def test_ping_endpoint(self):
        """测试ping端点"""
        response = client.get("/api/v1/stats/ping")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "ok"
        assert "统计API服务正常运行" in data["message"]
    
    def test_status_endpoint(self):
        """测试状态端点"""
        with patch('app.services.external_api.api_client.check_status') as mock_check:
            mock_check.return_value = {"status": "ok", "message": "外部API正常"}
            
            response = client.get("/api/v1/stats/status")
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "ok"
            assert "external_api" in data
    
    def test_api_proxy_not_implemented(self, limited_auth_headers):
        """测试API代理（未实现）"""
        response = client.get(
            "/api/v1/stats/proxy/test_endpoint",
            headers=limited_auth_headers
        )
        
        assert response.status_code == 501  # Not implemented
    
    def test_service_error_handling(self, standard_auth_headers):
        """测试服务错误处理"""
        with patch('app.services.data_service.data_service.get_customer_summary') as mock_service:
            mock_service.return_value = {"error": "客户不存在"}
            
            response = client.get(
                "/api/v1/stats/customer_summary",
                params={"customer_name": "不存在的客户"},
                headers=standard_auth_headers
            )
            
            assert response.status_code == 400
            assert "客户不存在" in response.json()["detail"]
    
    def test_service_exception_handling(self, standard_auth_headers):
        """测试服务异常处理"""
        with patch('app.services.data_service.data_service.get_customer_summary') as mock_service:
            mock_service.side_effect = Exception("数据库连接失败")
            
            response = client.get(
                "/api/v1/stats/customer_summary",
                params={"customer_name": "张三"},
                headers=standard_auth_headers
            )
            
            assert response.status_code == 500
            assert "服务器内部错误" in response.json()["detail"]
    
    def test_unauthorized_access(self):
        """测试未授权访问"""
        response = client.get(
            "/api/v1/stats/customer_summary",
            params={"customer_name": "张三"}
        )
        
        assert response.status_code == 403  # Forbidden
    
    def test_invalid_token_access(self):
        """测试无效token访问"""
        response = client.get(
            "/api/v1/stats/customer_summary",
            params={"customer_name": "张三"},
            headers={"Authorization": "Bearer invalid_token"}
        )
        
        assert response.status_code == 401  # Unauthorized
