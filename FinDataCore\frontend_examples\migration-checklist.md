# 前端迁移检查清单

## 🔄 迁移步骤

### 1. 准备工作
- [ ] 备份现有前端代码
- [ ] 确认FastAPI服务正常运行
- [ ] 测试API端点可访问性
- [ ] 准备测试数据和测试用例

### 2. 核心文件替换
- [ ] 添加 `auth-manager.js` 到项目中
- [ ] 添加 `api-client.js` 到项目中
- [ ] 更新登录页面 (`login.html`)
- [ ] 添加 `dashboard-adapter.js` 到主页面

### 3. HTML页面修改

#### 登录页面
- [ ] 更新登录表单的action和method
- [ ] 添加JWT Token处理逻辑
- [ ] 更新错误处理和用户反馈
- [ ] 测试不同权限级别的登录

#### 主页面/仪表板
- [ ] 引入新的JavaScript文件
- [ ] 更新页面初始化逻辑
- [ ] 添加认证状态检查
- [ ] 更新用户权限显示

#### 数据展示页面
- [ ] 更新API调用方式
- [ ] 修改数据加载逻辑
- [ ] 更新错误处理
- [ ] 测试分页和搜索功能

### 4. JavaScript代码修改

#### 原有API调用替换
```javascript
// 原Flask版本
fetch('/api/filter_data?date=2025-01-15')

// 替换为FastAPI版本
apiClient.filterData('2025-01-15')
```

#### 认证处理
- [ ] 移除Session相关代码
- [ ] 添加JWT Token管理
- [ ] 更新请求头处理
- [ ] 添加Token过期处理

#### 错误处理
- [ ] 更新HTTP状态码处理
- [ ] 添加统一错误处理函数
- [ ] 更新用户提示消息
- [ ] 添加网络错误重试机制

### 5. 样式和UI更新
- [ ] 检查CSS样式兼容性
- [ ] 更新加载状态显示
- [ ] 添加权限相关的UI控制
- [ ] 测试响应式布局

### 6. 功能测试

#### 认证功能
- [ ] 测试有效密码登录
- [ ] 测试无效密码登录
- [ ] 测试Token过期处理
- [ ] 测试自动登录检查
- [ ] 测试登出功能

#### 查询功能
- [ ] 测试日期筛选
- [ ] 测试客户订单查询
- [ ] 测试逾期订单查询
- [ ] 测试搜索功能
- [ ] 测试分页功能

#### 统计功能
- [ ] 测试客户汇总（需要标准权限）
- [ ] 测试数据汇总
- [ ] 测试订单汇总
- [ ] 测试权限控制

#### 权限控制
- [ ] 测试有限权限用户功能
- [ ] 测试标准权限用户功能
- [ ] 测试完全权限用户功能
- [ ] 测试权限不足时的处理

### 7. 性能优化
- [ ] 实现请求缓存
- [ ] 添加防抖和节流
- [ ] 优化大数据量显示
- [ ] 测试并发请求处理

### 8. 错误处理和用户体验
- [ ] 添加加载状态指示器
- [ ] 实现友好的错误提示
- [ ] 添加操作确认对话框
- [ ] 优化页面响应速度

### 9. 兼容性测试
- [ ] 测试不同浏览器兼容性
- [ ] 测试移动端适配
- [ ] 测试网络不稳定情况
- [ ] 测试大数据量处理

### 10. 部署和上线
- [ ] 更新生产环境配置
- [ ] 测试生产环境功能
- [ ] 准备回滚方案
- [ ] 通知用户变更内容

## 🔧 代码修改示例

### 1. 页面初始化
```javascript
// 原版本
$(document).ready(function() {
    loadInitialData();
});

// 新版本
document.addEventListener('DOMContentLoaded', async () => {
    if (!authManager.isAuthenticated()) {
        window.location.href = '/login';
        return;
    }
    await dashboardAdapter.initialize();
});
```

### 2. API调用
```javascript
// 原版本
function loadFilterData(date) {
    $.get('/api/filter_data', { date: date }, function(data) {
        displayData(data.results);
    });
}

// 新版本
async function loadFilterData(date) {
    try {
        const data = await apiClient.filterData(date);
        displayData(data.results);
    } catch (error) {
        console.error('加载数据失败:', error);
        showError(error.message);
    }
}
```

### 3. 表单提交
```javascript
// 原版本
$('#searchForm').submit(function(e) {
    e.preventDefault();
    var customerName = $('#customerName').val();
    loadCustomerData(customerName);
});

// 新版本
document.getElementById('searchForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const customerName = document.getElementById('customerName').value;
    try {
        const data = await apiClient.getCustomerOrders(customerName);
        displayCustomerData(data);
    } catch (error) {
        showError('查询失败: ' + error.message);
    }
});
```

## 🚨 常见问题和解决方案

### 1. CORS问题
如果遇到跨域问题，确保FastAPI配置了正确的CORS设置：
```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### 2. Token过期处理
```javascript
// 在API客户端中自动处理Token过期
if (response.status === 401) {
    authManager.logout();
    window.location.href = '/login';
    return;
}
```

### 3. 缓存问题
清除浏览器缓存或添加版本号：
```html
<script src="auth-manager.js?v=1.0.0"></script>
<script src="api-client.js?v=1.0.0"></script>
```

### 4. 数据格式差异
如果API响应格式有变化，需要添加数据适配器：
```javascript
function adaptApiResponse(data) {
    // 适配数据格式
    return {
        results: data.results || [],
        total: data.total || 0,
        columns: data.columns || []
    };
}
```

## 📋 测试用例

### 认证测试
1. 使用有效密码登录
2. 使用无效密码登录
3. Token过期后的自动跳转
4. 页面刷新后的自动登录

### 功能测试
1. 日期筛选功能
2. 客户查询功能
3. 逾期订单查询
4. 分页和搜索
5. 数据导出（如果有权限）

### 权限测试
1. 有限权限用户只能看到基础功能
2. 标准权限用户可以访问汇总功能
3. 完全权限用户可以访问所有功能

### 错误处理测试
1. 网络错误处理
2. 服务器错误处理
3. 权限不足处理
4. 参数错误处理

## 🎯 验收标准

- [ ] 所有原有功能正常工作
- [ ] 用户体验与原版本一致或更好
- [ ] 权限控制正确实施
- [ ] 错误处理友好且完整
- [ ] 性能满足要求
- [ ] 兼容主流浏览器
- [ ] 代码质量良好，易于维护

## 📞 支持和帮助

如果在迁移过程中遇到问题：
1. 查看浏览器控制台错误信息
2. 检查网络请求和响应
3. 验证API端点是否正确
4. 确认认证状态和权限
5. 查看FastAPI服务日志

迁移完成后，建议保留原版本代码一段时间，以便必要时快速回滚。
