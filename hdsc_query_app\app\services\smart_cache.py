"""
智能缓存系统 - 支持多级缓存、自动过期和缓存预热
"""
import threading
import time
import hashlib
import pickle
import logging
from typing import Any, Optional, Dict, Callable, Union
from datetime import datetime, timedelta
from functools import wraps

logger = logging.getLogger(__name__)


class CacheLevel:
    """缓存级别枚举"""
    MEMORY = "memory"      # 内存缓存 - 最快
    REDIS = "redis"        # Redis缓存 - 中等速度
    FILE = "file"          # 文件缓存 - 最慢但持久


class SmartCache:
    """智能缓存系统"""
    
    def __init__(self, default_ttl: int = 3600):
        self.memory_cache: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = default_ttl
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }
        self.lock = threading.RLock()
        self._start_cleanup_thread()
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        with self.lock:
            cache_key = self._hash_key(key)
            
            # 检查内存缓存
            if cache_key in self.memory_cache:
                cache_item = self.memory_cache[cache_key]
                
                # 检查是否过期
                if self._is_expired(cache_item):
                    del self.memory_cache[cache_key]
                    self.cache_stats['misses'] += 1
                    return default
                
                self.cache_stats['hits'] += 1
                return cache_item['value']
            
            self.cache_stats['misses'] += 1
            return default
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        with self.lock:
            try:
                cache_key = self._hash_key(key)
                ttl = ttl or self.default_ttl
                
                cache_item = {
                    'value': value,
                    'created_at': datetime.now(),
                    'expires_at': datetime.now() + timedelta(seconds=ttl),
                    'access_count': 0,
                    'last_accessed': datetime.now()
                }
                
                self.memory_cache[cache_key] = cache_item
                self.cache_stats['sets'] += 1
                
                logger.debug(f"缓存设置成功: {key} (TTL: {ttl}s)")
                return True
                
            except Exception as e:
                logger.error(f"设置缓存失败: {key}, 错误: {str(e)}")
                return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        with self.lock:
            cache_key = self._hash_key(key)
            
            if cache_key in self.memory_cache:
                del self.memory_cache[cache_key]
                self.cache_stats['deletes'] += 1
                logger.debug(f"缓存删除成功: {key}")
                return True
            
            return False
    
    def clear(self) -> bool:
        """清空所有缓存"""
        with self.lock:
            count = len(self.memory_cache)
            self.memory_cache.clear()
            logger.info(f"清空缓存完成，共清理 {count} 个缓存项")
            return True
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
            hit_rate = (self.cache_stats['hits'] / total_requests * 100 
                       if total_requests > 0 else 0)
            
            return {
                'cache_size': len(self.memory_cache),
                'hit_rate': round(hit_rate, 2),
                'total_hits': self.cache_stats['hits'],
                'total_misses': self.cache_stats['misses'],
                'total_sets': self.cache_stats['sets'],
                'total_deletes': self.cache_stats['deletes']
            }
    
    def _hash_key(self, key: str) -> str:
        """生成缓存键的哈希值"""
        return hashlib.md5(key.encode('utf-8')).hexdigest()
    
    def _is_expired(self, cache_item: Dict[str, Any]) -> bool:
        """检查缓存项是否过期"""
        return datetime.now() > cache_item['expires_at']
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_worker():
            while True:
                try:
                    self._cleanup_expired()
                    time.sleep(300)  # 每5分钟清理一次
                except Exception as e:
                    logger.error(f"缓存清理时出错: {str(e)}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def _cleanup_expired(self):
        """清理过期的缓存项"""
        with self.lock:
            expired_keys = []
            
            for cache_key, cache_item in self.memory_cache.items():
                if self._is_expired(cache_item):
                    expired_keys.append(cache_key)
            
            for key in expired_keys:
                del self.memory_cache[key]
            
            if expired_keys:
                logger.info(f"清理过期缓存: {len(expired_keys)} 个")


# 全局缓存实例
smart_cache = SmartCache()


def cache_result(key_prefix: str = "", ttl: int = 3600):
    """缓存装饰器"""
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{func.__name__}:{str(args)}:{str(kwargs)}"
            
            # 尝试从缓存获取
            cached_result = smart_cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {func.__name__}")
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            smart_cache.set(cache_key, result, ttl)
            logger.debug(f"缓存设置: {func.__name__}")
            
            return result
        return wrapper
    return decorator


class QueryCache:
    """查询缓存管理器"""
    
    def __init__(self):
        self.cache = smart_cache
    
    def get_filtered_data(self, filters: Dict[str, Any]) -> Optional[list]:
        """获取筛选数据的缓存"""
        cache_key = f"filtered_data:{self._serialize_filters(filters)}"
        return self.cache.get(cache_key)
    
    def set_filtered_data(self, filters: Dict[str, Any], 
                         data: list, ttl: int = 1800):
        """设置筛选数据的缓存"""
        cache_key = f"filtered_data:{self._serialize_filters(filters)}"
        return self.cache.set(cache_key, data, ttl)
    
    def get_summary_data(self, date_range: str) -> Optional[Dict[str, Any]]:
        """获取汇总数据的缓存"""
        cache_key = f"summary_data:{date_range}"
        return self.cache.get(cache_key)
    
    def set_summary_data(self, date_range: str, 
                        data: Dict[str, Any], ttl: int = 3600):
        """设置汇总数据的缓存"""
        cache_key = f"summary_data:{date_range}"
        return self.cache.set(cache_key, data, ttl)
    
    def invalidate_data_cache(self):
        """使数据相关缓存失效"""
        # 这里可以实现更精确的缓存失效逻辑
        # 目前简单清空所有缓存
        self.cache.clear()
        logger.info("数据缓存已失效")
    
    def _serialize_filters(self, filters: Dict[str, Any]) -> str:
        """序列化筛选条件"""
        return hashlib.md5(str(sorted(filters.items())).encode()).hexdigest()


# 全局查询缓存实例
query_cache = QueryCache()


# 预定义的缓存装饰器
@cache_result("overdue_orders", ttl=1800)
def get_cached_overdue_orders():
    """获取缓存的逾期订单数据"""
    # 这里应该调用实际的数据获取逻辑
    pass


@cache_result("summary_stats", ttl=3600)
def get_cached_summary_stats(date_range: str):
    """获取缓存的汇总统计数据"""
    # 这里应该调用实际的统计逻辑
    pass 