"""
安全认证模块 - JWT Token认证
"""
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status
from pydantic import BaseModel

from app.core.config import settings

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class Token(BaseModel):
    """Token响应模型"""
    access_token: str
    token_type: str
    expires_in: int
    user_level: str

class TokenData(BaseModel):
    """Token数据模型"""
    username: Optional[str] = None
    user_level: Optional[str] = None

class User(BaseModel):
    """用户模型"""
    username: str
    user_level: str
    permissions: list

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)

def authenticate_user(password: str) -> Optional[Dict[str, Any]]:
    """
    认证用户 - 基于密码的简单认证
    
    Args:
        password: 用户输入的密码
        
    Returns:
        用户信息字典或None
    """
    # 检查密码是否在用户权限配置中
    if password in settings.USER_LEVELS:
        user_level = settings.USER_LEVELS[password]
        return {
            "username": password,  # 使用密码作为用户名（保持与原系统兼容）
            "user_level": user_level,
            "permissions": get_permissions_by_level(user_level)
        }
    return None

def get_permissions_by_level(user_level: str) -> list:
    """
    根据用户级别获取权限列表
    
    Args:
        user_level: 用户级别
        
    Returns:
        权限列表
    """
    permissions_map = {
        "limited": ["read"],
        "standard": ["read", "export"],
        "full": ["read", "export", "admin"]
    }
    return permissions_map.get(user_level, [])

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    创建访问令牌
    
    Args:
        data: 要编码的数据
        expires_delta: 过期时间增量
        
    Returns:
        JWT令牌字符串
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[TokenData]:
    """
    验证令牌
    
    Args:
        token: JWT令牌
        
    Returns:
        TokenData对象或None
    """
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username: str = payload.get("sub")
        user_level: str = payload.get("user_level")
        
        if username is None:
            return None
            
        token_data = TokenData(username=username, user_level=user_level)
        return token_data
    except JWTError:
        return None

def get_current_user(token_data: TokenData) -> User:
    """
    获取当前用户
    
    Args:
        token_data: 令牌数据
        
    Returns:
        User对象
    """
    if token_data.username is None or token_data.user_level is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    permissions = get_permissions_by_level(token_data.user_level)
    user = User(
        username=token_data.username,
        user_level=token_data.user_level,
        permissions=permissions
    )
    return user

def check_permission(user: User, required_permission: str) -> bool:
    """
    检查用户权限
    
    Args:
        user: 用户对象
        required_permission: 所需权限
        
    Returns:
        是否有权限
    """
    return required_permission in user.permissions

def has_permission(user_level: str, required_level: str) -> bool:
    """
    检查用户级别是否满足要求
    
    Args:
        user_level: 用户级别
        required_level: 所需级别
        
    Returns:
        是否满足权限要求
    """
    level_hierarchy = {
        "limited": 1,
        "standard": 2,
        "full": 3
    }
    
    user_level_value = level_hierarchy.get(user_level, 0)
    required_level_value = level_hierarchy.get(required_level, 0)
    
    return user_level_value >= required_level_value
