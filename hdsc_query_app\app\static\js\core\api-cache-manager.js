/**
 * API缓存管理器
 * 统一管理API数据的缓存存储和获取
 * 
 * @module ApiCacheManager
 * @version 2.0
 */

const ApiCacheManager = {
  /**
   * 配置选项
   */
  config: {
    // 缓存过期时间 (毫秒)
    defaultExpiry: 5 * 60 * 1000, // 5分钟
    
    // 最大缓存条目数
    maxEntries: 100,
    
    // 存储类型
    storageType: 'memory', // 'memory', 'session', 'local'
    
    // 压缩数据
    enableCompression: false,
    
    // 调试模式
    debug: false
  },

  /**
   * 内存缓存存储
   */
  memoryCache: new Map(),

  /**
   * 缓存统计信息
   */
  stats: {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    errors: 0
  },

  /**
   * 初始化缓存管理器
   * @param {Object} options - 配置选项
   */
  init(options = {}) {
    // 合并配置
    Object.assign(this.config, options);
    
    // 初始化存储
    this.initializeStorage();
    
    // 启动清理任务
    this.startCleanupTask();
    
    // 绑定页面卸载事件
    this.bindUnloadEvents();
    
    this.log('ApiCacheManager: 初始化完成', {
      storageType: this.config.storageType,
      defaultExpiry: this.config.defaultExpiry,
      maxEntries: this.config.maxEntries
    });
  },

  /**
   * 存储数据到缓存
   * @param {string} key - 缓存键
   * @param {*} data - 要缓存的数据
   * @param {Object} options - 选项
   * @returns {boolean} 是否成功
   */
  set(key, data, options = {}) {
    try {
      const {
        expiry = this.config.defaultExpiry,
        priority = 'normal',
        tags = []
      } = options;

      // 验证参数
      if (!this.validateKey(key)) {
        throw new Error('Invalid cache key');
      }

      // 创建缓存条目
      const entry = {
        key,
        data: this.config.enableCompression ? this.compress(data) : data,
        timestamp: Date.now(),
        expiry: Date.now() + expiry,
        priority,
        tags: Array.isArray(tags) ? tags : [],
        compressed: this.config.enableCompression,
        size: this.calculateSize(data)
      };

      // 检查缓存大小限制
      this.ensureCacheSize();

      // 存储到对应的存储类型
      const success = this.storeEntry(key, entry);
      
      if (success) {
        this.stats.sets++;
        this.log('Cache SET:', key, { size: entry.size, expiry });
      }

      return success;

    } catch (error) {
      this.stats.errors++;
      this.error('Failed to set cache:', error);
      return false;
    }
  },

  /**
   * 从缓存获取数据
   * @param {string} key - 缓存键
   * @param {*} defaultValue - 默认值
   * @returns {*} 缓存的数据或默认值
   */
  get(key, defaultValue = null) {
    try {
      // 验证参数
      if (!this.validateKey(key)) {
        this.stats.misses++;
        return defaultValue;
      }

      // 从存储获取条目
      const entry = this.getEntry(key);
      
      if (!entry) {
        this.stats.misses++;
        this.log('Cache MISS:', key);
        return defaultValue;
      }

      // 检查是否过期
      if (this.isExpired(entry)) {
        this.delete(key);
        this.stats.misses++;
        this.log('Cache EXPIRED:', key);
        return defaultValue;
      }

      // 解压数据（如果需要）
      const data = entry.compressed ? this.decompress(entry.data) : entry.data;
      
      this.stats.hits++;
      this.log('Cache HIT:', key, { age: Date.now() - entry.timestamp });
      
      return data;

    } catch (error) {
      this.stats.errors++;
      this.error('Failed to get cache:', error);
      return defaultValue;
    }
  },

  /**
   * 检查缓存是否存在且未过期
   * @param {string} key - 缓存键
   * @returns {boolean} 是否存在
   */
  has(key) {
    try {
      const entry = this.getEntry(key);
      return entry && !this.isExpired(entry);
    } catch (error) {
      this.error('Failed to check cache:', error);
      return false;
    }
  },

  /**
   * 删除缓存条目
   * @param {string} key - 缓存键
   * @returns {boolean} 是否成功
   */
  delete(key) {
    try {
      const success = this.deleteEntry(key);
      
      if (success) {
        this.stats.deletes++;
        this.log('Cache DELETE:', key);
      }
      
      return success;
    } catch (error) {
      this.stats.errors++;
      this.error('Failed to delete cache:', error);
      return false;
    }
  },

  /**
   * 清空所有缓存
   * @param {Object} options - 选项
   */
  clear(options = {}) {
    try {
      const { tags = [], pattern } = options;
      
      if (tags.length > 0) {
        // 按标签清理
        this.clearByTags(tags);
      } else if (pattern) {
        // 按模式清理
        this.clearByPattern(pattern);
      } else {
        // 清空所有
        this.clearAll();
      }
      
      this.log('Cache CLEAR:', { tags, pattern });
    } catch (error) {
      this.error('Failed to clear cache:', error);
    }
  },

  /**
   * 获取或设置缓存（如果不存在则调用工厂函数）
   * @param {string} key - 缓存键
   * @param {Function} factory - 数据工厂函数
   * @param {Object} options - 选项
   * @returns {Promise<*>} 数据
   */
  async getOrSet(key, factory, options = {}) {
    try {
      // 先尝试从缓存获取
      const cached = this.get(key);
      if (cached !== null) {
        return cached;
      }

      // 防止并发请求
      const lockKey = `__lock_${key}`;
      if (this.has(lockKey)) {
        // 等待锁释放
        await this.waitForLock(lockKey);
        return this.get(key);
      }

      // 设置锁
      this.set(lockKey, true, { expiry: 30000 }); // 30秒锁

      try {
        // 调用工厂函数获取数据
        const data = await factory();
        
        // 存储到缓存
        this.set(key, data, options);
        
        return data;
      } finally {
        // 释放锁
        this.delete(lockKey);
      }

    } catch (error) {
      this.error('Failed to getOrSet cache:', error);
      throw error;
    }
  },

  /**
   * 批量操作
   * @param {Object} operations - 操作对象
   * @returns {Object} 结果
   */
  batch(operations) {
    const results = {};
    
    try {
      // 批量获取
      if (operations.get) {
        results.get = {};
        operations.get.forEach(key => {
          results.get[key] = this.get(key);
        });
      }

      // 批量设置
      if (operations.set) {
        results.set = {};
        Object.entries(operations.set).forEach(([key, value]) => {
          results.set[key] = this.set(key, value);
        });
      }

      // 批量删除
      if (operations.delete) {
        results.delete = {};
        operations.delete.forEach(key => {
          results.delete[key] = this.delete(key);
        });
      }

      return results;
    } catch (error) {
      this.error('Failed to execute batch operations:', error);
      return { error: error.message };
    }
  },

  /**
   * 生成缓存键
   * @param {string} prefix - 前缀
   * @param {Object} params - 参数
   * @returns {string} 缓存键
   */
  generateKey(prefix, params = {}) {
    try {
      // 对参数进行排序以确保一致性
      const sortedParams = Object.keys(params)
        .sort()
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&');
      
      return sortedParams ? `${prefix}:${sortedParams}` : prefix;
    } catch (error) {
      this.error('Failed to generate cache key:', error);
      return `${prefix}:${Date.now()}`;
    }
  },

  /**
   * 获取缓存统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0 
      ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
      : '0.00';

    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      totalSize: this.getTotalSize(),
      entryCount: this.getEntryCount()
    };
  },

  /**
   * 导出缓存数据
   * @returns {Object} 缓存数据
   */
  export() {
    try {
      const data = {};
      
      if (this.config.storageType === 'memory') {
        this.memoryCache.forEach((entry, key) => {
          if (!this.isExpired(entry)) {
            data[key] = {
              data: entry.data,
              timestamp: entry.timestamp,
              expiry: entry.expiry,
              tags: entry.tags
            };
          }
        });
      }
      
      return data;
    } catch (error) {
      this.error('Failed to export cache:', error);
      return {};
    }
  },

  /**
   * 导入缓存数据
   * @param {Object} data - 缓存数据
   */
  import(data) {
    try {
      Object.entries(data).forEach(([key, entry]) => {
        if (!this.isExpired(entry)) {
          this.set(key, entry.data, {
            expiry: entry.expiry - Date.now(),
            tags: entry.tags
          });
        }
      });
      
      this.log('Cache imported:', Object.keys(data).length, 'entries');
    } catch (error) {
      this.error('Failed to import cache:', error);
    }
  },

  // ========================================
  // 私有方法
  // ========================================

  /**
   * 初始化存储
   */
  initializeStorage() {
    switch (this.config.storageType) {
      case 'session':
        this.storage = sessionStorage;
        break;
      case 'local':
        this.storage = localStorage;
        break;
      default:
        this.storage = null; // 使用内存存储
    }
  },

  /**
   * 存储条目
   * @param {string} key - 键
   * @param {Object} entry - 条目
   * @returns {boolean} 是否成功
   */
  storeEntry(key, entry) {
    try {
      if (this.config.storageType === 'memory') {
        this.memoryCache.set(key, entry);
      } else if (this.storage) {
        this.storage.setItem(`cache_${key}`, JSON.stringify(entry));
      }
      return true;
    } catch (error) {
      this.error('Failed to store entry:', error);
      return false;
    }
  },

  /**
   * 获取条目
   * @param {string} key - 键
   * @returns {Object|null} 条目
   */
  getEntry(key) {
    try {
      if (this.config.storageType === 'memory') {
        return this.memoryCache.get(key) || null;
      } else if (this.storage) {
        const data = this.storage.getItem(`cache_${key}`);
        if (!data) return null;
        
        // 使用安全的JSON解析
        if (typeof DataValidator !== 'undefined') {
            return DataValidator.safeJsonParse(data, null, `Cache-${key}`);
        } else {
            // 降级到传统解析方式
            try {
                return JSON.parse(data);
            } catch (error) {
                this.error(`JSON解析失败 for key ${key}:`, error);
                return null;
            }
        }
      }
      return null;
    } catch (error) {
      this.error('Failed to get entry:', error);
      return null;
    }
  },

  /**
   * 删除条目
   * @param {string} key - 键
   * @returns {boolean} 是否成功
   */
  deleteEntry(key) {
    try {
      if (this.config.storageType === 'memory') {
        return this.memoryCache.delete(key);
      } else if (this.storage) {
        this.storage.removeItem(`cache_${key}`);
        return true;
      }
      return false;
    } catch (error) {
      this.error('Failed to delete entry:', error);
      return false;
    }
  },

  /**
   * 检查条目是否过期
   * @param {Object} entry - 条目
   * @returns {boolean} 是否过期
   */
  isExpired(entry) {
    return Date.now() > entry.expiry;
  },

  /**
   * 验证缓存键
   * @param {string} key - 键
   * @returns {boolean} 是否有效
   */
  validateKey(key) {
    return key && typeof key === 'string' && key.length > 0 && key.length <= 250;
  },

  /**
   * 计算数据大小
   * @param {*} data - 数据
   * @returns {number} 大小（字节）
   */
  calculateSize(data) {
    try {
      return new Blob([JSON.stringify(data)]).size;
    } catch (error) {
      return 0;
    }
  },

  /**
   * 确保缓存大小不超过限制
   */
  ensureCacheSize() {
    if (this.config.storageType === 'memory') {
      while (this.memoryCache.size >= this.config.maxEntries) {
        // 删除最老的条目
        const oldestKey = this.memoryCache.keys().next().value;
        this.memoryCache.delete(oldestKey);
      }
    }
  },

  /**
   * 启动清理任务
   */
  startCleanupTask() {
    // 每5分钟清理一次过期条目
    setInterval(() => {
      this.cleanupExpired();
    }, 5 * 60 * 1000);
  },

  /**
   * 清理过期条目
   */
  cleanupExpired() {
    let cleaned = 0;
    
    if (this.config.storageType === 'memory') {
      for (const [key, entry] of this.memoryCache) {
        if (this.isExpired(entry)) {
          this.memoryCache.delete(key);
          cleaned++;
        }
      }
    }
    
    if (cleaned > 0) {
      this.log('Cleaned up expired entries:', cleaned);
    }
  },

  /**
   * 等待锁释放
   * @param {string} lockKey - 锁键
   * @returns {Promise} Promise
   */
  waitForLock(lockKey) {
    return new Promise((resolve) => {
      const checkLock = () => {
        if (!this.has(lockKey)) {
          resolve();
        } else {
          setTimeout(checkLock, 100);
        }
      };
      checkLock();
    });
  },

  /**
   * 绑定页面卸载事件
   */
  bindUnloadEvents() {
    if (typeof window !== 'undefined') {
      EventManager.bind(window, 'beforeunload', () => {
        // 导出重要缓存到sessionStorage
        if (this.config.storageType === 'memory') {
          try {
            const exportData = this.export();
            sessionStorage.setItem('cache_backup', JSON.stringify(exportData));
          } catch (error) {
            this.error('Failed to backup cache on unload:', error);
          }
        }
      });
    }
  },

  /**
   * 日志输出
   */
  log(...args) {
    if (this.config.debug) {
      console.log('[ApiCacheManager]', ...args);
    }
  },

  /**
   * 错误输出
   */
  error(...args) {
    console.error('[ApiCacheManager]', ...args);
  },

  /**
   * 获取总大小
   * @returns {number} 总大小
   */
  getTotalSize() {
    let total = 0;
    if (this.config.storageType === 'memory') {
      for (const entry of this.memoryCache.values()) {
        total += entry.size || 0;
      }
    }
    return total;
  },

  /**
   * 获取条目数量
   * @returns {number} 条目数量
   */
  getEntryCount() {
    if (this.config.storageType === 'memory') {
      return this.memoryCache.size;
    }
    return 0;
  },

  /**
   * 数据压缩（简单实现）
   * @param {*} data - 数据
   * @returns {string} 压缩后的数据
   */
  compress(data) {
    // 简单的JSON字符串压缩，实际项目中可以使用更好的压缩算法
    return JSON.stringify(data);
  },

  /**
   * 数据解压
   * @param {string} data - 压缩的数据
   * @returns {*} 解压后的数据
   */
  decompress(data) {
    if (typeof DataValidator !== 'undefined') {
      return DataValidator.safeJsonParse(data, data, 'Cache-Decompress');
    } else {
      try {
        return JSON.parse(data);
      } catch (error) {
        console.error('Cache decompress JSON解析失败:', error);
        return data; // 返回原始数据作为降级处理
      }
    }
  },

  /**
   * 按标签清理
   * @param {Array} tags - 标签数组
   */
  clearByTags(tags) {
    const toDelete = [];
    
    if (this.config.storageType === 'memory') {
      for (const [key, entry] of this.memoryCache) {
        if (entry.tags && entry.tags.some(tag => tags.includes(tag))) {
          toDelete.push(key);
        }
      }
    }
    
    toDelete.forEach(key => this.delete(key));
  },

  /**
   * 按模式清理
   * @param {string|RegExp} pattern - 模式
   */
  clearByPattern(pattern) {
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
    const toDelete = [];
    
    if (this.config.storageType === 'memory') {
      for (const key of this.memoryCache.keys()) {
        if (regex.test(key)) {
          toDelete.push(key);
        }
      }
    }
    
    toDelete.forEach(key => this.delete(key));
  },

  /**
   * 清空所有缓存
   */
  clearAll() {
    if (this.config.storageType === 'memory') {
      this.memoryCache.clear();
    } else if (this.storage) {
      // 清理所有以cache_开头的项
      const keysToDelete = [];
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i);
        if (key && key.startsWith('cache_')) {
          keysToDelete.push(key);
        }
      }
      keysToDelete.forEach(key => this.storage.removeItem(key));
    }
    
    // 重置统计
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0
    };
  }
};

// 向后兼容
if (typeof window !== 'undefined') {
  // 创建兼容的ApiDataManager
  window.ApiDataManager = {
    set: (key, data) => ApiCacheManager.set(key, data),
    get: (key, defaultValue) => ApiCacheManager.get(key, defaultValue),
    generateCacheKey: (prefix, params) => ApiCacheManager.generateKey(prefix, params),
    clearOldData: () => ApiCacheManager.clear()
  };

  // 导出新的管理器
  window.ApiCacheManager = ApiCacheManager;

  // 全局API数据缓存（向后兼容）
  window.apiDataCache = new Map();
}

// 模块导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ApiCacheManager;
}

// 自动初始化
if (typeof document !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      ApiCacheManager.init();
    });
  } else {
    ApiCacheManager.init();
  }
}