/**
 * Todo Component - 待办事项组件
 * 独立可复用的待办事项模块
 */

class TodoModule {
    constructor(triggerElement) {
        this.triggerElement = triggerElement;
        this.modal = null;
        this.form = null;
        this.todoList = null;
        this.todos = this.loadTodos();
        
        this.init();
    }

    init() {
        this.bindTrigger();
        this.setupModal();
        this.renderTodoList();
    }

    bindTrigger() {
        if (this.triggerElement) {
            this.triggerElement.addEventListener('click', (e) => {
                e.preventDefault();
                this.show();
            });
        }
    }

    setupModal() {
        this.modal = document.getElementById('todoModal');
        if (this.modal) {
            this.form = this.modal.querySelector('#todoForm');
            this.bindModalEvents();
            this.bindFormEvents();
        }
        
        this.todoList = document.getElementById('todoList');
    }

    bindModalEvents() {
        // 模态框事件处理
        this.modal.addEventListener('show.bs.modal', () => {
            this.resetForm();
        });

        this.modal.addEventListener('shown.bs.modal', () => {
            const titleInput = this.modal.querySelector('#todoTitle');
            if (titleInput) {
                titleInput.focus();
            }
        });

        this.modal.addEventListener('hide.bs.modal', () => {
            this.clearFocus();
        });

        this.modal.addEventListener('hidden.bs.modal', () => {
            this.resetForm();
            this.restoreFocus();
        });

        // 关闭按钮特殊处理
        const closeButtons = this.modal.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.clearFocus();
            });
        });
    }

    bindFormEvents() {
        const saveBtn = this.modal.querySelector('#saveTodoBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.saveTodo();
            });
        }

        if (this.form) {
            this.form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveTodo();
            });
        }
    }

    saveTodo() {
        const formData = this.getFormData();
        
        if (!this.validateTodo(formData)) {
            return;
        }

        const todo = {
            id: this.generateId(),
            title: formData.title,
            description: formData.description,
            dueDate: formData.dueDate,
            priority: formData.priority,
            completed: false,
            createdAt: new Date().toISOString()
        };

        this.todos.push(todo);
        this.saveTodos();
        this.renderTodoList();
        this.hide();
        this.showSuccessMessage('待办事项已添加');
    }

    getFormData() {
        return {
            title: this.modal.querySelector('#todoTitle')?.value?.trim() || '',
            description: this.modal.querySelector('#todoDesc')?.value?.trim() || '',
            dueDate: this.modal.querySelector('#todoDueDate')?.value || '',
            priority: this.modal.querySelector('#todoPriority')?.value || 'medium'
        };
    }

    validateTodo(formData) {
        if (!formData.title) {
            this.showErrorMessage('请输入待办事项标题');
            return false;
        }
        
        if (formData.title.length > 100) {
            this.showErrorMessage('标题长度不能超过100个字符');
            return false;
        }
        
        return true;
    }

    renderTodoList() {
        if (!this.todoList) return;

        // 如果没有自定义待办事项，保持默认显示
        if (this.todos.length === 0) {
            return;
        }

        // 按优先级和创建时间排序
        const sortedTodos = this.todos.sort((a, b) => {
            const priorityOrder = { high: 3, medium: 2, low: 1 };
            const aPriority = priorityOrder[a.priority] || 2;
            const bPriority = priorityOrder[b.priority] || 2;
            
            if (aPriority !== bPriority) {
                return bPriority - aPriority;
            }
            
            return new Date(b.createdAt) - new Date(a.createdAt);
        });

        const todoHTML = sortedTodos.map(todo => this.renderTodoItem(todo)).join('');
        this.todoList.innerHTML = todoHTML;
        
        // 重新绑定事件
        this.bindTodoEvents();
    }

    renderTodoItem(todo) {
        const priorityColors = {
            high: 'danger',
            medium: 'warning', 
            low: 'info'
        };
        
        const priorityLabels = {
            high: '紧急',
            medium: '中等',
            low: '一般'
        };
        
        const dueDateText = todo.dueDate ? this.formatDueDate(todo.dueDate) : '';
        
        return `
            <li class="list-group-item d-flex align-items-center px-3 py-3 ${todo.completed ? 'completed' : ''}" data-todo-id="${todo.id}">
                <div class="form-check me-3">
                    <input class="form-check-input todo-checkbox" type="checkbox" 
                           ${todo.completed ? 'checked' : ''} data-todo-id="${todo.id}">
                </div>
                <div class="todo-content flex-grow-1">
                    <h6 class="mb-1 ${todo.completed ? 'text-muted' : ''}">${this.escapeHtml(todo.title)}</h6>
                    ${dueDateText ? `<span class="text-muted smaller">截止日期: ${dueDateText}</span>` : ''}
                    ${todo.description ? `<p class="mb-0 text-muted small mt-1">${this.escapeHtml(todo.description)}</p>` : ''}
                </div>
                <div class="ms-auto d-flex align-items-center gap-2">
                    <span class="badge bg-${priorityColors[todo.priority]}">${priorityLabels[todo.priority]}</span>
                    <button class="btn btn-sm btn-outline-danger delete-todo" data-todo-id="${todo.id}" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </li>
        `;
    }

    bindTodoEvents() {
        // 复选框事件
        this.todoList.querySelectorAll('.todo-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.toggleTodo(e.target.dataset.todoId, e.target.checked);
            });
        });

        // 删除按钮事件
        this.todoList.querySelectorAll('.delete-todo').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.deleteTodo(e.target.closest('[data-todo-id]').dataset.todoId);
            });
        });
    }

    toggleTodo(todoId, completed) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            todo.completed = completed;
            this.saveTodos();
            
            // 更新UI
            const todoItem = this.todoList.querySelector(`[data-todo-id="${todoId}"]`);
            if (todoItem) {
                todoItem.classList.toggle('completed', completed);
                const title = todoItem.querySelector('h6');
                if (title) {
                    title.classList.toggle('text-muted', completed);
                }
            }
        }
    }

    deleteTodo(todoId) {
        if (confirm('确定要删除这个待办事项吗？')) {
            this.todos = this.todos.filter(t => t.id !== todoId);
            this.saveTodos();
            this.renderTodoList();
            this.showSuccessMessage('待办事项已删除');
        }
    }

    formatDueDate(dateString) {
        const date = new Date(dateString);
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        if (date.toDateString() === today.toDateString()) {
            return '今天';
        } else if (date.toDateString() === tomorrow.toDateString()) {
            return '明天';
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    }

    // 数据管理方法
    loadTodos() {
        try {
            const saved = localStorage.getItem('home-todos');
            return saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.error('Failed to load todos:', error);
            return [];
        }
    }

    saveTodos() {
        try {
            localStorage.setItem('home-todos', JSON.stringify(this.todos));
        } catch (error) {
            console.error('Failed to save todos:', error);
            this.showErrorMessage('保存失败，请检查存储空间');
        }
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // 工具方法
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    resetForm() {
        if (this.form) {
            this.form.reset();
        }
    }

    show() {
        const todoModal = new bootstrap.Modal(this.modal);
        todoModal.show();
    }

    hide() {
        const todoModal = bootstrap.Modal.getInstance(this.modal);
        if (todoModal) {
            todoModal.hide();
        }
    }

    clearFocus() {
        const focusedElement = document.activeElement;
        if (focusedElement && this.modal.contains(focusedElement)) {
            focusedElement.blur();
        }
    }

    restoreFocus() {
        if (this.triggerElement && document.activeElement === document.body) {
            setTimeout(() => {
                this.triggerElement.focus();
            }, 100);
        }
    }

    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }

    showErrorMessage(message) {
        this.showMessage(message, 'danger');
    }

    showMessage(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    destroy() {
        // 清理事件监听器
        if (this.triggerElement) {
            this.triggerElement.removeEventListener('click', this.show);
        }
        
        // 清理模态框事件
        if (this.modal) {
            const events = ['show.bs.modal', 'shown.bs.modal', 'hide.bs.modal', 'hidden.bs.modal'];
            events.forEach(event => {
                this.modal.removeEventListener(event, this[event]);
            });
        }
    }
}

// 导出模块
window.TodoModule = TodoModule;