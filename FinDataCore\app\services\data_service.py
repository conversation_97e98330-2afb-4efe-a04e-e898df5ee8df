"""
数据服务层 - FastAPI版本
"""
import logging
import math
from datetime import datetime, date
from typing import Dict, Any, List, Optional
from app.services.external_api import api_client
from app.services.cache_service import cache_service

logger = logging.getLogger(__name__)

class DataService:
    """数据服务类"""
    
    def __init__(self):
        """初始化数据服务"""
        self.logger = logging.getLogger(__name__)
        self.api_client = api_client
        self.cache_service = cache_service
    
    async def get_filtered_data(self, date: str) -> Dict[str, Any]:
        """
        按日期获取筛选数据
        
        Args:
            date: 筛选日期
            
        Returns:
            筛选结果字典
        """
        self.logger.info(f"获取筛选数据，日期: {date}")
        
        # 生成缓存键
        cache_key = self.cache_service.generate_cache_key("filter_data", {"date": date})
        
        # 使用缓存
        return await self.cache_service.get_or_set(
            cache_key,
            self._fetch_filtered_data,
            None,  # 使用默认超时
            date
        )
    
    async def _fetch_filtered_data(self, date: str) -> Dict[str, Any]:
        """获取筛选数据的内部方法"""
        data = await self.api_client.get_filter_data(date=date)
        
        # 检查错误
        if "error" in data:
            self.logger.error(f"获取筛选数据时出错: {data['error']}")
            return {"error": data["error"], "results": []}
        
        # 处理数据
        processed_data = self._process_filter_data(data)
        return processed_data
    
    async def get_overdue_orders(
        self, 
        page: int = 1, 
        limit: int = 10, 
        force_refresh: bool = False, 
        search_query: str = ""
    ) -> Dict[str, Any]:
        """
        获取逾期订单数据，支持分页
        
        Args:
            page: 页码，从1开始
            limit: 每页数据量
            force_refresh: 是否强制刷新缓存
            search_query: 搜索关键词
            
        Returns:
            逾期订单列表，带分页信息
        """
        self.logger.info(f"获取逾期订单数据，页码: {page}, 每页数量: {limit}, 强制刷新: {force_refresh}")
        
        # 缓存键
        cache_key = "overdue_data_full"
        
        # 如果强制刷新，删除缓存
        if force_refresh:
            await self.cache_service.delete(cache_key)
            self.logger.info("强制刷新，已清除逾期订单缓存")
        
        # 获取完整数据
        full_data = await self.cache_service.get_or_set(
            cache_key,
            self._fetch_overdue_data,
            3600  # 1小时缓存
        )
        
        # 分页处理
        return self._paginate_data(full_data, page, limit, search_query)
    
    async def _fetch_overdue_data(self) -> Dict[str, Any]:
        """获取逾期订单数据的内部方法"""
        data = await self.api_client.get_overdue_data(limit=100000)
        
        # 检查错误
        if "error" in data:
            self.logger.error(f"获取逾期订单数据时出错: {data['error']}")
            return {"error": data["error"], "results": []}
        
        # 处理数据
        processed_data = self._process_overdue_data(data)
        return processed_data
    
    async def get_orders_by_customer(self, customer_name: str) -> Dict[str, Any]:
        """
        获取客户订单数据
        
        Args:
            customer_name: 客户姓名
            
        Returns:
            客户订单列表
        """
        self.logger.info(f"获取客户订单数据，客户: {customer_name}")
        
        # 生成缓存键
        cache_key = self.cache_service.generate_cache_key(
            "filter_orders_by_customer_name", 
            {"customer_name": customer_name}
        )
        
        # 使用缓存
        return await self.cache_service.get_or_set(
            cache_key,
            self._fetch_customer_orders,
            600,  # 10分钟缓存
            customer_name
        )
    
    async def _fetch_customer_orders(self, customer_name: str) -> Dict[str, Any]:
        """获取客户订单数据的内部方法"""
        data = await self.api_client.get_customer_data(customer_name)
        
        # 检查错误
        if "error" in data:
            self.logger.error(f"获取客户订单数据时出错: {data['error']}")
            return {"error": data["error"], "results": []}
        
        # 处理数据
        processed_data = self._process_customer_data(data)
        return processed_data
    
    async def get_customer_summary(self, customer_name: str) -> Dict[str, Any]:
        """
        获取客户汇总数据
        
        Args:
            customer_name: 客户姓名
            
        Returns:
            客户汇总数据
        """
        self.logger.info(f"获取客户汇总数据，客户: {customer_name}")
        
        # 生成缓存键
        cache_key = self.cache_service.generate_cache_key(
            "customer_summary", 
            {"customer_name": customer_name}
        )
        
        # 使用缓存
        return await self.cache_service.get_or_set(
            cache_key,
            self._fetch_customer_summary,
            600,  # 10分钟缓存
            customer_name
        )
    
    async def _fetch_customer_summary(self, customer_name: str) -> Dict[str, Any]:
        """获取客户汇总数据的内部方法"""
        data = await self.api_client.get_customer_summary(customer_name)
        
        # 检查错误
        if "error" in data:
            self.logger.error(f"获取客户汇总数据时出错: {data['error']}")
            return {"error": data["error"]}
        
        # 处理数据
        processed_data = self._process_customer_summary_data(data)
        return processed_data
    
    async def get_summary_data(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """
        获取数据汇总
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            汇总数据
        """
        self.logger.info(f"获取数据汇总，开始日期: {start_date}, 结束日期: {end_date}")
        
        # 生成缓存键
        cache_key = self.cache_service.generate_cache_key(
            "summary_data", 
            {"start_date": start_date, "end_date": end_date}
        )
        
        # 使用缓存
        return await self.cache_service.get_or_set(
            cache_key,
            self._fetch_summary_data,
            1800,  # 30分钟缓存
            start_date,
            end_date
        )
    
    async def _fetch_summary_data(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """获取数据汇总的内部方法"""
        data = await self.api_client.get_summary_data(start_date, end_date)
        
        # 检查错误
        if "error" in data:
            self.logger.error(f"获取数据汇总时出错: {data['error']}")
            return {"error": data["error"]}
        
        # 处理数据
        processed_data = self._process_summary_data(data)
        return processed_data
    
    async def get_order_summary(self, end_date: str) -> Dict[str, Any]:
        """
        获取订单汇总数据
        
        Args:
            end_date: 结束日期
            
        Returns:
            订单汇总数据
        """
        self.logger.info(f"获取订单汇总数据，结束日期: {end_date}")
        
        # 生成缓存键
        cache_key = self.cache_service.generate_cache_key(
            "order_summary", 
            {"end_date": end_date}
        )
        
        # 使用缓存
        return await self.cache_service.get_or_set(
            cache_key,
            self._fetch_order_summary,
            1800,  # 30分钟缓存
            end_date
        )
    
    async def _fetch_order_summary(self, end_date: str) -> Dict[str, Any]:
        """获取订单汇总数据的内部方法"""
        data = await self.api_client.get_order_summary(end_date)
        
        # 检查错误
        if "error" in data:
            self.logger.error(f"获取订单汇总数据时出错: {data['error']}")
            return {"error": data["error"]}
        
        # 处理数据
        processed_data = self._process_order_summary_data(data)
        return processed_data

    def _process_filter_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理筛选数据"""
        if not data or "results" not in data:
            return {"results": [], "columns": [], "total": 0}

        results = data.get("results", [])

        # 处理期数列数据
        processed_results = []
        for item in results:
            processed_item = self._process_period_data_item(item)
            processed_results.append(processed_item)

        return {
            "results": processed_results,
            "columns": data.get("columns", []),
            "total": len(processed_results)
        }

    def _process_overdue_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理逾期订单数据"""
        if not data or "results" not in data:
            return {"results": [], "columns": [], "total": 0}

        results = data.get("results", [])

        # 处理期数列数据
        processed_results = []
        for item in results:
            processed_item = self._process_period_data_item(item)
            processed_results.append(processed_item)

        return {
            "results": processed_results,
            "columns": data.get("columns", []),
            "total": len(processed_results)
        }

    def _process_customer_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理客户订单数据"""
        if not data or "results" not in data:
            return {"results": [], "columns": [], "total": 0}

        results = data.get("results", [])

        # 处理期数列数据
        processed_results = []
        for item in results:
            processed_item = self._process_period_data_item(item)
            processed_results.append(processed_item)

        return {
            "results": processed_results,
            "columns": data.get("columns", []),
            "total": len(processed_results)
        }

    def _process_customer_summary_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理客户汇总数据"""
        # 这里需要根据原项目的具体处理逻辑来实现
        # 暂时直接返回原数据
        return data

    def _process_summary_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理数据汇总"""
        # 这里需要根据原项目的具体处理逻辑来实现
        # 暂时直接返回原数据
        return data

    def _process_order_summary_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理订单汇总数据"""
        # 这里需要根据原项目的具体处理逻辑来实现
        # 暂时直接返回原数据
        return data

    def _process_period_data_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个数据项的期数信息

        Args:
            item: 数据项

        Returns:
            处理后的数据项
        """
        # 这里实现期数列数据处理逻辑
        # 根据原项目的process_period_data函数逻辑

        if not isinstance(item, dict):
            return item

        # 复制原数据
        processed_item = item.copy()

        # 处理账单状态和期数关联
        # 这里需要根据具体的业务逻辑来实现

        return processed_item

    def _paginate_data(
        self,
        data: Dict[str, Any],
        page: int,
        limit: int,
        search_query: str = ""
    ) -> Dict[str, Any]:
        """
        分页处理数据

        Args:
            data: 完整数据
            page: 页码
            limit: 每页数量
            search_query: 搜索关键词

        Returns:
            分页后的数据
        """
        if "error" in data:
            return {
                "error": data["error"],
                "results": [],
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": 0,
                    "pages": 0
                }
            }

        results = data.get("results", [])

        # 搜索过滤
        if search_query:
            filtered_results = []
            search_lower = search_query.lower()
            for item in results:
                # 在所有字段中搜索
                item_str = str(item).lower()
                if search_lower in item_str:
                    filtered_results.append(item)
            results = filtered_results

        # 计算分页信息
        total_count = len(results)
        total_pages = math.ceil(total_count / limit) if limit > 0 else 1

        # 计算起始和结束索引
        start_index = (page - 1) * limit
        end_index = start_index + limit

        # 获取当前页数据
        page_results = results[start_index:end_index]

        # 构造返回结果
        return {
            "results": page_results,
            "columns": data.get("columns", []),
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total_count,
                "pages": total_pages
            },
            "cache_info": {
                "last_update": datetime.now().isoformat(),
                "next_update": None
            }
        }

# 创建全局数据服务实例
data_service = DataService()
