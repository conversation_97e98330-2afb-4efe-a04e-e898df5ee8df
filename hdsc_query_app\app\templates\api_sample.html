<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逾期订单API数据格式示例</title>
    <link href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
    <style>
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .json-key {
            color: #d63384;
        }
        .json-string {
            color: #20c997;
        }
        .json-number {
            color: #0d6efd;
        }
        .json-boolean {
            color: #fd7e14;
        }
        .json-null {
            color: #6c757d;
        }
        .card {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <h1 class="mb-4">逾期订单API数据格式示例</h1>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">API 端点</h5>
            </div>
            <div class="card-body">
                <code>GET /api/filter_overdue_orders</code>
                <p class="mt-2 mb-0">此API返回所有逾期订单的数据，包括订单详情和统计信息。</p>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">响应数据格式</h5>
            </div>
            <div class="card-body">
                <pre><code>{
    <span class="json-key">"results"</span>: [
        {
            <span class="json-key">"订单日期"</span>: <span class="json-string">"2023-05-15"</span>,
            <span class="json-key">"订单编号"</span>: <span class="json-string">"ORD20230515001"</span>,
            <span class="json-key">"客户姓名"</span>: <span class="json-string">"张三"</span>,
            <span class="json-key">"贷后状态"</span>: <span class="json-string">"逾期"</span>,
            <span class="json-key">"客户手机"</span>: <span class="json-string">"138****1234"</span>,
            <span class="json-key">"客服归属"</span>: <span class="json-string">"王客服"</span>,
            <span class="json-key">"业务归属"</span>: <span class="json-string">"李业务"</span>,
            <span class="json-key">"产品"</span>: <span class="json-string">"消费贷"</span>,
            <span class="json-key">"期数"</span>: <span class="json-number">12</span>,
            <span class="json-key">"总待收"</span>: <span class="json-number">10000.00</span>,
            <span class="json-key">"当前待收"</span>: <span class="json-number">5000.00</span>,
            <span class="json-key">"首次逾期期数"</span>: <span class="json-number">3</span>,
            <span class="json-key">"账单日期"</span>: <span class="json-string">"2023-08-15"</span>,
            <span class="json-key">"逾期天数"</span>: <span class="json-number">45</span>,
            <span class="json-key">"备注信息"</span>: <span class="json-string">"已电话联系"</span>
        },
        {
            <span class="json-key">"订单日期"</span>: <span class="json-string">"2023-06-20"</span>,
            <span class="json-key">"订单编号"</span>: <span class="json-string">"ORD20230620002"</span>,
            <span class="json-key">"客户姓名"</span>: <span class="json-string">"李四"</span>,
            <span class="json-key">"贷后状态"</span>: <span class="json-string">"严重逾期"</span>,
            <span class="json-key">"客户手机"</span>: <span class="json-string">"139****5678"</span>,
            <span class="json-key">"客服归属"</span>: <span class="json-string">"赵客服"</span>,
            <span class="json-key">"业务归属"</span>: <span class="json-string">"钱业务"</span>,
            <span class="json-key">"产品"</span>: <span class="json-string">"经营贷"</span>,
            <span class="json-key">"期数"</span>: <span class="json-number">24</span>,
            <span class="json-key">"总待收"</span>: <span class="json-number">50000.00</span>,
            <span class="json-key">"当前待收"</span>: <span class="json-number">35000.00</span>,
            <span class="json-key">"首次逾期期数"</span>: <span class="json-number">5</span>,
            <span class="json-key">"账单日期"</span>: <span class="json-string">"2023-09-20"</span>,
            <span class="json-key">"逾期天数"</span>: <span class="json-number">90</span>,
            <span class="json-key">"备注信息"</span>: <span class="json-string">"无法联系"</span>
        },
        {
            <span class="json-key">"订单日期"</span>: <span class="json-string">"2023-07-10"</span>,
            <span class="json-key">"订单编号"</span>: <span class="json-string">"ORD20230710003"</span>,
            <span class="json-key">"客户姓名"</span>: <span class="json-string">"王五"</span>,
            <span class="json-key">"贷后状态"</span>: <span class="json-string">"轻微逾期"</span>,
            <span class="json-key">"客户手机"</span>: <span class="json-string">"137****9012"</span>,
            <span class="json-key">"客服归属"</span>: <span class="json-string">"孙客服"</span>,
            <span class="json-key">"业务归属"</span>: <span class="json-string">"周业务"</span>,
            <span class="json-key">"产品"</span>: <span class="json-string">"房贷"</span>,
            <span class="json-key">"期数"</span>: <span class="json-number">36</span>,
            <span class="json-key">"总待收"</span>: <span class="json-number">200000.00</span>,
            <span class="json-key">"当前待收"</span>: <span class="json-number">180000.00</span>,
            <span class="json-key">"首次逾期期数"</span>: <span class="json-number">10</span>,
            <span class="json-key">"账单日期"</span>: <span class="json-string">"2023-10-10"</span>,
            <span class="json-key">"逾期天数"</span>: <span class="json-number">15</span>,
            <span class="json-key">"备注信息"</span>: <span class="json-string">"承诺还款"</span>
        }
    ],
    <span class="json-key">"columns"</span>: [
        <span class="json-string">"订单日期"</span>,
        <span class="json-string">"订单编号"</span>,
        <span class="json-string">"客户姓名"</span>,
        <span class="json-string">"贷后状态"</span>,
        <span class="json-string">"客户手机"</span>,
        <span class="json-string">"客服归属"</span>,
        <span class="json-string">"业务归属"</span>,
        <span class="json-string">"产品"</span>,
        <span class="json-string">"期数"</span>,
        <span class="json-string">"总待收"</span>,
        <span class="json-string">"当前待收"</span>,
        <span class="json-string">"首次逾期期数"</span>,
        <span class="json-string">"账单日期"</span>,
        <span class="json-string">"逾期天数"</span>,
        <span class="json-string">"备注信息"</span>
    ],
    <span class="json-key">"summary"</span>: {
        <span class="json-key">"total_count"</span>: <span class="json-number">3</span>,
        <span class="json-key">"total_amount"</span>: <span class="json-number">220000.00</span>,
        <span class="json-key">"avg_overdue_days"</span>: <span class="json-number">50</span>
    }
}</code></pre>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">字段说明</h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>类型</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>results</td>
                            <td>数组</td>
                            <td>逾期订单列表，包含多个订单对象</td>
                        </tr>
                        <tr>
                            <td>columns</td>
                            <td>数组</td>
                            <td>列名列表，用于前端表格显示</td>
                        </tr>
                        <tr>
                            <td>summary</td>
                            <td>对象</td>
                            <td>汇总统计信息</td>
                        </tr>
                        <tr>
                            <td>summary.total_count</td>
                            <td>数字</td>
                            <td>逾期订单总数</td>
                        </tr>
                        <tr>
                            <td>summary.total_amount</td>
                            <td>数字</td>
                            <td>逾期金额总计</td>
                        </tr>
                        <tr>
                            <td>summary.avg_overdue_days</td>
                            <td>数字</td>
                            <td>平均逾期天数</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">使用示例</h5>
            </div>
            <div class="card-body">
                <pre><code>// JavaScript 示例
fetch('/api/filter_overdue_orders')
    .then(response => response.json())
    .then(data => {
        // 处理返回的数据
        console.log(`共有 ${data.results.length} 条逾期订单`);
        console.log(`逾期金额总计: ${data.summary.total_amount}`);
        
        // 显示数据到表格
        displayOverdueData(data);
    })
    .catch(error => {
        console.error('获取逾期订单数据失败:', error);
    });</code></pre>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
</body>
</html>