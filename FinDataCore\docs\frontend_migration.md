# 前端API适配指南

## 概述

本文档说明如何将现有的前端代码从Flask API适配到新的FastAPI接口。主要变化是认证方式从Session改为JWT Token。

## 主要变化

### 1. 认证方式变更

**Flask版本 (Session认证)**:
```javascript
// 无需特殊处理，浏览器自动管理Session
fetch('/api/filter_data?date=2025-01-15')
```

**FastAPI版本 (JWT Token认证)**:
```javascript
// 需要在请求头中包含JWT Token
const token = localStorage.getItem('access_token');
fetch('/api/v1/query/filter_data?date=2025-01-15', {
    headers: {
        'Authorization': `Bearer ${token}`
    }
})
```

### 2. API路径变更

| Flask路径 | FastAPI路径 | 说明 |
|-----------|-------------|------|
| `/api/filter_data` | `/api/v1/query/filter_data` | 日期筛选 |
| `/api/filter_orders_by_customer_name` | `/api/v1/query/filter_orders_by_customer_name` | 客户订单 |
| `/api/overdue` | `/api/v1/query/overdue` | 逾期订单 |
| `/api/customer_summary` | `/api/v1/stats/customer_summary` | 客户汇总 |

**兼容性路径**: 为了简化迁移，FastAPI提供了兼容性路径：
- `/api/v1/query/filter` → `/api/v1/query/filter_data`
- `/api/v1/query/customer` → `/api/v1/query/filter_orders_by_customer_name`

## 适配步骤

### 步骤1: 创建认证管理器

```javascript
// auth-manager.js
class AuthManager {
    constructor() {
        this.token = localStorage.getItem('access_token');
        this.userLevel = localStorage.getItem('user_level');
    }
    
    async login(password) {
        try {
            const response = await fetch('/api/v1/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ password })
            });
            
            if (response.ok) {
                const data = await response.json();
                this.token = data.access_token;
                this.userLevel = data.user_level;
                
                localStorage.setItem('access_token', this.token);
                localStorage.setItem('user_level', this.userLevel);
                localStorage.setItem('permissions', JSON.stringify(data.permissions));
                
                return { success: true, data };
            } else {
                const error = await response.json();
                return { success: false, error: error.detail };
            }
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    logout() {
        this.token = null;
        this.userLevel = null;
        localStorage.removeItem('access_token');
        localStorage.removeItem('user_level');
        localStorage.removeItem('permissions');
    }
    
    isAuthenticated() {
        return !!this.token;
    }
    
    getAuthHeaders() {
        if (!this.token) {
            throw new Error('用户未登录');
        }
        return {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
        };
    }
    
    hasPermission(permission) {
        const permissions = JSON.parse(localStorage.getItem('permissions') || '[]');
        return permissions.includes(permission);
    }
}

// 全局实例
const authManager = new AuthManager();
```

### 步骤2: 创建API客户端

```javascript
// api-client.js
class ApiClient {
    constructor(baseUrl = '') {
        this.baseUrl = baseUrl;
    }
    
    async request(url, options = {}) {
        try {
            // 自动添加认证头
            if (authManager.isAuthenticated()) {
                options.headers = {
                    ...options.headers,
                    ...authManager.getAuthHeaders()
                };
            }
            
            const response = await fetch(this.baseUrl + url, options);
            
            // 处理认证失败
            if (response.status === 401) {
                authManager.logout();
                window.location.href = '/login';
                return;
            }
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.detail || '请求失败');
            }
            
            return await response.json();
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }
    
    // 查询相关API
    async filterData(date) {
        return this.request(`/api/v1/query/filter_data?date=${date}`);
    }
    
    async getCustomerOrders(customerName) {
        return this.request(`/api/v1/query/filter_orders_by_customer_name?customer_name=${encodeURIComponent(customerName)}`);
    }
    
    async getOverdueOrders(page = 1, limit = 10, searchQuery = '') {
        const params = new URLSearchParams({
            page: page.toString(),
            limit: limit.toString(),
            search_query: searchQuery
        });
        return this.request(`/api/v1/query/overdue?${params}`);
    }
    
    // 统计相关API
    async getCustomerSummary(customerName) {
        return this.request(`/api/v1/stats/customer_summary?customer_name=${encodeURIComponent(customerName)}`);
    }
    
    async getSummaryData(startDate, endDate) {
        return this.request(`/api/v1/stats/summary_data?start_date=${startDate}&end_date=${endDate}`);
    }
    
    async getOrderSummary(endDate) {
        return this.request(`/api/v1/stats/order_summary?end_date=${endDate}`);
    }
}

// 全局实例
const apiClient = new ApiClient();
```

### 步骤3: 修改现有代码

**原Flask版本**:
```javascript
// 原来的代码
async function loadFilterData(date) {
    try {
        const response = await fetch(`/api/filter_data?date=${date}`);
        const data = await response.json();
        displayData(data.results);
    } catch (error) {
        console.error('加载数据失败:', error);
    }
}
```

**适配后的FastAPI版本**:
```javascript
// 适配后的代码
async function loadFilterData(date) {
    try {
        const data = await apiClient.filterData(date);
        displayData(data.results);
    } catch (error) {
        console.error('加载数据失败:', error);
        showErrorMessage(error.message);
    }
}
```

### 步骤4: 登录页面适配

```html
<!-- login.html -->
<form id="loginForm">
    <div class="form-group">
        <label for="password">密码:</label>
        <input type="password" id="password" name="password" required>
    </div>
    <button type="submit">登录</button>
</form>

<script>
document.getElementById('loginForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const password = document.getElementById('password').value;
    const result = await authManager.login(password);
    
    if (result.success) {
        // 登录成功，跳转到主页
        window.location.href = '/dashboard';
    } else {
        // 显示错误信息
        alert('登录失败: ' + result.error);
    }
});
</script>
```

### 步骤5: 页面加载时检查认证状态

```javascript
// 在每个需要认证的页面添加
document.addEventListener('DOMContentLoaded', () => {
    if (!authManager.isAuthenticated()) {
        window.location.href = '/login';
        return;
    }
    
    // 页面初始化逻辑
    initializePage();
});
```

## 错误处理

### 统一错误处理

```javascript
function handleApiError(error) {
    if (error.message.includes('401')) {
        // 认证失败，跳转到登录页
        authManager.logout();
        window.location.href = '/login';
    } else if (error.message.includes('403')) {
        // 权限不足
        showErrorMessage('权限不足，请联系管理员');
    } else if (error.message.includes('500')) {
        // 服务器错误
        showErrorMessage('服务器错误，请稍后重试');
    } else {
        // 其他错误
        showErrorMessage(error.message);
    }
}
```

### 网络错误处理

```javascript
function showErrorMessage(message) {
    // 显示错误消息的通用函数
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger';
    errorDiv.textContent = message;
    
    const container = document.querySelector('.container');
    container.insertBefore(errorDiv, container.firstChild);
    
    // 3秒后自动隐藏
    setTimeout(() => {
        errorDiv.remove();
    }, 3000);
}
```

## 权限控制

### 前端权限检查

```javascript
// 根据用户权限显示/隐藏功能
function updateUIBasedOnPermissions() {
    const permissions = JSON.parse(localStorage.getItem('permissions') || '[]');
    
    // 隐藏需要export权限的按钮
    if (!permissions.includes('export')) {
        document.querySelectorAll('.export-btn').forEach(btn => {
            btn.style.display = 'none';
        });
    }
    
    // 隐藏需要admin权限的功能
    if (!permissions.includes('admin')) {
        document.querySelectorAll('.admin-only').forEach(element => {
            element.style.display = 'none';
        });
    }
}
```

## 兼容性考虑

### 渐进式迁移

如果需要渐进式迁移，可以创建一个适配器：

```javascript
// compatibility-adapter.js
class CompatibilityAdapter {
    constructor() {
        this.useNewAPI = localStorage.getItem('use_new_api') === 'true';
    }
    
    async filterData(date) {
        if (this.useNewAPI) {
            return apiClient.filterData(date);
        } else {
            // 使用旧的Flask API
            const response = await fetch(`/api/filter_data?date=${date}`);
            return response.json();
        }
    }
    
    // 其他API方法...
}
```

## 测试建议

### 单元测试

```javascript
// 使用Jest进行测试
describe('AuthManager', () => {
    test('should login successfully with valid password', async () => {
        const result = await authManager.login('TT2024');
        expect(result.success).toBe(true);
        expect(result.data.user_level).toBe('limited');
    });
    
    test('should fail login with invalid password', async () => {
        const result = await authManager.login('invalid');
        expect(result.success).toBe(false);
    });
});
```

### 集成测试

```javascript
describe('API Integration', () => {
    beforeEach(async () => {
        await authManager.login('TT2024');
    });
    
    test('should fetch filter data', async () => {
        const data = await apiClient.filterData('2025-01-15');
        expect(data.results).toBeDefined();
        expect(Array.isArray(data.results)).toBe(true);
    });
});
```

## 部署注意事项

1. **更新API基础URL**: 确保前端代码中的API URL指向新的FastAPI服务
2. **清理旧的Session数据**: 在部署时清理用户浏览器中的旧Session数据
3. **向后兼容**: 考虑保留一段时间的兼容性路径
4. **用户通知**: 通知用户认证方式的变更

## 常见问题

### Q: Token过期后如何处理？
A: 在API客户端中检测401错误，自动跳转到登录页面。

### Q: 如何处理并发请求？
A: 使用Promise.all()或适当的并发控制机制。

### Q: 如何优化性能？
A: 实现请求缓存、防抖、节流等优化策略。
