"""
查询API测试
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch
from app.main import app

client = TestClient(app)

class TestQueryAPI:
    """查询API测试类"""
    
    @pytest.fixture
    def auth_headers(self):
        """获取认证头"""
        response = client.post(
            "/api/v1/auth/login",
            json={"password": "TT2024"}
        )
        token = response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    
    def test_filter_data_valid_date(self, auth_headers):
        """测试有效日期筛选"""
        with patch('app.services.data_service.data_service.get_filtered_data') as mock_service:
            mock_service.return_value = {
                "results": [{"id": 1, "name": "测试数据"}],
                "columns": ["id", "name"],
                "total": 1
            }
            
            response = client.get(
                "/api/v1/query/filter_data",
                params={"date": "2025-01-15"},
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["total"] == 1
            assert len(data["results"]) == 1
            mock_service.assert_called_once_with("2025-01-15")
    
    def test_filter_data_invalid_date_format(self, auth_headers):
        """测试无效日期格式"""
        response = client.get(
            "/api/v1/query/filter_data",
            params={"date": "invalid-date"},
            headers=auth_headers
        )
        # 这里应该返回422验证错误，但我们的API目前没有严格验证
        # 实际行为取决于外部API的响应
        assert response.status_code in [400, 422, 500]
    
    def test_filter_data_missing_date(self, auth_headers):
        """测试缺少日期参数"""
        response = client.get(
            "/api/v1/query/filter_data",
            headers=auth_headers
        )
        assert response.status_code == 422  # Validation error
    
    def test_filter_data_without_auth(self):
        """测试未认证访问"""
        response = client.get(
            "/api/v1/query/filter_data",
            params={"date": "2025-01-15"}
        )
        assert response.status_code == 403
    
    def test_customer_orders_valid_name(self, auth_headers):
        """测试有效客户名查询"""
        with patch('app.services.data_service.data_service.get_orders_by_customer') as mock_service:
            mock_service.return_value = {
                "results": [{"order_id": "123", "customer": "张三"}],
                "columns": ["order_id", "customer"],
                "total": 1
            }
            
            response = client.get(
                "/api/v1/query/filter_orders_by_customer_name",
                params={"customer_name": "张三"},
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["total"] == 1
            mock_service.assert_called_once_with("张三")
    
    def test_customer_orders_empty_name(self, auth_headers):
        """测试空客户名"""
        response = client.get(
            "/api/v1/query/filter_orders_by_customer_name",
            params={"customer_name": ""},
            headers=auth_headers
        )
        # 应该返回验证错误或空结果
        assert response.status_code in [400, 422]
    
    def test_overdue_orders_default_params(self, auth_headers):
        """测试逾期订单查询默认参数"""
        with patch('app.services.data_service.data_service.get_overdue_orders') as mock_service:
            mock_service.return_value = {
                "results": [{"order_id": "456", "overdue_days": 30}],
                "columns": ["order_id", "overdue_days"],
                "pagination": {
                    "page": 1,
                    "limit": 10,
                    "total": 1,
                    "pages": 1
                }
            }
            
            response = client.get(
                "/api/v1/query/overdue",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "pagination" in data
            assert data["pagination"]["page"] == 1
            mock_service.assert_called_once_with(
                page=1, limit=10, force_refresh=False, search_query=""
            )
    
    def test_overdue_orders_custom_params(self, auth_headers):
        """测试逾期订单查询自定义参数"""
        with patch('app.services.data_service.data_service.get_overdue_orders') as mock_service:
            mock_service.return_value = {
                "results": [],
                "columns": [],
                "pagination": {
                    "page": 2,
                    "limit": 5,
                    "total": 0,
                    "pages": 0
                }
            }
            
            response = client.get(
                "/api/v1/query/overdue",
                params={
                    "page": 2,
                    "limit": 5,
                    "force_refresh": True,
                    "search_query": "测试"
                },
                headers=auth_headers
            )
            
            assert response.status_code == 200
            mock_service.assert_called_once_with(
                page=2, limit=5, force_refresh=True, search_query="测试"
            )
    
    def test_overdue_orders_invalid_pagination(self, auth_headers):
        """测试无效分页参数"""
        response = client.get(
            "/api/v1/query/overdue",
            params={"page": 0, "limit": 101},  # 无效的页码和限制
            headers=auth_headers
        )
        assert response.status_code == 422  # Validation error
    
    def test_compatibility_routes(self, auth_headers):
        """测试兼容性路由"""
        with patch('app.services.data_service.data_service.get_filtered_data') as mock_filter:
            mock_filter.return_value = {"results": [], "columns": [], "total": 0}
            
            # 测试兼容性筛选路由
            response = client.get(
                "/api/v1/query/filter",
                params={"date": "2025-01-15"},
                headers=auth_headers
            )
            assert response.status_code == 200
        
        with patch('app.services.data_service.data_service.get_orders_by_customer') as mock_customer:
            mock_customer.return_value = {"results": [], "columns": [], "total": 0}
            
            # 测试兼容性客户路由
            response = client.get(
                "/api/v1/query/customer",
                params={"name": "张三"},
                headers=auth_headers
            )
            assert response.status_code == 200
    
    def test_service_error_handling(self, auth_headers):
        """测试服务错误处理"""
        with patch('app.services.data_service.data_service.get_filtered_data') as mock_service:
            mock_service.return_value = {"error": "外部API错误"}
            
            response = client.get(
                "/api/v1/query/filter_data",
                params={"date": "2025-01-15"},
                headers=auth_headers
            )
            
            assert response.status_code == 400
            assert "外部API错误" in response.json()["detail"]
    
    def test_service_exception_handling(self, auth_headers):
        """测试服务异常处理"""
        with patch('app.services.data_service.data_service.get_filtered_data') as mock_service:
            mock_service.side_effect = Exception("服务异常")
            
            response = client.get(
                "/api/v1/query/filter_data",
                params={"date": "2025-01-15"},
                headers=auth_headers
            )
            
            assert response.status_code == 500
            assert "服务器内部错误" in response.json()["detail"]
