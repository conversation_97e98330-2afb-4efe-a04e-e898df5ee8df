from flask import current_app
import logging
from app.utils.api_client import make_api_request
from app import cache
import datetime

logger = logging.getLogger(__name__)


def get_cache_timeout(endpoint):
    """
    获取指定端点的缓存超时时间
    
    Args:
        endpoint: API端点名称
        
    Returns:
        int: 缓存超时时间（秒）
    """
    timeouts = current_app.config.get('CACHE_TIMEOUTS', {})
    default_timeout = current_app.config.get('CACHE_DEFAULT_TIMEOUT', 300)
    return timeouts.get(endpoint, default_timeout)


@cache.memoize(timeout=300)
def get_filtered_data(date):
    """
    按日期获取筛选数据
    
    Args:
        date: 筛选日期
    
    Returns:
        筛选结果列表
    """
    logger.info(f"获取筛选数据，日期: {date}")
    
    # 尝试使用最可能的API调用格式
    params = {'date': date}
    
    # 使用与新文档一致的API端点
    data = make_api_request('GET', 'filter_data_db', params)
    
    # 检查错误
    if 'error' in data:
        logger.error(f"获取筛选数据时出错: {data['error']}")
        return {'error': data['error'], 'results': []}
    
    # 日志记录解析前的数据结构
    logger.info(f"筛选API返回数据类型: {type(data)}")
    if isinstance(data, dict):
        logger.info(f"筛选API返回数据键: {data.keys()}")
        for key in data.keys():
            value = data[key]
            value_type = type(value)
            logger.info(f"键'{key}'对应的值类型: {value_type}")
            if isinstance(value, list) and value:
                logger.info(f"键'{key}'内的第一个项目类型: {type(value[0])}")
                if isinstance(value[0], dict):
                    logger.info(f"键'{key}'内的第一个项目键: {value[0].keys()}")
                elif isinstance(value[0], list):
                    logger.info(f"键'{key}'内的第一个项目(列表)长度: {len(value[0])}")
    elif isinstance(data, list):
        logger.info(f"筛选API返回数据列表长度: {len(data)}")
        if data:
            first_item = data[0]
            logger.info(f"列表中第一个项目的类型: {type(first_item)}")
            if isinstance(first_item, dict):
                logger.info(f"列表中第一个项目的键: {first_item.keys()}")
            elif isinstance(first_item, list):
                logger.info(f"列表中第一个项目(列表)的长度: {len(first_item)}")
    
    processed_data = process_filter_data(data)
    logger.info(f"处理后的数据结构: {processed_data.keys() if isinstance(processed_data, dict) else type(processed_data)}")
    if 'results' in processed_data:
        results = processed_data['results']
        logger.info(f"结果列表长度: {len(results)}")
        if results:
            logger.info(f"第一个结果项类型: {type(results[0])}")
            if isinstance(results[0], dict):
                logger.info(f"第一个结果项键: {results[0].keys()}")
                
                # 确保结果每一项都是标准格式
                if not all(isinstance(item, dict) for item in results):
                    logger.warning(f"结果中存在非字典类型项，进行修复")
                    processed_data['results'] = [
                        item if isinstance(item, dict) else {'数据': str(item)}
                        for item in results
                    ]
            else:
                logger.warning(f"结果列表包含非字典项，尝试转换为字典")
                # 将非字典项转换为字典
                columns = processed_data.get('columns', ['数据'])
                if len(columns) == 1:
                    processed_data['results'] = [
                        {columns[0]: str(item)} for item in results
                    ]
                    logger.info(f"转换后结果数量: {len(processed_data['results'])}")
    
    return processed_data


@cache.memoize(timeout=3600)  # 缓存1小时，增加缓存时间减少API调用频率
def get_overdue_orders(page=1, limit=10, force_refresh=False, search_query=''):
    """
    获取逾期订单数据，支持本地分页
    
    Args:
        page: 页码，从1开始
        limit: 每页数据量，默认10条
        force_refresh: 是否强制刷新缓存
        search_query: 搜索关键词，会在所有字段中搜索包含该关键词的记录
    
    Returns:
        逾期订单列表，带分页信息
    """
    logger.info(f"获取逾期订单数据，页码: {page}, 每页数量: {limit}, 强制刷新: {force_refresh}, 搜索关键词: {search_query}")
    
    # 缓存键名
    cache_key = "overdue_data_full"
    last_update_key = "overdue_data_last_update"
    
    # 如果强制刷新，则删除缓存
    if force_refresh:
        cache.delete(cache_key)
        cache.delete(last_update_key)
        logger.info("强制刷新，已清除逾期订单缓存")
    
    # 获取上次更新时间
    last_update = cache.get(last_update_key)
    current_time = datetime.datetime.now()
    
    # 检查是否有完整缓存数据
    full_data = cache.get(cache_key)
    
    # 如果有缓存数据并且更新时间在1小时内，从缓存提取分页数据
    if full_data and last_update and (current_time - last_update).total_seconds() < 3600:
        logger.info(f"从缓存获取完整逾期订单数据，最后更新时间: {last_update}")
        return paginate_data(full_data, page, limit, search_query)
    
    # 如果没有缓存数据或数据已过期，从API获取新数据
    try:
        # 获取全部数据 - 明确设置limit为非常大的值，确保获取所有数据
        logger.info("从API获取完整逾期订单数据")
        api_params = {
            'api_key': current_app.config.get('API_KEY'),
            'limit': 100000  # 设置一个足够大的值，确保获取全部数据
        }
        api_data = make_api_request('GET', 'filter_overdue_orders_db', api_params)
        
        # 检查错误
        if 'error' in api_data:
            error_msg = f"获取逾期订单数据时出错: {api_data['error']}"
            logger.error(error_msg)
            # 如果有旧缓存数据，仍然返回旧数据
            if full_data:
                logger.warning("API请求失败，使用旧缓存数据")
                return paginate_data(full_data, page, limit, search_query)
            return {'error': error_msg, 'results': [], 'pagination': {'page': page, 'limit': limit, 'total': 0, 'pages': 0}}
        
        # 处理数据
        processed_data = process_overdue_data(api_data)
        
        # 更新缓存
        cache.set(cache_key, processed_data, timeout=3600)  # 1小时缓存
        cache.set(last_update_key, current_time, timeout=3600)
        logger.info(f"已更新逾期订单缓存，总记录数: {len(processed_data.get('results', []))}")
        
        # 返回分页数据
        return paginate_data(processed_data, page, limit, search_query)
        
    except Exception as e:
        error_msg = f"获取逾期订单数据时发生异常: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        # 如果有旧缓存数据，仍然返回旧数据
        if full_data:
            logger.warning("发生异常，使用旧缓存数据")
            return paginate_data(full_data, page, limit, search_query)
            
        return {'error': error_msg, 'results': [], 'pagination': {'page': page, 'limit': limit, 'total': 0, 'pages': 0}}

def filter_data(data, search_query=''):
    """
    对数据进行过滤处理，不包含分页逻辑，适用于导出功能
    
    Args:
        data: 完整数据集
        search_query: 搜索关键词，会在所有字段中搜索包含该关键词的记录
        
    Returns:
        过滤后的完整数据（不分页）
    """
    # 提取结果和列信息
    if isinstance(data, dict) and 'results' in data:
        all_results = data['results']
        columns = data.get('columns', [])
    else:
        # 如果数据格式不符合预期，尝试修复
        if isinstance(data, list):
            all_results = data
            columns = list(data[0].keys()) if data and isinstance(data[0], dict) else []
        else:
            logger.error(f"数据格式不符合预期: {type(data)}")
            return {'error': '数据格式错误', 'results': [], 'columns': []}
    
    # 如果有搜索关键词，进行数据过滤
    if search_query and search_query.strip():
        search_query = search_query.strip().lower()
        logger.info(f"执行搜索过滤，关键词: {search_query}")
        
        filtered_results = []
        for item in all_results:
            if isinstance(item, dict):
                # 在所有字段中搜索关键词
                item_matched = False
                for field, value in item.items():
                    # 将各种类型的值转换为字符串再搜索
                    str_value = str(value).lower() if value is not None else ""
                    if search_query in str_value:
                        item_matched = True
                        break
                if item_matched:
                    filtered_results.append(item)
        
        logger.info(f"搜索过滤结果: 原始数据量={len(all_results)}, 过滤后数据量={len(filtered_results)}")
        all_results = filtered_results
    
    # 获取缓存更新信息
    last_update = cache.get("overdue_data_last_update")
    next_update = None
    
    if last_update:
        try:
            next_update = last_update + datetime.timedelta(hours=1)
        except Exception as e:
            logger.error(f"计算缓存到期时间发生错误: {str(e)}")
    
    # 构造返回结果 - 返回全部过滤后的数据，不分页
    return {
        'results': all_results,
        'columns': columns,
        'total': len(all_results),
        'cache_info': {
            'last_update': last_update,
            'next_update': next_update
        }
    }

def paginate_data(data, page=1, limit=10, search_query=''):
    """
    对数据进行本地分页处理
    
    Args:
        data: 完整数据集
        page: 页码，从1开始
        limit: 每页数据量，默认10条
        search_query: 搜索关键词，会在所有字段中搜索包含该关键词的记录
        
    Returns:
        分页后的数据
    """
    # 确保页码和每页数量为有效整数
    try:
        page = max(1, int(page))
    except (TypeError, ValueError):
        logger.warning(f"无效的页码参数: {page}，使用默认值 1")
        page = 1
        
    try:
        # 每页数量可选值为 10、25、50、100、200
        limit = int(limit)
        if limit not in [10, 25, 50, 100, 200]:
            limit = 10  # 默认值
    except (TypeError, ValueError):
        logger.warning(f"无效的每页数量参数: {limit}，使用默认值 10")
        limit = 10
    
    logger.info(f"分页处理: 页码={page}, 每页数量={limit}, 搜索关键词={search_query}")
    
    # 提取结果和列信息
    if isinstance(data, dict) and 'results' in data:
        all_results = data['results']
        columns = data.get('columns', [])
    else:
        # 如果数据格式不符合预期，尝试修复
        if isinstance(data, list):
            all_results = data
            columns = list(data[0].keys()) if data and isinstance(data[0], dict) else []
        else:
            logger.error(f"数据格式不符合预期: {type(data)}")
            return {'error': '数据格式错误', 'results': [], 'columns': [], 'pagination': {'page': page, 'limit': limit, 'total': 0, 'pages': 0}}
    
    # 如果有搜索关键词，先进行数据过滤
    if search_query and search_query.strip():
        search_query = search_query.strip().lower()
        logger.info(f"执行搜索过滤，关键词: {search_query}")
        
        filtered_results = []
        for item in all_results:
            if isinstance(item, dict):
                # 在所有字段中搜索关键词
                item_matched = False
                for field, value in item.items():
                    # 将各种类型的值转换为字符串再搜索
                    str_value = str(value).lower() if value is not None else ""
                    if search_query in str_value:
                        item_matched = True
                        break
                if item_matched:
                    filtered_results.append(item)
            
        logger.info(f"搜索过滤结果: 原始数据量={len(all_results)}, 过滤后数据量={len(filtered_results)}")
        all_results = filtered_results
    
    # 计算分页信息
    total_count = len(all_results)
    total_pages = max(1, (total_count + limit - 1) // limit) if limit > 0 else 1
    
    # 检查页码是否超出范围
    if page > total_pages:
        page = total_pages
        logger.warning(f"页码超出范围，调整为最后一页: {page}")
    
    # 计算当前页的数据范围
    start_idx = (page - 1) * limit
    end_idx = min(start_idx + limit, total_count)
    
    # 确保起始索引合法
    if start_idx >= total_count:
        start_idx = 0
        end_idx = min(limit, total_count)
        page = 1
        logger.warning("起始索引超出数据范围，重置为第一页")
    
    # 提取当前页数据
    page_results = all_results[start_idx:end_idx]
    
    # 检查分页结果
    if not page_results and total_count > 0:
        logger.warning(f"分页后的数据为空，可能是分页计算错误。起始索引={start_idx}, 结束索引={end_idx}, 总记录数={total_count}")
    else:
        logger.info(f"分页数据获取成功: 当前页={page}, 每页数量={limit}, 起始索引={start_idx}, 结束索引={end_idx}, 当前页数据条数={len(page_results)}, 总记录数={total_count}")
    
    # 获取缓存更新信息
    last_update = cache.get("overdue_data_last_update")
    next_update = None
    
    if last_update:
        try:
            next_update = last_update + datetime.timedelta(hours=1)
        except Exception as e:
            logger.error(f"计算缓存到期时间发生错误: {str(e)}")
    
    # 构造返回结果
    return {
        'results': page_results,
        'columns': columns,
        'pagination': {
            'page': page,
            'limit': limit,
            'total': total_count,
            'pages': total_pages
        },
        'cache_info': {
            'last_update': last_update,
            'next_update': next_update
        }
    }


@cache.memoize(timeout=600)  # 缓存10分钟
def get_orders_by_customer(customer_name):
    """
    获取客户订单数据
    
    Args:
        customer_name: 客户姓名
    
    Returns:
        客户订单列表
    """
    logger.info(f"获取客户订单数据，客户: {customer_name}")
    
    # 使用与新文档一致的API端点和参数
    params = {'customer_name': customer_name}
    
    data = make_api_request('GET', 'filter_orders_by_customer_name_db', params)
    
    # 检查错误
    if 'error' in data:
        logger.error(f"获取客户订单数据时出错: {data['error']}")
        return {'error': data['error'], 'results': []}
    
    return process_customer_data(data)


@cache.memoize(timeout=600)  # 缓存10分钟
def get_customer_summary(customer_name):
    """
    获取客户订单汇总数据
    
    Args:
        customer_name: 客户姓名
    
    Returns:
        客户订单汇总数据
    """
    logger.info(f"开始获取客户汇总数据，客户名: {customer_name}")
    
    # 使用与新文档一致的API端点模式
    params = {
        'customer_name': customer_name,
        'api_key': current_app.config.get('API_KEY')
    }
    
    # 首先尝试与文档一致的API端点
    logger.info(f"尝试调用customer_summary_db API，参数: {params}")
    data = make_api_request('GET', 'customer_summary_db', params)
    
    # 记录API返回的原始数据特征
    if data:
        data_type = type(data).__name__
        if isinstance(data, dict):
            logger.info(f"API返回字典数据，键名: {list(data.keys())}, 数据类型: {data_type}")
        elif isinstance(data, list):
            logger.info(f"API返回列表数据，长度: {len(data)}, 数据类型: {data_type}")
            if len(data) > 0:
                first_item = data[0]
                if isinstance(first_item, dict):
                    logger.info(f"第一项数据类型: {type(first_item).__name__}, 键名: {list(first_item.keys())}")
                else:
                    logger.info(f"第一项数据类型: {type(first_item).__name__}")
    else:
        logger.error("API返回空数据")
    
    # 检查错误
    if isinstance(data, dict) and 'error' in data:
        logger.error(f"获取客户订单汇总数据时出错: {data['error']}")
        # 尝试使用备用参数名称
        params = {
            'customer': customer_name,  # 有些API可能使用'customer'而不是'customer_name'
            'api_key': current_app.config.get('API_KEY')
        }
        logger.info("尝试使用备用参数名称重新请求")
        data = make_api_request('GET', 'customer_summary_db', params)
        
        if isinstance(data, dict) and 'error' in data:
            logger.error(f"使用备用参数仍然出错: {data['error']}")
            return {'error': data['error']}
    
    # 处理从API获取的数据
    try:
        logger.info("开始处理API返回的数据")
        processed_data = process_customer_summary(data)
        
        # 再次检查处理后的数据是否有错误
        if isinstance(processed_data, dict) and 'error' in processed_data:
            logger.error(f"处理客户汇总数据时出错: {processed_data['error']}")
            # 尝试使用新的客户汇总格式处理
            if isinstance(data, list) and len(data) > 0 and isinstance(data[0], dict):
                logger.info("检测到可能是新格式的数据，尝试使用新格式处理函数")
                processed_data = process_new_customer_summary_format(data)
                logger.info(f"新格式处理函数返回结果: {list(processed_data.keys()) if isinstance(processed_data, dict) else type(processed_data)}")
            return processed_data
        
        logger.info(f"成功获取并处理了客户汇总数据, 客户名: {customer_name}")
        return processed_data
    except Exception as e:
        logger.error(f"处理客户汇总数据时发生异常: {str(e)}")
        logger.exception("处理异常详情:")
        return {'error': f"处理数据时发生错误: {str(e)}"}


@cache.memoize(timeout=1800)  # 缓存30分钟
def get_order_summary(start_date, end_date):
    """
    获取订单汇总数据
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
    
    Returns:
        订单汇总数据
    """
    logger.info(f"获取订单汇总数据, 开始: {start_date}, 结束: {end_date}")
    
    # 使用与新文档一致的API端点
    params = {
        'start_date': start_date, 
        'end_date': end_date,
        'api_key': current_app.config.get('API_KEY')
    }
    
    # 首先尝试文档中的summary_data_db端点
    data = make_api_request('GET', 'summary_data_db', params)
    
    # 检查错误
    if 'error' in data:
        logger.error(f"获取订单汇总数据时出错: {data['error']}")
        return {'error': data['error'], 'results': []}
    
    # 增强数据处理逻辑
    logger.info(f"订单汇总数据类型: {type(data)}")
    if isinstance(data, list) and len(data) > 0:
        # 处理列表格式数据
        logger.info(f"接收到订单汇总列表数据，长度: {len(data)}")
        processed_data = []
        
        # 不同的数据结构处理方式
        if all(isinstance(item, dict) for item in data):
            # 字典列表，确保每个字典都有必要的字段
            for item in data:
                processed_item = {}
                # 确保必要字段存在
                processed_item['month'] = item.get('month', item.get('date', item.get('period', '未知')))
                processed_item['ecommerce_count'] = int(item.get('ecommerce_count', item.get('e_commerce', item.get('e_count', 0))))
                processed_item['leasing_count'] = int(item.get('leasing_count', item.get('lease', item.get('l_count', 0))))
                processed_data.append(processed_item)
        elif all(isinstance(item, list) for item in data):
            # 可能是二维数组
            headers = ['month', 'ecommerce_count', 'leasing_count']
            for row in data:
                if len(row) >= 3:  # 至少需要3个元素
                    processed_item = {
                        'month': row[0],
                        'ecommerce_count': int(row[1]) if str(row[1]).isdigit() else 0,
                        'leasing_count': int(row[2]) if str(row[2]).isdigit() else 0
                    }
                    processed_data.append(processed_item)
        
        return {'results': processed_data}
    
    elif isinstance(data, dict):
        # 处理字典格式数据
        if 'data' in data and isinstance(data['data'], list):
            # 尝试从data字段获取数据
            return get_order_summary_from_list(data['data'])
        
        if 'results' in data and isinstance(data['results'], list):
            # 尝试从results字段获取数据
            return get_order_summary_from_list(data['results'])
            
        if 'summary' in data and isinstance(data['summary'], (list, dict)):
            # 尝试从summary字段获取数据
            summary_data = data['summary']
            if isinstance(summary_data, list):
                return get_order_summary_from_list(summary_data)
            else:  # 字典情况
                if 'orders' in summary_data and isinstance(summary_data['orders'], list):
                    return get_order_summary_from_list(summary_data['orders'])
        
        # 如果没有找到合适的数据结构，尝试直接构建
        monthly_data = []
        for key, value in data.items():
            if isinstance(value, dict) and 'ecommerce_count' in value and 'leasing_count' in value:
                item = {
                    'month': key,
                    'ecommerce_count': int(value['ecommerce_count']),
                    'leasing_count': int(value['leasing_count'])
                }
                monthly_data.append(item)
        
        if monthly_data:
            return {'results': monthly_data}
    
    # 如果没有找到有效数据，返回空结果
    logger.warning("无法从API响应中提取订单汇总数据")
    return {'results': [], 'error': "无法解析API返回的数据格式"}


@cache.memoize(timeout=1800)  # 缓存30分钟
def get_overdue_summary(start_date, end_date):
    """
    获取逾期订单汇总数据
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
    
    Returns:
        逾期订单汇总数据
    """
    logger.info(f"获取逾期订单汇总数据, 开始: {start_date}, 结束: {end_date}")
    
    # 使用与新文档一致的API端点
    params = {
        'start_date': start_date, 
        'end_date': end_date,
        'api_key': current_app.config.get('API_KEY')
    }
    
    # 首先尝试 overdue_summary_db 端点
    data = make_api_request('GET', 'overdue_summary_db', params)
    
    # 检查错误
    if 'error' in data:
        logger.error(f"获取逾期订单汇总数据时出错: {data['error']}")
        return {'error': data['error'], 'results': []}
    
    # 增强数据处理逻辑
    logger.info(f"逾期汇总数据类型: {type(data)}")
    if isinstance(data, list) and len(data) > 0:
        # 处理列表格式数据
        logger.info(f"接收到逾期汇总列表数据，长度: {len(data)}")
        processed_data = []
        
        # 不同的数据结构处理方式
        if all(isinstance(item, dict) for item in data):
            # 字典列表，确保每个字典都有必要的字段
            for item in data:
                processed_item = {}
                # 确保必要字段存在
                processed_item['month'] = item.get('month', item.get('date', item.get('period', '未知')))
                processed_item['overdue_count'] = int(item.get('overdue_count', item.get('count', 0)))
                processed_item['overdue_amount'] = float(item.get('overdue_amount', item.get('amount', 0)))
                processed_item['overdue_rate'] = float(item.get('overdue_rate', item.get('rate', 0)))
                processed_data.append(processed_item)
        elif all(isinstance(item, list) for item in data):
            # 可能是二维数组
            for row in data:
                if len(row) >= 4:  # 至少需要4个元素
                    processed_item = {
                        'month': row[0],
                        'overdue_count': int(row[1]) if str(row[1]).isdigit() else 0,
                        'overdue_amount': float(row[2]) if str(row[2]).replace('.', '', 1).isdigit() else 0,
                        'overdue_rate': float(row[3]) if str(row[3]).replace('.', '', 1).isdigit() else 0
                    }
                    processed_data.append(processed_item)
        
        return {'results': processed_data}
    
    elif isinstance(data, dict):
        # 处理字典格式数据
        if 'data' in data and isinstance(data['data'], list):
            # 尝试从data字段获取数据
            return get_overdue_summary_from_list(data['data'])
        
        if 'results' in data and isinstance(data['results'], list):
            # 尝试从results字段获取数据
            return get_overdue_summary_from_list(data['results'])
            
        if 'summary' in data and isinstance(data['summary'], (list, dict)):
            # 尝试从summary字段获取数据
            summary_data = data['summary']
            if isinstance(summary_data, list):
                return get_overdue_summary_from_list(summary_data)
            else:  # 字典情况
                if 'overdue' in summary_data and isinstance(summary_data['overdue'], list):
                    return get_overdue_summary_from_list(summary_data['overdue'])
        
        # 如果没有找到合适的数据结构，尝试直接构建
        monthly_data = []
        for key, value in data.items():
            if isinstance(value, dict) and ('overdue_count' in value or 'count' in value):
                item = {
                    'month': key,
                    'overdue_count': int(value.get('overdue_count', value.get('count', 0))),
                    'overdue_amount': float(value.get('overdue_amount', value.get('amount', 0))),
                    'overdue_rate': float(value.get('overdue_rate', value.get('rate', 0)))
                }
                monthly_data.append(item)
        
        if monthly_data:
            return {'results': monthly_data}
    
    # 如果没有找到有效数据，返回空结果
    logger.warning("无法从API响应中提取逾期汇总数据")
    return {'results': [], 'error': "无法解析API返回的数据格式"}


def get_order_summary_from_list(data_list):
    """从列表中提取订单汇总数据"""
    processed_data = []
    
    for item in data_list:
        if isinstance(item, dict):
            processed_item = {}
            # 确保必要字段存在
            processed_item['month'] = item.get('month', item.get('date', item.get('period', '未知')))
            processed_item['ecommerce_count'] = int(item.get('ecommerce_count', item.get('e_commerce', item.get('e_count', 0))))
            processed_item['leasing_count'] = int(item.get('leasing_count', item.get('lease', item.get('l_count', 0))))
            processed_data.append(processed_item)
        elif isinstance(item, list) and len(item) >= 3:
            # 假设列表格式为 [month, ecommerce_count, leasing_count]
            processed_item = {
                'month': item[0],
                'ecommerce_count': int(item[1]) if str(item[1]).isdigit() else 0,
                'leasing_count': int(item[2]) if str(item[2]).isdigit() else 0
            }
            processed_data.append(processed_item)
    
    return {'results': processed_data}


def get_overdue_summary_from_list(data_list):
    """从列表中提取逾期汇总数据"""
    processed_data = []
    
    for item in data_list:
        if isinstance(item, dict):
            processed_item = {}
            # 确保必要字段存在
            processed_item['month'] = item.get('month', item.get('date', item.get('period', '未知')))
            processed_item['overdue_count'] = int(item.get('overdue_count', item.get('count', 0)))
            processed_item['overdue_amount'] = float(item.get('overdue_amount', item.get('amount', 0)))
            processed_item['overdue_rate'] = float(item.get('overdue_rate', item.get('rate', 0)))
            processed_data.append(processed_item)
        elif isinstance(item, list) and len(item) >= 4:
            # 假设列表格式为 [month, count, amount, rate]
            processed_item = {
                'month': item[0],
                'overdue_count': int(item[1]) if str(item[1]).isdigit() else 0,
                'overdue_amount': float(item[2]) if str(item[2]).replace('.', '', 1).isdigit() else 0,
                'overdue_rate': float(item[3]) if str(item[3]).replace('.', '', 1).isdigit() else 0
            }
            processed_data.append(processed_item)
    
    return {'results': processed_data}


@cache.memoize(timeout=300)  # 缓存5分钟
def process_summary_data(data):
    """
    处理从summary_data API获取的原始汇总数据
    
    Args:
        data: API返回的数据
        
    Returns:
        处理后的汇总数据列表
    """
    logger.info(f"开始处理原始汇总数据: 类型={type(data)}")
    
    # 如果数据为空，返回空列表
    if not data:
        logger.warning("原始汇总数据为空")
        return []
    
    processed_results = []
    
    # 处理列表格式数据
    if isinstance(data, list):
        logger.info(f"原始汇总数据为列表，包含{len(data)}条记录")
        
        # 检查列表中的元素类型
        if data and all(isinstance(item, dict) for item in data):
            # 如果是字典列表，直接使用
            return data
        
        # 处理二维数组
        if data and isinstance(data[0], list):
            # 判断是否是带有标题行的二维数组
            if len(data) >= 2:
                headers = data[0]
                rows = data[1:]
                
                for row in rows:
                    if len(row) == len(headers):
                        row_dict = dict(zip(headers, row))
                        processed_results.append(row_dict)
                    else:
                        logger.warning(f"行数据长度与标题不匹配: headers={len(headers)}, row={len(row)}")
                        # 尝试填充或截断行数据以匹配标题长度
                        if len(row) < len(headers):
                            padded_row = row + [None] * (len(headers) - len(row))
                            row_dict = dict(zip(headers, padded_row))
                        else:
                            row_dict = dict(zip(headers, row[:len(headers)]))
                        processed_results.append(row_dict)
                
                return processed_results
    
    # 处理字典格式数据
    elif isinstance(data, dict):
        # 检查常见的包含数据的字段
        for key in ['data', 'results', 'orders', 'summary']:
            if key in data and isinstance(data[key], list):
                logger.info(f"从字典字段'{key}'中提取数据")
                return process_summary_data(data[key])  # 递归处理提取的列表
        
        # 如果数据是单个结果对象
        processed_results = [data]
    
    else:
        # 对于其他类型的数据，直接转换为字符串并包装为字典
        processed_results = [{"data": str(data)}]
    
    return processed_results


def process_filter_data(data):
    """处理筛选数据并设置自定义列顺序"""
    # 记录详细的数据处理过程
    logger.info(f"开始处理筛选数据: 数据类型={type(data)}")
    
    # 定义字段展示顺序优先级
    priority_columns = [
        "订单编号", "客户姓名", "手机号码", "客户手机", "订单日期", 
        "客服", "客服归属", "业务", "业务归属", "产品", "设备型号", "期数", "总期数", 
        "每期还款金额", "当前期数", "账单日期", "账单状态", "当前待收", "当期待收",
        "台数", "devices_count", "备注", "备注信息", "客户信息备注"
    ]
    
    # 检查是否为新的嵌套JSON格式
    if isinstance(data, dict) and 'results' in data and data['results'] and isinstance(data['results'][0], dict):
        # 检测是否包含嵌套结构（客户信息、订单信息、账单信息等）
        first_item = data['results'][0]
        if any(key in ["客户信息", "订单信息", "账单信息"] for key in first_item.keys()):
            logger.info("检测到新的嵌套JSON格式，进行扁平化处理")
            flattened_results = []
            
            for item in data['results']:
                # 使用扁平化函数处理嵌套数据
                flat_item = flatten_nested_order_data(item)
                flattened_results.append(flat_item)
            
            # 提取扁平化后的所有可能列
            all_possible_columns = set()
            for item in flattened_results:
                all_possible_columns.update(item.keys())
            
            # 对列名进行排序
            sorted_columns = []
            # 首先添加优先列
            for col in priority_columns:
                if col in all_possible_columns:
                    sorted_columns.append(col)
            # 然后添加其余列
            for col in all_possible_columns:
                if col not in sorted_columns:
                    sorted_columns.append(col)
            
            logger.info(f"扁平化后得到的列: {sorted_columns}")
            
            return {
                'results': flattened_results,
                'columns': sorted_columns
            }
    
    # 如果数据本身是字典，并且已经包含results字段
    if isinstance(data, dict) and 'results' in data:
        logger.info("筛选数据已包含results字段")
        
        # 从返回的第一个结果中提取所有字段作为列名
        if data['results'] and isinstance(data['results'][0], dict):
            # 提取实际列名
            actual_columns = list(data['results'][0].keys())
            logger.info(f"从API返回数据中提取实际列名: {actual_columns}")
            
            # 对列名进行排序，优先显示priority_columns中定义的字段，其余字段按原顺序排列
            sorted_columns = []
            
            # 首先添加优先列（按照priority_columns中的顺序）
            for col in priority_columns:
                if col in actual_columns:
                    sorted_columns.append(col)
            
            # 然后添加其余列（保持原有顺序）
            for col in actual_columns:
                if col not in sorted_columns:
                    sorted_columns.append(col)
            
            logger.info(f"排序后的列名: {sorted_columns}")
            
            return {
                'results': data['results'],
                'columns': sorted_columns
            }
        else:
            # 定义自定义列顺序
            custom_columns = [
                "订单编号", "客户姓名", "客户手机", "订单日期", 
                "产品", "期数", "总待收", "当前待收",
                "客服归属", "业务归属",
                "期数1", "期数2", "期数3", "期数4", "期数5", "期数6",
                "备注信息"
            ]
            return {
                'results': data['results'],
                'columns': custom_columns
            }
    
    # 处理结果为列表形式——将results和headers结合
    if isinstance(data, dict) and 'headers' in data and 'results' in data:
        headers = data['headers']
        results_rows = data['results']
        
        # 将列表结果转换为字典列表，以匹配Flask模板所需
        if results_rows and isinstance(results_rows[0], list):
            dict_results = []
            for row in results_rows:
                # 确保列表长度一致
                if len(headers) == len(row):
                    dict_results.append(dict(zip(headers, row)))
                else:
                    # 长度不一致时进行补充或截断处理
                    logger.warning(f"行数据和headers长度不匹配: headers={len(headers)}, row={len(row)}")
                    if len(row) < len(headers):
                        # 数据不足，用空值补充
                        padded_row = row + [''] * (len(headers) - len(row))
                        dict_results.append(dict(zip(headers, padded_row)))
                    else:
                        # 数据超出，进行截断
                        dict_results.append(dict(zip(headers, row[:len(headers)])))
            
            logger.info(f"转换后的结果数量: {len(dict_results)}")
            
            # 对headers进行排序
            sorted_headers = []
            # 首先添加优先列
            for col in priority_columns:
                if col in headers:
                    sorted_headers.append(col)
            # 然后添加其余列
            for col in headers:
                if col not in sorted_headers:
                    sorted_headers.append(col)
                    
            return {
                'results': dict_results,
                'columns': sorted_headers
            }
    
    # 如果数据是列表，将其包装到results字段中
    if isinstance(data, list):
        logger.info(f"筛选数据是列表，包含{len(data)}条记录")
        if data and isinstance(data[0], dict):
            # 如果是字典列表，提取实际列名并排序
            actual_columns = list(data[0].keys())
            logger.info(f"从列表数据中提取实际列名: {actual_columns}")
            
            # 对列名进行排序
            sorted_columns = []
            # 首先添加优先列
            for col in priority_columns:
                if col in actual_columns:
                    sorted_columns.append(col)
            # 然后添加其余列
            for col in actual_columns:
                if col not in sorted_columns:
                    sorted_columns.append(col)
                    
            return {
                'results': data,
                'columns': sorted_columns
            }
        elif data and isinstance(data[0], list) and len(data) >= 2:
            # 可能是二维数组格式，第一行作为字段名
            headers = data[0]
            rows = data[1:]
            dict_results = []
            for row in rows:
                if len(headers) == len(row):
                    dict_results.append(dict(zip(headers, row)))
                else:
                    logger.warning(f"行数据和headers长度不匹配: headers={len(headers)}, row={len(row)}")
                    if len(row) < len(headers):
                        padded_row = row + [''] * (len(headers) - len(row))
                        dict_results.append(dict(zip(headers, padded_row)))
                    else:
                        dict_results.append(dict(zip(headers, row[:len(headers)])))
            
            # 对headers进行排序        
            sorted_headers = []
            # 首先添加优先列
            for col in priority_columns:
                if col in headers:
                    sorted_headers.append(col)
            # 然后添加其余列
            for col in headers:
                if col not in sorted_headers:
                    sorted_headers.append(col)
                    
            return {
                'results': dict_results,
                'columns': sorted_headers
            }
        else:
            # 未知列表格式，使用默认列顺序
            custom_columns = [
                "订单编号", "客户姓名", "客户手机", "订单日期", 
                "客服归属", "业务归属", "产品", "期数", 
                "每期还款金额", "当前期数", "账单日期", "台数", "备注", 
                "备注信息"
            ]
            logger.warning("未知列表格式，使用默认列顺序")
            return {
                'results': data,
                'columns': custom_columns
            }
    
    # 如果是其他格式，尝试提取可能的数据结构
    if isinstance(data, dict):
        for key in ['data', 'items', 'records', 'list']:
            if key in data and isinstance(data[key], list):
                logger.info(f"从键'{key}'中提取列表数据，包含{len(data[key])}条记录")
                extracted_data = data[key]
                
                # 从第一条数据中提取列名
                if extracted_data and isinstance(extracted_data[0], dict):
                    actual_columns = list(extracted_data[0].keys())
                    logger.info(f"从提取的数据中获取实际列名: {actual_columns}")
                    
                    # 对列名进行排序
                    sorted_columns = []
                    # 首先添加优先列
                    for col in priority_columns:
                        if col in actual_columns:
                            sorted_columns.append(col)
                    # 然后添加其余列
                    for col in actual_columns:
                        if col not in sorted_columns:
                            sorted_columns.append(col)
                            
                    return {
                        'results': extracted_data,
                        'columns': sorted_columns
                    }
                else:
                    # 使用默认列顺序
                    custom_columns = [
                        "订单编号", "客户姓名", "客户手机", "订单日期", 
                        "客服归属", "业务归属", "产品", "期数", 
                        "每期还款金额", "当前期数", "账单日期", "台数", "备注", 
                        "备注信息"
                    ]
                    return {
                        'results': extracted_data,
                        'columns': custom_columns
                    }
    
    # 无法解析数据，返回空结果
    logger.warning("无法解析筛选数据，返回空结果")
    custom_columns = [
        "订单编号", "客户姓名", "客户手机", "订单日期", 
        "客服归属", "业务归属", "产品", "期数", 
        "每期还款金额", "当前期数", "账单日期", "台数", "备注", 
        "备注信息"
    ]
    return {
        'results': [],
        'columns': custom_columns
    }


def process_overdue_data(data):
    """处理逾期订单数据并设置自定义列顺序"""
    logger.info(f"开始处理逾期订单数据: 数据类型={type(data)}")
    
    # 定义字段展示顺序优先级
    priority_columns = [
        "订单编号", "客户姓名", "贷后状态", "手机号码", "业务", "客服",
        "产品", "产品类型", "型号", "台数", "成本", 
        "期数", "订单日期", "总待收", "当前待收",
        "逾期天数", "逾期期数", "逾期金额", "首次逾期日期", "首次逾期期数",
        "账单日期", "还款周期", "备注", "客户信息备注"
    ]
    
    # 创建字段映射表，用于标准化字段名称
    field_mapping = {
        "业务": "业务",
        "业务归属": "业务",
        "业务人员": "业务",
        "客户信息备注": "客户信息备注",
        "客户备注": "客户信息备注",
        "备注": "备注",
        "备注信息": "备注",
        "客服": "客服",
        "客服人员": "客服",
        "客服归属": "客服",
        "手机号码": "手机号码",
        "手机号": "手机号码",
        "联系电话": "手机号码",
        "电话": "手机号码"
    }
    
    # 处理函数：用于处理每个订单项，确保客户信息字段正确展平
    def process_item(item):
        processed_item = {}
        
        if isinstance(item, dict):
            # 先处理非嵌套字段
            for key, value in item.items():
                if key == "客户信息" and isinstance(value, dict):
                    # 简化日志记录
                    # logger.info(f"处理客户信息嵌套字段: 包含键 {list(value.keys())}")
                    
                    # 特殊处理客户信息嵌套字段
                    for info_key, info_value in value.items():
                        # 使用映射名称或原始名称
                        processed_key = field_mapping.get(info_key, info_key)
                        processed_item[processed_key] = info_value
                else:
                    # 对于非客户信息嵌套字段，也进行字段映射标准化
                    processed_key = field_mapping.get(key, key)
                    processed_item[processed_key] = value
                    
                    # 特殊处理：将账单状态映射为贷后状态
                    if key == "账单状态":
                        processed_item["贷后状态"] = value
        else:
            # 非字典类型，直接添加
            processed_item = item
            
        # 确保优先字段存在，即使原始数据中没有
        for priority_field in ["业务", "客户信息备注", "客服", "手机号码"]:
            if priority_field not in processed_item:
                # 尝试从嵌套结构中找到对应字段
                if isinstance(item, dict) and "客户信息" in item and isinstance(item["客户信息"], dict):
                    for info_key in item["客户信息"]:
                        mapped_key = field_mapping.get(info_key, info_key)
                        if mapped_key == priority_field:
                            processed_item[priority_field] = item["客户信息"][info_key]
                            # 移除过度的日志记录
                            # logger.info(f"从客户信息中提取到缺失的优先字段: {priority_field} = {item['客户信息'][info_key]}")
        
        return processed_item
    
    # 如果数据本身是字典，并且已经包含results字段
    if isinstance(data, dict) and 'results' in data:
        logger.info("逾期数据已包含results字段")
        
        # 处理每个结果项，展平嵌套结构
        processed_results = []
        for item in data['results']:
            processed_item = process_item(item)
            processed_results.append(processed_item)
        
        # 更新处理后的结果
        data['results'] = processed_results
        
        # 从返回的第一个结果中提取所有字段作为列名
        if data['results'] and isinstance(data['results'][0], dict):
            # 提取实际列名
            actual_columns = list(data['results'][0].keys())
            logger.info(f"从API返回数据中提取实际列名: {actual_columns}")
            
            # 对列名进行排序，优先显示priority_columns中定义的字段，其余字段按原顺序排列
            sorted_columns = []
            
            # 首先添加优先列（按照priority_columns中的顺序）
            for col in priority_columns:
                if col in actual_columns:
                    sorted_columns.append(col)
                    
            # 然后添加其余列（保持原有顺序）
            for col in actual_columns:
                if col not in sorted_columns:
                    sorted_columns.append(col)
            
            logger.info(f"排序后的列名: {sorted_columns}")
            
            # 确保优先字段在columns列表中，即使数据中没有
            for priority_field in ["业务", "客户信息备注", "客服", "手机号码"]:
                if priority_field not in sorted_columns:
                    sorted_columns.insert(priority_columns.index(priority_field), priority_field)
                    logger.info(f"添加缺失的优先字段到列名中: {priority_field}")
            
            return {
                'results': data['results'],
                'columns': sorted_columns
            }
        else:
            # 定义自定义列顺序
            custom_columns = [
                "订单编号", "客户姓名", "贷后状态", "手机号码", "业务", "客服", 
                "产品", "产品类型", "型号", "台数", "成本", 
                "期数", "订单日期", "总待收", "当前待收",
                "逾期天数", "逾期期数", "逾期金额", "首次逾期日期", "首次逾期期数",
                "账单日期", "还款周期", "备注", "客户信息备注"
            ]
            return {
                'results': data['results'],
                'columns': custom_columns
            }
    
    # 处理结果为列表形式——将results和headers结合
    if isinstance(data, dict) and 'headers' in data and 'results' in data:
        headers = data['headers']
        results_rows = data['results']
        
        # 将列表结果转换为字典列表，以匹配Flask模板所需
        if results_rows and isinstance(results_rows[0], list):
            dict_results = []
            for row in results_rows:
                # 确保列表长度一致
                if len(headers) == len(row):
                    dict_results.append(dict(zip(headers, row)))
                else:
                    # 长度不一致时进行补充或截断处理
                    logger.warning(f"行数据和headers长度不匹配: headers={len(headers)}, row={len(row)}")
                    if len(row) < len(headers):
                        # 数据不足，用空值补充
                        padded_row = row + [''] * (len(headers) - len(row))
                        dict_results.append(dict(zip(headers, padded_row)))
                    else:
                        # 数据超出，进行截断
                        dict_results.append(dict(zip(headers, row[:len(headers)])))
            
            logger.info(f"转换后的结果数量: {len(dict_results)}")
            
            # 对headers进行排序
            sorted_headers = []
            # 首先添加优先列
            for col in priority_columns:
                if col in headers:
                    sorted_headers.append(col)
            # 然后添加其余列
            for col in headers:
                if col not in sorted_headers:
                    sorted_headers.append(col)
                    
            return {
                'results': dict_results,
                'columns': sorted_headers
            }
    
    # 如果数据是列表，将其包装到results字段中
    if isinstance(data, list):
        # 保留简化的日志
        logger.debug(f"处理逾期数据列表，共{len(data)}条记录")
        
        # 处理每个列表项，展平嵌套结构
        processed_data = []
        for item in data:
            processed_item = process_item(item)
            processed_data.append(processed_item)
        
        # 更新处理后的数据
        data = processed_data
        
        if data and isinstance(data[0], dict):
            # 如果是字典列表，提取实际列名并排序
            actual_columns = list(data[0].keys())
            logger.info(f"从列表数据中提取实际列名: {actual_columns}")
            
            # 对列名进行排序
            sorted_columns = []
            # 首先添加优先列
            for col in priority_columns:
                if col in actual_columns:
                    sorted_columns.append(col)
            # 然后添加其余列
            for col in actual_columns:
                if col not in sorted_columns:
                    sorted_columns.append(col)
            
            # 确保优先字段在columns列表中，即使数据中没有
            for priority_field in ["业务", "客户信息备注", "客服", "手机号码"]:
                if priority_field not in sorted_columns:
                    sorted_columns.insert(priority_columns.index(priority_field), priority_field)
                    logger.info(f"添加缺失的优先字段到列名中: {priority_field}")
                    
            return {
                'results': data,
                'columns': sorted_columns
            }
        else:
            # 未知列表格式，使用默认列顺序
            custom_columns = [
                "订单编号", "客户姓名", "手机号码", "业务", "客服", 
                "产品", "产品类型", "型号", "台数", "成本", 
                "期数", "订单日期", "总待收", "当前待收",
                "逾期天数", "逾期期数", "逾期金额", "首次逾期日期", "首次逾期期数",
                "账单日期", "还款周期", "备注", "客户信息备注"
            ]
            return {
                'results': data,
                'columns': custom_columns
            }
    
    # 如果是其他格式，尝试提取可能的数据结构
    if isinstance(data, dict):
        for key in ['data', 'items', 'records', 'list']:
            if key in data and isinstance(data[key], list):
                logger.info(f"从键'{key}'中提取列表数据，包含{len(data[key])}条记录")
                extracted_data = data[key]
                
                # 处理提取的数据，展平嵌套结构
                processed_extracted = []
                for item in extracted_data:
                    processed_item = process_item(item)
                    processed_extracted.append(processed_item)
                
                # 从第一条数据中提取列名
                if processed_extracted and isinstance(processed_extracted[0], dict):
                    actual_columns = list(processed_extracted[0].keys())
                    logger.info(f"从提取的数据中获取实际列名: {actual_columns}")
                    
                    # 对列名进行排序
                    sorted_columns = []
                    # 首先添加优先列
                    for col in priority_columns:
                        if col in actual_columns:
                            sorted_columns.append(col)
                    # 然后添加其余列
                    for col in actual_columns:
                        if col not in sorted_columns:
                            sorted_columns.append(col)
                    
                    # 确保优先字段在columns列表中，即使数据中没有
                    for priority_field in ["业务", "客户信息备注", "客服", "手机号码"]:
                        if priority_field not in sorted_columns:
                            sorted_columns.insert(priority_columns.index(priority_field), priority_field)
                            logger.info(f"添加缺失的优先字段到列名中: {priority_field}")
                    
                    return {
                        'results': processed_extracted,
                        'columns': sorted_columns
                    }
                    
                # 如果提取的数据不是字典格式，使用自定义列顺序
                custom_columns = [
                    "订单编号", "客户姓名", "贷后状态", "手机号码", "业务", "客服", 
                    "产品", "产品类型", "型号", "台数", "成本", 
                    "期数", "订单日期", "总待收", "当前待收",
                    "逾期天数", "逾期期数", "逾期金额", "首次逾期日期", "首次逾期期数",
                    "账单日期", "还款周期", "备注", "客户信息备注"
                ]
                return {
                    'results': processed_extracted,
                    'columns': custom_columns
                }
    
    # 如果以上方法都无法处理数据，尝试最基本的处理方式
    logger.warning("无法识别的数据格式，使用基本处理逻辑")
    
    # 将未知格式的数据转换为字典，用于前端显示
    if isinstance(data, dict):
        # 如果是字典但没有预期的字段，包装成列表项
        processed_data = [data]
    elif isinstance(data, list):
        # 如果已经是列表，直接使用
        processed_data = data
    else:
        # 其他类型，包装为字符串
        processed_data = [{"数据": str(data)}]
    
    # 使用自定义列顺序
    custom_columns = [
        "订单编号", "客户姓名", "贷后状态", "手机号码", "业务", "客服", 
        "产品", "产品类型", "型号", "台数", "成本", 
        "期数", "订单日期", "总待收", "当前待收",
        "逾期天数", "逾期期数", "逾期金额", "首次逾期日期", "首次逾期期数",
        "账单日期", "还款周期", "备注", "客户信息备注"
    ]
    
    return {
        'results': processed_data,
        'columns': custom_columns
    }


def process_customer_data(data):
    """处理客户订单数据并设置自定义列顺序"""
    logger.info(f"开始处理客户订单数据: 数据类型={type(data)}")
    
    # 定义字段展示顺序优先级
    priority_columns = [
        "订单编号", "客户姓名", "贷后状态", "手机号码", "订单日期", "客服", "业务", "产品", 
        "期数", "台数", "当前待收", "总待收", "产品类型", "融资总额", "成本",
        "期数1", "期数2", "期数3", "期数4", "期数5", "期数6",
        "客户信息备注", "备注", "订单备注", "设备型号", "业务归属", "客服人员"
    ]
    
    # 检查是否为新的汇总JSON格式
    if isinstance(data, list) and len(data) > 0 and isinstance(data[0], dict) and "汇总数据" in data[0]:
        logger.info("检测到新的汇总JSON格式，进行转换处理")
        return process_new_customer_summary_format(data)
    
    # 如果数据本身是字典，并且已经包含results字段
    if isinstance(data, dict) and 'results' in data:
        logger.info("客户数据已包含results字段")
        
        # 处理每个结果项，添加字段映射
        processed_results = []
        for item in data['results']:
            if isinstance(item, dict):
                processed_item = item.copy()
                # 如果有账单状态字段，同时创建贷后状态字段
                if "账单状态" in processed_item:
                    processed_item["贷后状态"] = processed_item["账单状态"]
                processed_results.append(processed_item)
            else:
                processed_results.append(item)
        
        # 更新处理后的结果
        data['results'] = processed_results
        
        # 从返回的第一个结果中提取所有字段作为列名
        if data['results'] and isinstance(data['results'][0], dict):
            # 提取实际列名
            actual_columns = list(data['results'][0].keys())
            logger.info(f"从API返回数据中提取实际列名: {actual_columns}")
            
            # 对列名进行排序，优先显示priority_columns中定义的字段，其余字段按原顺序排列
            sorted_columns = []
            
            # 首先添加优先列（按照priority_columns中的顺序）
            for col in priority_columns:
                if col in actual_columns:
                    sorted_columns.append(col)
            
            # 然后添加其余列（保持原有顺序）
            for col in actual_columns:
                if col not in sorted_columns:
                    sorted_columns.append(col)
            
            logger.info(f"排序后的列名: {sorted_columns}")
            
            if data['results']:
                logger.info(f"第一条数据示例: {data['results'][0]}")
            return {
                'results': data['results'],
                'columns': sorted_columns
            }
        else:
            # 定义自定义列顺序
            custom_columns = [
                "订单编号", "客户姓名", "客户手机", "订单日期", 
                "产品", "期数", "总待收", "当前待收",
                "客服归属", "业务归属",
                "期数1", "期数2", "期数3", "期数4", "期数5", "期数6",
                "备注信息"
            ]
            if data['results']:
                logger.info(f"第一条数据示例: {data['results'][0]}")
            return {
                'results': data['results'],
                'columns': custom_columns
            }
    
    # 处理结果为列表形式——将results和headers结合
    if isinstance(data, dict) and 'headers' in data and 'results' in data:
        headers = data['headers']
        results_rows = data['results']
        logger.info(f"处理headers和results格式数据: headers={headers}, 行数={len(results_rows)}")
        
        # 将列表结果转换为字典列表，以匹配Flask模板所需
        if results_rows and isinstance(results_rows[0], list):
            dict_results = []
            for row in results_rows:
                # 确保列表长度一致
                if len(headers) == len(row):
                    dict_results.append(dict(zip(headers, row)))
                else:
                    # 长度不一致时进行补充或截断处理
                    logger.warning(f"行数据和headers长度不匹配: headers={len(headers)}, row={len(row)}")
                    if len(row) < len(headers):
                        # 数据不足，用空值补充
                        padded_row = row + [''] * (len(headers) - len(row))
                        dict_results.append(dict(zip(headers, padded_row)))
                    else:
                        # 数据超出，进行截断
                        dict_results.append(dict(zip(headers, row[:len(headers)])))
            
            logger.info(f"转换后的结果数量: {len(dict_results)}")
            
            # 对headers进行排序
            sorted_headers = []
            # 首先添加优先列
            for col in priority_columns:
                if col in headers:
                    sorted_headers.append(col)
            # 然后添加其余列
            for col in headers:
                if col not in sorted_headers:
                    sorted_headers.append(col)
                    
            if dict_results:
                logger.info(f"转换后的第一条数据示例: {dict_results[0]}")
            return {
                'results': dict_results,
                'columns': sorted_headers
            }
    
    # 如果数据是列表，将其包装到results字段中
    if isinstance(data, list):
        logger.info(f"客户数据是列表，包含{len(data)}条记录")
        
        # 处理每个列表项，添加字段映射
        processed_data = []
        for item in data:
            if isinstance(item, dict):
                processed_item = item.copy()
                # 如果有账单状态字段，同时创建贷后状态字段
                if "账单状态" in processed_item:
                    processed_item["贷后状态"] = processed_item["账单状态"]
                processed_data.append(processed_item)
            else:
                processed_data.append(item)
        
        if processed_data and isinstance(processed_data[0], dict):
            # 如果是字典列表，提取实际列名并排序
            actual_columns = list(processed_data[0].keys())
            logger.info(f"从列表数据中提取实际列名: {actual_columns}")
            
            # 对列名进行排序
            sorted_columns = []
            # 首先添加优先列
            for col in priority_columns:
                if col in actual_columns:
                    sorted_columns.append(col)
            # 然后添加其余列
            for col in actual_columns:
                if col not in sorted_columns:
                    sorted_columns.append(col)
                    
            if processed_data:
                logger.info(f"第一条数据示例: {processed_data[0]}")
            return {
                'results': processed_data,
                'columns': sorted_columns
            }
        elif data and isinstance(data[0], list) and len(data) >= 2:
            # 可能是二维数组格式，第一行作为字段名
            headers = data[0]
            rows = data[1:]
            logger.info(f"二维数组格式，headers={headers}, 行数={len(rows)}")
            dict_results = []
            for row in rows:
                if len(headers) == len(row):
                    dict_results.append(dict(zip(headers, row)))
                else:
                    logger.warning(f"行数据和headers长度不匹配: headers={len(headers)}, row={len(row)}")
                    if len(row) < len(headers):
                        padded_row = row + [''] * (len(headers) - len(row))
                        dict_results.append(dict(zip(headers, padded_row)))
                    else:
                        dict_results.append(dict(zip(headers, row[:len(headers)])))
            
            # 对headers进行排序        
            sorted_headers = []
            # 首先添加优先列
            for col in priority_columns:
                if col in headers:
                    sorted_headers.append(col)
            # 然后添加其余列
            for col in headers:
                if col not in sorted_headers:
                    sorted_headers.append(col)
                    
            logger.info(f"转换后的结果数量: {len(dict_results)}")
            if dict_results:
                logger.info(f"转换后的第一条数据示例: {dict_results[0]}")
            return {
                'results': dict_results,
                'columns': sorted_headers
            }
        else:
            # 未知列表格式，使用默认列顺序
            custom_columns = [
                "订单编号", "客户姓名", "客户手机", "订单日期", 
                "产品", "期数", "总待收", "当前待收",
                "客服归属", "业务归属",
                "期数1", "期数2", "期数3", "期数4", "期数5", "期数6",
                "备注信息"
            ]
            logger.warning("未知列表格式，使用默认列顺序")
            if data:
                logger.info(f"原始数据示例: {data[0]}")
            return {
                'results': data,
                'columns': custom_columns
            }
    
    # 如果是其他格式，尝试提取可能的数据结构
    if isinstance(data, dict):
        for key in ['data', 'items', 'records', 'list']:
            if key in data and isinstance(data[key], list):
                logger.info(f"从键'{key}'中提取列表数据，包含{len(data[key])}条记录")
                extracted_data = data[key]
                
                # 从第一条数据中提取列名
                if extracted_data and isinstance(extracted_data[0], dict):
                    actual_columns = list(extracted_data[0].keys())
                    logger.info(f"从提取的数据中获取实际列名: {actual_columns}")
                    
                    # 对列名进行排序
                    sorted_columns = []
                    # 首先添加优先列
                    for col in priority_columns:
                        if col in actual_columns:
                            sorted_columns.append(col)
                    # 然后添加其余列
                    for col in actual_columns:
                        if col not in sorted_columns:
                            sorted_columns.append(col)
                            
                    return {
                        'results': extracted_data,
                        'columns': sorted_columns
                    }
                else:
                    # 使用默认列顺序
                    custom_columns = [
                        "订单编号", "客户姓名", "客户手机", "订单日期", 
                        "产品", "期数", "总待收", "当前待收",
                        "客服归属", "业务归属",
                        "期数1", "期数2", "期数3", "期数4", "期数5", "期数6",
                        "备注信息"
                    ]
                    return {
                        'results': extracted_data,
                        'columns': custom_columns
                    }
    
    # 如果数据是其他格式，记录警告并返回空结果
    logger.warning(f"未知数据格式: {type(data)}")
    custom_columns = [
        "订单编号", "客户姓名", "客户手机", "订单日期", 
        "产品", "期数", "总待收", "当前待收",
        "客服归属", "业务归属",
        "期数1", "期数2", "期数3", "期数4", "期数5", "期数6",
        "备注信息"
    ]
    return {
        'results': [],
        'columns': custom_columns
    }


def process_new_customer_summary_format(data):
    """处理新的客户汇总JSON格式，转换为客户订单汇总页面所需的数据结构"""
    logger.info("处理新的客户汇总JSON格式")
    
    # 初始化标准返回结构
    standard_result = {
        'basic_info': {
            'phones': [], 
            'device_models': [], 
            'services': [], 
            'business_affiliations': [], 
            'customer_remarks': '', 
            'total_devices_count': 0
        },
        'order_summary': {
            'total_count': 0,
            'total_amount': 0,
            'current_receivable': 0,
            'repaid_amount': 0,
            'business_types': {}
        },
        'receivable_by_periods': [],
        'order_details': [],
        'finance_records': []
    }
    
    if not data or not isinstance(data, list) or len(data) == 0:
        logger.error("输入数据为空或格式不正确")
        standard_result['error'] = '数据格式不正确'
        return standard_result
    
    # 提取第一个汇总数据对象 - 转换非ASCII编码的键
    try:
        summary_data = data[0].get("汇总数据") or data[0].get("\u6c47\u603b\u6570\u636e", {})
    except Exception as e:
        logger.error(f"提取汇总数据出错: {str(e)}")
        summary_data = {}
    
    if not summary_data:
        logger.error("未找到汇总数据字段")
        standard_result['error'] = '未找到汇总数据'
        return standard_result
    
    logger.info(f"汇总数据键: {list(summary_data.keys())}")
    
    # 处理客户基本信息
    try:
        basic_info = summary_data.get("客户基本信息") or summary_data.get("\u5ba2\u6237\u57fa\u672c\u4fe1\u606f", {})
        
        # 客户姓名
        name_key = "姓名" if "姓名" in basic_info else "\u59d3\u540d"
        if name_key in basic_info and basic_info[name_key] and len(basic_info[name_key]) > 0:
            customer_name = basic_info[name_key][0]
            logger.info(f"客户姓名: {customer_name}")
        else:
            customer_name = ""
        
        # 手机号
        phone_key = "手机号" if "手机号" in basic_info else "\u624b\u673a\u53f7"
        if phone_key in basic_info and basic_info[phone_key] and len(basic_info[phone_key]) > 0:
            standard_result['basic_info']['phones'] = basic_info[phone_key]
            logger.info(f"手机号: {basic_info[phone_key]}")
        
        # 业务归属
        business_key = "业务归属" if "业务归属" in basic_info else "\u4e1a\u52a1\u5f52\u5c5e"
        if business_key in basic_info and basic_info[business_key] and len(basic_info[business_key]) > 0:
            standard_result['basic_info']['business_affiliations'] = basic_info[business_key]
            logger.info(f"业务归属: {basic_info[business_key]}")
        
        # 客服人员
        service_key = "客服人员" if "客服人员" in basic_info else "\u5ba2\u670d\u4eba\u5458"
        if service_key in basic_info and basic_info[service_key] and len(basic_info[service_key]) > 0:
            standard_result['basic_info']['services'] = basic_info[service_key]
            logger.info(f"客服人员: {basic_info[service_key]}")
        
        # 客户备注
        remark_key = "客户备注" if "客户备注" in basic_info else "\u5ba2\u6237\u5907\u6ce8"
        if remark_key in basic_info and basic_info[remark_key] and len(basic_info[remark_key]) > 0:
            standard_result['basic_info']['customer_remarks'] = basic_info[remark_key][0]
            logger.info(f"客户备注: {basic_info[remark_key][0]}")
        
        # 设备型号
        device_key = "设备型号" if "设备型号" in basic_info else "\u8bbe\u5907\u578b\u53f7"
        if device_key in basic_info and basic_info[device_key] and len(basic_info[device_key]) > 0:
            standard_result['basic_info']['device_models'] = basic_info[device_key]
            logger.info(f"设备型号: {basic_info[device_key]}")
    except Exception as e:
        logger.error(f"处理客户基本信息出错: {str(e)}")
    
    # 处理订单汇总信息
    try:
        summary_key = "订单汇总" if "订单汇总" in summary_data else "\u8ba2\u5355\u6c47\u603b"
        order_summary = summary_data.get(summary_key, {})
        
        if order_summary:
            # 总台数
            total_devices_key = "总台数" if "总台数" in order_summary else "\u603b\u53f0\u6570"
            if total_devices_key in order_summary:
                standard_result['basic_info']['total_devices_count'] = order_summary[total_devices_key]
            
            # 总融资金额
            total_amount_key = "总融资金额" if "总融资金额" in order_summary else "\u603b\u878d\u8d44\u91d1\u989d"
            if total_amount_key in order_summary:
                standard_result['order_summary']['total_amount'] = order_summary[total_amount_key]
            
            # 当前待收金额
            current_receivable_key = "当前待收金额" if "当前待收金额" in order_summary else "\u5f53\u524d\u5f85\u6536\u91d1\u989d"
            if current_receivable_key in order_summary:
                standard_result['order_summary']['current_receivable'] = order_summary[current_receivable_key]
            
            # 已还款金额
            repaid_amount_key = "已还款金额" if "已还款金额" in order_summary else "\u5df2\u8fd8\u6b3e\u91d1\u989d"
            if repaid_amount_key in order_summary:
                standard_result['order_summary']['repaid_amount'] = order_summary[repaid_amount_key]
            
            # 业务类型分布
            business_types_key = "业务类型分布" if "业务类型分布" in order_summary else "\u4e1a\u52a1\u7c7b\u578b\u5206\u5e03"
            if business_types_key in order_summary and isinstance(order_summary[business_types_key], dict):
                standard_result['order_summary']['business_types'] = order_summary[business_types_key]
                
                # 计算总订单数
                total_count = sum(order_summary[business_types_key].values())
                standard_result['order_summary']['total_count'] = total_count
            else:
                # 如果没有业务类型分布，尝试从订单明细计算总数
                logger.warning("未找到业务类型分布数据，将从订单明细计算总数")
    except Exception as e:
        logger.error(f"处理订单汇总信息出错: {str(e)}")
    
    # 处理待收期数统计
    try:
        periods_key = "待收期数统计" if "待收期数统计" in summary_data else "\u5f85\u6536\u671f\u6570\u7edf\u8ba1"
        periods_data = summary_data.get(periods_key, [])
        
        if periods_data and isinstance(periods_data, list):
            # 先记录期数与订单的映射关系，用于后续分析
            period_orders_map = {}
            period_devices_map = {}
            
            # 第一次遍历待收期数数据，构建基本期数项
            for period in periods_data:
                if not isinstance(period, dict):
                    continue
                
                period_item = {}
                
                # 期数
                period_value = None
                if "periods" in period:
                    period_value = period['periods']
                elif "\u671f\u6570" in period:
                    period_value = period['\u671f\u6570']
                
                if period_value is None:
                    continue
                    
                period_item['period'] = period_value
                
                # 金额
                if "amount" in period:
                    period_item['amount'] = period['amount']
                elif "\u91d1\u989d" in period:
                    period_item['amount'] = period['\u91d1\u989d']
                else:
                    period_item['amount'] = 0
                
                # 数量
                if "count" in period:
                    period_item['order_count'] = period['count']
                elif "\u6570\u91cf" in period:
                    period_item['order_count'] = period['\u6570\u91cf']
                else:
                    period_item['order_count'] = 0
                
                # 初始化台数为0，后续计算
                period_item['devices_count'] = 0
                
                # 将期数项添加到结果列表中
                standard_result['receivable_by_periods'].append(period_item)
                
                # 记录期数与索引的映射，便于后续更新
                period_orders_map[str(period_value)] = []
                period_devices_map[str(period_value)] = 0
            
            # 处理完期数统计信息后，尝试从订单明细中计算每个期数对应的台数
            # 先获取订单明细
            details_key = "订单明细" if "订单明细" in summary_data else "\u8ba2\u5355\u660e\u7ec6"
            order_details = summary_data.get(details_key, [])
            
            # 如果有订单明细数据，则分析期数与台数关系
            if order_details and isinstance(order_details, list):
                for order in order_details:
                    if not isinstance(order, dict):
                        continue
                    
                    # 获取当前待收期数
                    current_period = None
                    for period_key in ["待收期数", "\u5f85\u6536\u671f\u6570", "当前期数"]:
                        if period_key in order:
                            current_period = order[period_key]
                            break
                    
                    # 获取台数
                    devices_count = 0
                    for devices_key in ["台数", "\u53f0\u6570", "设备台数"]:
                        if devices_key in order:
                            try:
                                devices_count = int(order[devices_key])
                                break
                            except (ValueError, TypeError):
                                # 如果转换失败，尝试使用1作为默认值
                                devices_count = 1
                    
                    # 如果有期数和台数信息，则更新对应期数的台数统计
                    if current_period is not None and current_period != "" and str(current_period) in period_devices_map:
                        period_devices_map[str(current_period)] += devices_count
                        
                        # 同时记录订单编号，便于后续分析
                        order_number = None
                        for order_key in ["订单编号", "\u8ba2\u5355\u7f16\u53f7"]:
                            if order_key in order:
                                order_number = order[order_key]
                                break
                        
                        if order_number:
                            period_orders_map[str(current_period)].append(order_number)
            
            # 更新每个期数项的台数信息
            for period_item in standard_result['receivable_by_periods']:
                period_value = str(period_item['period'])
                if period_value in period_devices_map:
                    period_item['devices_count'] = period_devices_map[period_value]
                    
                    # 如果期数项中没有订单数量，则使用映射中的订单数量
                    if period_item['order_count'] == 0 and period_value in period_orders_map:
                        period_item['order_count'] = len(period_orders_map[period_value])
            
            logger.info(f"处理了 {len(standard_result['receivable_by_periods'])} 条待收期数记录")
            if standard_result['receivable_by_periods']:
                logger.info(f"期数台数映射: {period_devices_map}")
                logger.info(f"第一条待收期数记录: {standard_result['receivable_by_periods'][0]}")
    except Exception as e:
        logger.error(f"处理待收期数统计出错: {str(e)}")
        logger.exception("待收期数处理异常详情:")
    
    # 处理订单明细
    try:
        details_key = "订单明细" if "订单明细" in summary_data else "\u8ba2\u5355\u660e\u7ec6"
        order_details = summary_data.get(details_key, [])
        
        if order_details and isinstance(order_details, list):
            for order in order_details:
                if not isinstance(order, dict):
                    continue
                
                order_item = {}
                
                # 遍历并映射键值对
                key_mappings = {
                    # 订单编号
                    "order_number": ["订单编号", "\u8ba2\u5355\u7f16\u53f7"], 
                    # 订单日期
                    "order_date": ["订单日期", "\u8ba2\u5355\u65e5\u671f"],
                    # 产品类型
                    "product_type": ["产品类型", "\u4ea7\u54c1\u7c7b\u578b"],
                    # 总融资金额
                    "total_finance": ["融资总额", "\u878d\u8d44\u603b\u989d", "总融资额", "融资金额"],
                    # 当前待收
                    "current_receivable": ["当前待收", "\u5f53\u524d\u5f85\u6536"],
                    # 总期数
                    "total_periods": ["总期数", "\u603b\u671f\u6570", "期数"],
                    # 当前待收期数
                    "current_receivable_periods": ["待收期数", "\u5f85\u6536\u671f\u6570", "当前期数"],
                    # 台数
                    "devices_count": ["台数", "\u53f0\u6570", "设备台数"],
                    # 成本
                    "cost": ["成本", "\u6210\u672c"]
                }
                
                for target_key, source_keys in key_mappings.items():
                    for source_key in source_keys:
                        if source_key in order:
                            order_item[target_key] = order[source_key]
                            break
                
                # 确保所有必要字段都存在，避免模板访问时出错
                required_fields = ["order_number", "order_date", "product_type", "total_finance", 
                                 "current_receivable", "total_periods", "current_receivable_periods", 
                                 "devices_count"]
                
                for field in required_fields:
                    if field not in order_item or order_item[field] is None:
                        order_item[field] = "-"
                
                standard_result['order_details'].append(order_item)
            
            logger.info(f"处理了 {len(standard_result['order_details'])} 条订单明细记录")
            
            # 如果订单汇总中没有设置总数，使用订单明细的长度
            if standard_result['order_summary']['total_count'] == 0 and len(standard_result['order_details']) > 0:
                standard_result['order_summary']['total_count'] = len(standard_result['order_details'])
                
            # 记录第一条订单详情，用于调试
            if len(standard_result['order_details']) > 0:
                logger.info(f"第一条订单详情示例: {standard_result['order_details'][0]}")
        else:
            logger.warning("未找到订单明细数据")
    except Exception as e:
        logger.error(f"处理订单明细出错: {str(e)}")
    
    # 处理资金流水
    try:
        finance_key = "资金流水" if "资金流水" in summary_data else "\u8d44\u91d1\u6d41\u6c34"
        finance_records = summary_data.get(finance_key, [])
        
        if finance_records and isinstance(finance_records, list):
            for record in finance_records:
                if not isinstance(record, dict):
                    continue
                
                finance_item = {}
                
                # 映射键值对
                key_mappings = {
                    # 交易日期
                    "transaction_date": ["交易日期", "\u4ea4\u6613\u65e5\u671f", "日期"],
                    # 交易流水号
                    "transaction_id": ["交易流水号", "\u4ea4\u6613\u6d41\u6c34\u53f7", "流水号"],
                    # 交易类型
                    "transaction_type": ["交易类型", "\u4ea4\u6613\u7c7b\u578b", "类型"],
                    # 订单编号
                    "order_number": ["订单编号", "\u8ba2\u5355\u7f16\u53f7", "订单号"],
                    # 资金流向
                    "flow_direction": ["资金流向", "\u8d44\u91d1\u6d41\u5411", "流向"],
                    # 金额
                    "amount": ["金额", "\u91d1\u989d", "交易金额"]
                }
                
                for target_key, source_keys in key_mappings.items():
                    for source_key in source_keys:
                        if source_key in record:
                            finance_item[target_key] = record[source_key]
                            break
                
                # 确保所有必须的字段都有值，即使为空值
                for key in ["transaction_date", "transaction_type", "amount", "order_number"]:
                    if key not in finance_item or finance_item[key] is None:
                        finance_item[key] = "-"
                
                standard_result['finance_records'].append(finance_item)
            
            logger.info(f"处理了 {len(standard_result['finance_records'])} 条资金流水记录")
            if len(standard_result['finance_records']) > 0:
                logger.info(f"第一条资金流水示例: {standard_result['finance_records'][0]}")
        else:
            logger.warning("未找到资金流水数据")
    except Exception as e:
        logger.error(f"处理资金流水出错: {str(e)}")
        logger.exception("资金流水处理异常详情:")
    
    # 输出处理后的数据结构概况
    logger.info(f"处理完成，生成的数据结构包含以下字段:")
    for key, value in standard_result.items():
        if isinstance(value, list):
            logger.info(f"  - {key}: {len(value)}条记录")
        elif isinstance(value, dict):
            logger.info(f"  - {key}: {list(value.keys())}")
    
    return standard_result


def process_customer_summary(data):
    """处理客户订单汇总数据"""
    # 记录详细的数据处理过程
    logger.info(f"开始处理客户汇总数据: 数据类型={type(data)}")
    
    # 定义一个默认的返回结构，确保所有必要字段都存在
    default_response = {
        'basic_info': {
            'phones': [], 
            'device_models': [], 
            'services': [], 
            'business_affiliations': [], 
            'customer_remarks': '', 
            'total_devices_count': 0
        },
        'order_summary': {
            'total_count': 0,
            'total_amount': 0,
            'current_receivable': 0,
            'repaid_amount': 0,
            'business_types': {}
        },
        'receivable_by_periods': [],
        'order_details': [],
        'finance_records': []
    }
    
    # 更严格的空数据检查
    if data is None:
        logger.error("返回数据为None")
        default_response['error'] = "返回数据为空"
        return default_response
    
    if isinstance(data, dict) and not data:
        logger.error("返回数据为空字典")
        default_response['error'] = "返回数据为空"
        return default_response
        
    if isinstance(data, list) and not data:
        logger.error("返回数据为空列表")
        default_response['error'] = "返回数据为空"
        return default_response
        
    # 检查特殊空返回值情况
    if data == "null" or data == "undefined" or data == "":
        logger.error(f"返回数据为特殊空值: {data}")
        default_response['error'] = "返回数据为空值"
        return default_response
    
    # 记录数据大小
    if isinstance(data, (dict, list)) and len(str(data)) <= 1000:
        logger.info(f"接收到的数据: {data}")
    else:
        logger.info("接收到的数据太大，不完整打印")
    
    # 初始化summary_data
    summary_data = None
    
    # 处理嵌套列表结构
    if isinstance(data, list) and len(data) > 0:
        first_level = data[0]
        logger.info(f"第一层元素类型: {type(first_level)}")
        if isinstance(first_level, list) and len(first_level) > 0:
            second_level = first_level[0]
            logger.info(f"第二层元素类型: {type(second_level)}")
            if isinstance(second_level, dict) and 'summary' in second_level:
                summary_data = second_level['summary']
                logger.info("从嵌套列表中提取summary成功")
        else:
            if isinstance(first_level, dict) and 'summary' in first_level:
                summary_data = first_level['summary']
                logger.info("从列表第一项的summary键获取数据成功")
    
    # 处理字典结构
    if summary_data is None and isinstance(data, dict):
        if 'summary' in data:
            summary_data = data['summary']
            logger.info("从字典的summary键获取数据成功")
        else:
            summary_data = data
            logger.info("直接使用字典作为数据")
    
    # 递归搜索summary字段
    if summary_data is None:
        logger.info("尝试递归搜索summary字段")
        summary_data = find_field_in_nested_data(data, 'summary')
        if summary_data:
            logger.info("通过递归搜索找到summary数据")
        else:
            logger.warning("递归搜索未找到summary数据")
            summary_data = data
            logger.info("使用原始数据作为汇总数据")
    
    if not summary_data:
        logger.error("提取的汇总数据为空")
        default_response['error'] = "未找到有效的客户订单汇总数据"
        return default_response
    
    # 处理列表中的汇总数据
    if isinstance(summary_data, list) and len(summary_data) > 0:
        logger.info("summary_data是列表，尝试使用第一个元素")
        first_item = summary_data[0]
        if isinstance(first_item, dict) and ('basic_info' in first_item or 'order_summary' in first_item):
            summary_data = first_item
            logger.info("成功从列表中提取汇总数据")
    
    if not isinstance(summary_data, dict):
        logger.error(f"无法获取有效的汇总数据: {type(summary_data)}")
        default_response['error'] = "未找到有效的客户订单汇总数据"
        return default_response
    
    # 递归展平所有嵌套对象
    try:
        summary_data = flatten_nested_objects(summary_data)
    except Exception as e:
        logger.error(f"展平嵌套对象失败: {str(e)}")
        # 继续使用原始数据
    
    # 将处理后的数据合并到默认响应结构中
    result = default_response.copy()
    
    # 检查并合并客户基本信息
    if 'basic_info' in summary_data and isinstance(summary_data['basic_info'], dict):
        for key, value in summary_data['basic_info'].items():
            result['basic_info'][key] = value
    else:
        # 尝试从顶层提取基本信息字段
        if 'phones' in summary_data:
            result['basic_info']['phones'] = summary_data['phones']
        if 'device_models' in summary_data:
            result['basic_info']['device_models'] = summary_data['device_models']
        if 'services' in summary_data:
            result['basic_info']['services'] = summary_data['services']
        if 'business_affiliations' in summary_data:
            result['basic_info']['business_affiliations'] = summary_data['business_affiliations']
        if 'customer_remarks' in summary_data:
            result['basic_info']['customer_remarks'] = summary_data['customer_remarks']
        if 'total_devices_count' in summary_data:
            result['basic_info']['total_devices_count'] = summary_data['total_devices_count']
    
    # 确保phones等字段是列表类型
    for field in ['phones', 'device_models', 'services', 'business_affiliations']:
        if not isinstance(result['basic_info'][field], list):
            if result['basic_info'][field] is None:
                result['basic_info'][field] = []
            elif isinstance(result['basic_info'][field], str):
                result['basic_info'][field] = [result['basic_info'][field]]
            else:
                result['basic_info'][field] = [str(result['basic_info'][field])]
    
    # 检查并合并订单汇总信息
    if 'order_summary' in summary_data and isinstance(summary_data['order_summary'], dict):
        for key, value in summary_data['order_summary'].items():
            result['order_summary'][key] = value
    else:
        # 尝试从顶层提取订单汇总字段
        for key in ['total_count', 'total_amount', 'current_receivable', 'repaid_amount', 'business_types']:
            if key in summary_data:
                result['order_summary'][key] = summary_data[key]
    
    # 检查并合并待收期数信息
    if 'receivable_by_periods' in summary_data and isinstance(summary_data['receivable_by_periods'], list):
        result['receivable_by_periods'] = summary_data['receivable_by_periods']
    
    # 检查并合并订单详情
    if 'order_details' in summary_data and isinstance(summary_data['order_details'], list):
        result['order_details'] = summary_data['order_details']
    
    # 检查并合并财务流水
    if 'finance_records' in summary_data and isinstance(summary_data['finance_records'], list):
        result['finance_records'] = summary_data['finance_records']
    
    # 如果有错误信息，也一并传递
    if 'error' in summary_data:
        result['error'] = summary_data['error']
    
    logger.info(f"客户汇总数据处理完成，生成的数据结构包含以下字段: {', '.join(result.keys())}")
    
    return result


def find_field_in_nested_data(data, field_name, depth=0, max_depth=5):
    """在嵌套数据结构中递归搜索指定字段"""
    if depth > max_depth:
        return None
    
    if isinstance(data, dict):
        # 直接检查当前字典
        if field_name in data:
            return data[field_name]
        
        # 递归检查所有子字典
        for key, value in data.items():
            result = find_field_in_nested_data(value, field_name, depth + 1, max_depth)
            if result is not None:
                return result
    
    elif isinstance(data, list) and len(data) > 0:
        # 检查列表中的第一个元素
        if isinstance(data[0], (dict, list)):
            result = find_field_in_nested_data(data[0], field_name, depth + 1, max_depth)
            if result is not None:
                return result
        
        # 如果列表第一个元素没有找到，尝试其他元素
        for item in data[1:]:
            if isinstance(item, (dict, list)):
                result = find_field_in_nested_data(item, field_name, depth + 1, max_depth)
                if result is not None:
                    return result
    
    return None 


def flatten_nested_objects(data):
    """递归展平嵌套对象，确保前端可以正确显示所有字段"""
    if not isinstance(data, dict):
        return data
    
    # 创建展平后的字典
    flattened = {}
    
    # 遍历字典的所有键值对
    for key, value in data.items():
        # 如果值是字典，则展平并与当前字典合并
        if isinstance(value, dict):
            logger.info(f"展平嵌套对象: {key}")
            
            # 首先将原始对象的键复制到展平字典
            flattened[key] = value
            
            # 然后将嵌套字典的所有键添加到顶层
            for sub_key, sub_value in value.items():
                # 避免命名冲突 - 如果顶层已有同名键，则跳过
                if sub_key not in flattened:
                    flattened[sub_key] = sub_value
        elif isinstance(value, list):
            # 如果值是列表，则检查列表中的每个元素
            flattened[key] = []
            for item in value:
                if isinstance(item, dict):
                    # 递归展平列表中的字典
                    flattened_item = flatten_nested_objects(item)
                    flattened[key].append(flattened_item)
                else:
                    flattened[key].append(item)
        else:
            # 非字典和列表类型直接复制
            flattened[key] = value
    
    return flattened


def flatten_nested_order_data(order_data):
    """
    将嵌套格式的订单数据扁平化为单层字典
    
    Args:
        order_data: 包含嵌套结构的订单数据
        
    Returns:
        dict: 扁平化后的单层字典
    """
    flattened = {}
    
    # 处理客户信息
    if "客户信息" in order_data and isinstance(order_data["客户信息"], dict):
        for key, value in order_data["客户信息"].items():
            # 对一些特殊字段进行重命名，避免和其他字段冲突
            if key == "备注":
                flattened["客户信息备注"] = value
            elif key == "手机号" or key == "手机号码":
                flattened["客户手机"] = value
            elif key == "业务归属":
                flattened["业务归属"] = value
            elif key == "客服":
                flattened["客服"] = value
            else:
                flattened[key] = value
    
    # 处理订单信息
    if "订单信息" in order_data and isinstance(order_data["订单信息"], dict):
        for key, value in order_data["订单信息"].items():
            # 对一些特殊字段进行重命名
            if key == "订单编号":
                flattened["订单编号"] = value
            elif key == "订单日期":
                flattened["订单日期"] = value
            elif key == "设备型号":
                flattened["设备型号"] = value
                # 为了兼容性，同时设置"产品"字段
                flattened["产品"] = value
            elif key == "台数":
                flattened["台数"] = value
            else:
                flattened[key] = value
    
    # 处理账单信息
    if "账单信息" in order_data and isinstance(order_data["账单信息"], dict):
        for key, value in order_data["账单信息"].items():
            # 对一些特殊字段进行重命名
            if key == "当前期数":
                flattened["当前期数"] = value
            elif key == "账单日期":
                flattened["账单日期"] = value  # 只保留账单日期字段
            elif key == "账单状态":
                flattened["账单状态"] = value
                # 同时将账单状态映射为贷后状态，确保前端显示一致性
                flattened["贷后状态"] = value
            else:
                flattened[key] = value
    
    # 如果有直接字段（不在嵌套结构中），也添加到扁平化结果中
    for key, value in order_data.items():
        if key not in ["客户信息", "订单信息", "账单信息"] and not isinstance(value, dict):
            # 特别处理贷后状态字段
            if key == "贷后状态":
                flattened["贷后状态"] = value
            else:
                flattened[key] = value
    
    return flattened


def get_daily_cached_data():
    """
    获取每日预加载的缓存数据
    包括今日待处理订单和逾期订单数据
    
    Returns:
        dict: 包含今日待处理订单和逾期订单数据的字典
    """
    logger.info("获取每日预加载的缓存数据")
    
    # 从缓存中获取今日待处理订单数据
    today = datetime.date.today().strftime('%Y-%m-%d')
    today_orders_count = cache.get('daily_today_orders_count')
    today_orders_data = cache.get('daily_today_orders_data')
    
    # 如果缓存中没有数据，则直接调用原函数获取
    if today_orders_data is None:
        logger.info("缓存中没有今日待处理订单数据，直接获取")
        today_orders_data = get_filtered_data(today)
        today_orders_count = len(today_orders_data.get('results', []))
    
    # 从缓存中获取逾期订单数据
    overdue_orders_count = cache.get('daily_overdue_orders_count')
    overdue_orders_data = cache.get('daily_overdue_orders_data')
    
    # 如果缓存中没有数据，则尝试获取全部逾期订单数据
    if overdue_orders_data is None:
        logger.info("缓存中没有逾期订单数据，尝试获取全部数据")
        # 首先尝试获取第一页了解总数
        first_page = get_overdue_orders(page=1, limit=100, force_refresh=True)
        
        if 'pagination' in first_page and first_page['pagination'].get('total', 0) > 100:
            total_records = first_page['pagination']['total']
            logger.info(f"检测到大量逾期订单({total_records}条)，使用大limit获取全部数据")
            # 使用足够大的limit获取全部数据
            overdue_orders_data = get_overdue_orders(page=1, limit=max(total_records + 100, 10000), force_refresh=True)
            overdue_orders_count = len(overdue_orders_data.get('results', []))
            
            # 如果获取的数据明显少于预期，尝试分批获取
            if overdue_orders_count < total_records * 0.9:
                logger.warning(f"获取数据({overdue_orders_count})少于预期({total_records})，尝试分批获取")
                all_results = []
                all_columns = first_page.get('columns', [])
                
                # 分批获取数据
                batch_size = 100
                total_pages = min((total_records + batch_size - 1) // batch_size, 50)  # 最多50页
                
                for page_num in range(1, total_pages + 1):
                    page_data = get_overdue_orders(page=page_num, limit=batch_size, force_refresh=True)
                    if 'results' in page_data and page_data['results']:
                        all_results.extend(page_data['results'])
                        if 'columns' in page_data:
                            all_columns = page_data['columns']
                    else:
                        break
                
                # 构建完整数据
                overdue_orders_data = {
                    'results': all_results,
                    'columns': all_columns,
                    'pagination': {'page': 1, 'pages': 1, 'total': len(all_results), 'limit': len(all_results)}
                }
                overdue_orders_count = len(all_results)
                logger.info(f"分批获取完成，总计: {overdue_orders_count}条记录")
        else:
            # 记录数不多或者没有分页信息，直接使用
            overdue_orders_data = first_page
            overdue_orders_count = len(overdue_orders_data.get('results', []))
    
    return {
        'today_orders_count': today_orders_count,
        'today_orders_data': today_orders_data,
        'overdue_orders_count': overdue_orders_count,
        'overdue_orders_data': overdue_orders_data
    }


def process_period_data(data):
    """
    处理期数数据，将期数与账单状态关联
    
    Args:
        data: 包含期数和账单信息的数据
        
    Returns:
        处理后的数据，将期数与账单状态关联
    """
    logger.info("开始处理期数数据与账单状态关联")
    
    if not isinstance(data, dict) or 'results' not in data or not data['results']:
        logger.warning("数据格式不正确或没有结果数据")
        return data
    
    # 遍历所有结果记录
    for item in data['results']:
        if not isinstance(item, dict):
            continue
            
        # 检查是否有账单状态字段
        account_status = item.get('账单状态', '')
        bill_date = item.get('账单日期', '')
        
        # 查找期数字段（期数1-期数6）
        period_fields = [key for key in item.keys() if key.startswith('期数') and key[2:].isdigit()]
        
        for period_field in period_fields:
            period_value = item.get(period_field)
            
            # 跳过空值或已经带有状态的值
            if not period_value or isinstance(period_value, str) and '（' in period_value:
                continue
                
            # 检查期数值是否为日期格式
            if isinstance(period_value, str) and (
                '-' in period_value or '/' in period_value or 
                (len(period_value) == 10 and period_value[:4].isdigit())
            ):
                # 尝试确定此期数的状态
                period_status = ''
                
                # 如果期数的日期与账单日期相同，标记为账单日
                if period_value == bill_date:
                    period_status = '账单日'
                # 根据日期比较判断状态
                else:
                    try:
                        # 尝试解析日期
                        from datetime import datetime
                        
                        # 处理可能的日期格式
                        if len(period_value) == 10 and period_value[4] == '-' and period_value[7] == '-':
                            # 标准格式 YYYY-MM-DD
                            period_date = datetime.strptime(period_value, '%Y-%m-%d')
                        elif len(period_value) == 8 and period_value[2] == '-' and period_value[5] == '-':
                            # 缩写格式 YY-MM-DD，假设是20XX年
                            year = int(period_value[:2])
                            formatted_date = f"20{year}-{period_value[3:5]}-{period_value[6:8]}"
                            period_date = datetime.strptime(formatted_date, '%Y-%m-%d')
                        else:
                            # 其他未知格式，跳过
                            continue
                            
                        today = datetime.now()
                        
                        # 根据日期和账单状态确定期数状态
                        if period_date < today:
                            # 已过期的日期
                            if account_status == '逾期未还':
                                period_status = '逾期未还'
                            elif account_status == '逾期还款':
                                period_status = '逾期还款'
                            elif account_status == '催收中':
                                period_status = '催收中'
                            elif account_status == '已诉讼':
                                period_status = '已诉讼'
                            elif account_status == '已结清':
                                period_status = '已结清'
                            elif account_status == '提前还款':
                                period_status = '提前还款'
                            elif account_status == '已取消':
                                period_status = '已取消'
                            else:
                                # 其他情况假设按时还款
                                period_status = '按时还款'
                        else:
                            # 未到期的日期
                            period_status = '未到还款日期'
                    except Exception as e:
                        logger.warning(f"解析期数日期出错: {e}")
                
                # 添加状态信息到期数值
                if period_status:
                    item[period_field] = f"{period_value}（{period_status}）"
    
    return data