/**
 * 智能资源加载器 - 按需加载大型库
 */
class AssetLoader {
    constructor() {
        this.loadedAssets = new Set();
        this.loadingPromises = new Map();
    }

    /**
     * 加载JavaScript文件
     * @param {string} src - 脚本源路径
     * @param {string} id - 可选的脚本ID
     * @returns {Promise}
     */
    loadScript(src, id = null) {
        // 如果已经加载过，直接返回
        if (this.loadedAssets.has(src)) {
            return Promise.resolve();
        }

        // 如果正在加载，返回现有的Promise
        if (this.loadingPromises.has(src)) {
            return this.loadingPromises.get(src);
        }

        const promise = new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            if (id) script.id = id;
            script.async = true;
            
            script.onload = () => {
                this.loadedAssets.add(src);
                this.loadingPromises.delete(src);
                resolve();
            };
            
            script.onerror = () => {
                this.loadingPromises.delete(src);
                reject(new Error(`Failed to load script: ${src}`));
            };
            
            document.head.appendChild(script);
        });

        this.loadingPromises.set(src, promise);
        return promise;
    }

    /**
     * 加载CSS文件
     * @param {string} href - 样式表路径
     * @param {string} id - 可选的样式表ID
     * @returns {Promise}
     */
    loadCSS(href, id = null) {
        if (this.loadedAssets.has(href)) {
            return Promise.resolve();
        }

        if (this.loadingPromises.has(href)) {
            return this.loadingPromises.get(href);
        }

        const promise = new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            if (id) link.id = id;
            
            link.onload = () => {
                this.loadedAssets.add(href);
                this.loadingPromises.delete(href);
                resolve();
            };
            
            link.onerror = () => {
                this.loadingPromises.delete(href);
                reject(new Error(`Failed to load CSS: ${href}`));
            };
            
            document.head.appendChild(link);
        });

        this.loadingPromises.set(href, promise);
        return promise;
    }

    /**
     * 按需加载Excel功能
     * @returns {Promise}
     */
    async loadExcelSupport() {
        try {
            // 尝试多个可能的Excel库路径
            const excelPaths = [
                '/static/vendor/excel/excel.min.js',
                '/static/js/excel.min.js',
                '/static/dist/js/excel.min.js'
            ];
            
            let loaded = false;
            for (const path of excelPaths) {
                try {
                    await this.loadScript(path, 'excel-lib');
                    console.log(`Excel支持已从 ${path} 加载`);
                    loaded = true;
                    break;
                } catch (error) {
                    console.warn(`从 ${path} 加载Excel库失败:`, error.message);
                }
            }
            
            if (!loaded) {
                console.warn('所有Excel库路径都加载失败，Excel功能将不可用');
                return false;
            }
            
            return true;
        } catch (error) {
            console.error('Excel库加载失败:', error);
            return false;
        }
    }

    /**
     * 按需加载图表功能
     * @returns {Promise}
     */
    async loadChartSupport() {
        // 检查Chart.js是否已经存在
        if (typeof Chart !== 'undefined') {
            console.log('Chart.js已存在，跳过加载');
            return true;
        }

        try {
            // 尝试多个可能的Chart.js路径
            const chartPaths = [
                '/static/vendor/chart.js/chart.min.js',
                '/static/dist/js/charts.min.js',
                '/static/js/chart-engine.js'
            ];
            
            let loaded = false;
            let lastError = null;
            
            for (const path of chartPaths) {
                try {
                    await this.loadScript(path, 'chart-lib');
                    // 验证Chart.js是否正确加载
                    if (typeof Chart !== 'undefined') {
                        console.log(`图表支持已从 ${path} 加载`);
                        loaded = true;
                        break;
                    }
                } catch (error) {
                    lastError = error;
                    console.warn(`从 ${path} 加载图表库失败:`, error.message);
                }
            }
            
            if (!loaded) {
                throw new Error(`所有Chart.js路径都加载失败，最后错误: ${lastError?.message || '未知错误'}`);
            }
            
            // 额外验证Chart.js功能
            if (typeof Chart === 'undefined' || typeof Chart.register === 'undefined') {
                throw new Error('Chart.js加载成功但功能不完整');
            }
            
            return true;
        } catch (error) {
            console.error('图表库加载失败:', error);
            return false;
        }
    }

    /**
     * 按需加载企业级功能
     * @returns {Promise}
     */
    async loadEnterpriseSupport() {
        try {
            // 尝试多个可能的企业级功能路径
            const enterprisePaths = [
                '/static/js/enterprise/enterprise.min.js',
                '/static/js/enterprise-table-manager.js',
                '/static/dist/js/enterprise.min.js'
            ];
            
            let loaded = false;
            for (const path of enterprisePaths) {
                try {
                    await this.loadScript(path, 'enterprise-lib');
                    console.log(`企业级功能已从 ${path} 加载`);
                    loaded = true;
                    break;
                } catch (error) {
                    console.warn(`从 ${path} 加载企业级功能失败:`, error.message);
                }
            }
            
            if (!loaded) {
                console.warn('所有企业级功能路径都加载失败，企业级功能将不可用');
                return false;
            }
            
            return true;
        } catch (error) {
            console.error('企业级功能加载失败:', error);
            return false;
        }
    }

    /**
     * 预加载资源
     * @param {string} src - 资源路径
     * @param {string} as - 资源类型 (script, style, etc.)
     */
    preload(src, as = 'script') {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = src;
        link.as = as;
        
        // 修复跨域属性警告 - 对于同源资源不需要crossOrigin
        if (as === 'script' && !src.startsWith(window.location.origin)) {
            link.crossOrigin = 'anonymous';
        }
        
        document.head.appendChild(link);
    }

    /**
     * 检查资源是否已加载
     * @param {string} src - 资源路径
     * @returns {boolean}
     */
    isLoaded(src) {
        return this.loadedAssets.has(src);
    }

    /**
     * 获取图表库加载状态
     * @returns {Object} 包含状态信息的对象
     */
    getChartStatus() {
        const status = {
            loaded: typeof Chart !== 'undefined',
            version: null,
            features: {
                register: false,
                Chart: false,
                plugins: false
            },
            errors: []
        };

        if (status.loaded) {
            try {
                status.version = Chart.version || 'unknown';
                status.features.register = typeof Chart.register === 'function';
                status.features.Chart = typeof Chart === 'function';
                status.features.plugins = typeof Chart.plugins === 'object';
            } catch (error) {
                status.errors.push(`检查Chart.js功能时出错: ${error.message}`);
            }
        }

        return status;
    }

    /**
     * 等待图表库加载完成
     * @param {number} timeout - 超时时间（毫秒）
     * @returns {Promise<boolean>}
     */
    async waitForChartReady(timeout = 5000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            const status = this.getChartStatus();
            if (status.loaded && status.features.register && status.features.Chart) {
                return true;
            }
            
            // 等待100ms后重新检查
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        throw new Error(`图表库在${timeout}ms内未就绪`);
    }
}

// 创建全局实例
window.AssetLoader = new AssetLoader();

// 导出功能函数
window.loadExcelSupport = () => window.AssetLoader.loadExcelSupport();
window.loadChartSupport = () => window.AssetLoader.loadChartSupport();
window.loadEnterpriseSupport = () => window.AssetLoader.loadEnterpriseSupport();

// 导出图表相关的新功能
window.getChartStatus = () => window.AssetLoader.getChartStatus();
window.waitForChartReady = (timeout) => window.AssetLoader.waitForChartReady(timeout);

// 预加载关键资源
document.addEventListener('DOMContentLoaded', () => {
    console.log('Asset loader initialized, checking page requirements...');
    
    // 预加载可能需要的资源 - 使用实际存在的文件
    if (document.querySelector('[data-needs-excel]')) {
        console.log('Page needs Excel support, checking available paths...');
        // 预加载最可能的Excel库路径
        window.AssetLoader.preload('/static/vendor/excel/excel.min.js');
    }
    
    if (document.querySelector('[data-needs-charts]')) {
        console.log('Page needs charts support, preloading...');
        // 预加载已确认存在的Chart.js文件
        window.AssetLoader.preload('/static/vendor/chart.js/chart.min.js');
    }
    
    if (document.querySelector('[data-needs-enterprise]')) {
        console.log('Page needs enterprise support, checking available paths...');
        // 预加载已确认存在的企业级功能文件
        window.AssetLoader.preload('/static/js/enterprise-table-manager.js');
    }
});