/*! Bootstrap 5 integration for DataTables' Responsive
 * © SpryMedia Ltd - datatables.net/license
 */
!function(a){"function"==typeof define&&define.amd?define(["jquery","datatables.net-bs5","datatables.net-responsive"],function(e){return a(e,window,document)}):"object"==typeof exports?module.exports=function(e,d){return e=e||window,(d=d||("undefined"!=typeof window?require("jquery"):require("jquery")(e))).fn.dataTable||require("datatables.net-bs5")(e,d),d.fn.dataTable||require("datatables.net-responsive")(e,d),a(d,e,e.document)}:a(jQuery,window,document)}(function(i,e,d,a){"use strict";var s,o=i.fn.dataTable,t=o.Responsive.display,l=t.modal,r=i('<div class="modal fade dtr-bs-modal" role="dialog"><div class="modal-dialog" role="document"><div class="modal-content"><div class="modal-header"><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div><div class="modal-body"/></div></div></div>'),u=e.bootstrap;return o.Responsive.bootstrap=function(e){u=e},t.modal=function(n){return s=s||new u.Modal(r[0]),function(e,d,a){var o,t;i.fn.modal?d||(n&&n.header&&(t=(o=r.find("div.modal-header")).find("button").detach(),o.empty().append('<h4 class="modal-title">'+n.header(e)+"</h4>").append(t)),r.find("div.modal-body").empty().append(a()),r.appendTo("body").modal(),s.show()):l(e,d,a)}},o});