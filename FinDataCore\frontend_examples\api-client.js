/**
 * API客户端 - 封装所有API调用
 * 自动处理认证、错误处理和重试逻辑
 */
class ApiClient {
    constructor(baseUrl = '') {
        this.baseUrl = baseUrl;
        this.defaultTimeout = 30000; // 30秒超时
        this.retryAttempts = 3;
        this.retryDelay = 1000; // 1秒
    }
    
    /**
     * 通用请求方法
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<any>}
     */
    async request(url, options = {}) {
        const fullUrl = this.baseUrl + url;
        
        // 默认选项
        const defaultOptions = {
            timeout: this.defaultTimeout,
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        // 合并选项
        const requestOptions = { ...defaultOptions, ...options };
        
        // 自动添加认证头
        if (authManager.isAuthenticated()) {
            try {
                requestOptions.headers = {
                    ...requestOptions.headers,
                    ...authManager.getAuthHeaders()
                };
            } catch (error) {
                console.error('获取认证头失败:', error);
                this.handleAuthError();
                return;
            }
        }
        
        // 执行请求（带重试）
        return this.executeWithRetry(fullUrl, requestOptions);
    }
    
    /**
     * 带重试的请求执行
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<any>}
     */
    async executeWithRetry(url, options) {
        let lastError;
        
        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                const response = await this.fetchWithTimeout(url, options);
                
                // 处理HTTP错误状态
                if (!response.ok) {
                    await this.handleHttpError(response);
                    return;
                }
                
                // 解析响应
                const data = await response.json();
                console.log(`API请求成功: ${url}`);
                return data;
                
            } catch (error) {
                lastError = error;
                console.warn(`API请求失败 (尝试 ${attempt}/${this.retryAttempts}):`, error.message);
                
                // 如果是最后一次尝试，抛出错误
                if (attempt === this.retryAttempts) {
                    break;
                }
                
                // 等待后重试
                await this.delay(this.retryDelay * attempt);
            }
        }
        
        // 所有重试都失败了
        console.error(`API请求最终失败: ${url}`, lastError);
        throw new Error(`请求失败: ${lastError.message}`);
    }
    
    /**
     * 带超时的fetch
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<Response>}
     */
    async fetchWithTimeout(url, options) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), options.timeout);
        
        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error('请求超时');
            }
            throw error;
        }
    }
    
    /**
     * 处理HTTP错误
     * @param {Response} response - 响应对象
     */
    async handleHttpError(response) {
        let errorMessage = `HTTP ${response.status}`;
        
        try {
            const errorData = await response.json();
            errorMessage = errorData.detail || errorMessage;
        } catch (e) {
            // 无法解析错误响应
        }
        
        switch (response.status) {
            case 401:
                this.handleAuthError();
                break;
            case 403:
                this.showError('权限不足，请联系管理员');
                break;
            case 404:
                this.showError('请求的资源不存在');
                break;
            case 422:
                this.showError('请求参数错误: ' + errorMessage);
                break;
            case 500:
                this.showError('服务器内部错误，请稍后重试');
                break;
            default:
                this.showError(errorMessage);
        }
        
        throw new Error(errorMessage);
    }
    
    /**
     * 处理认证错误
     */
    handleAuthError() {
        console.log('认证失败，跳转到登录页');
        authManager.logout();
        
        // 如果不在登录页，则跳转
        if (window.location.pathname !== '/login') {
            window.location.href = '/login';
        }
    }
    
    /**
     * 显示错误消息
     * @param {string} message - 错误消息
     */
    showError(message) {
        console.error('API错误:', message);
        
        // 可以在这里集成通知组件
        if (typeof showNotification === 'function') {
            showNotification(message, 'error');
        } else {
            alert(message);
        }
    }
    
    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     * @returns {Promise<void>}
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // ==================== 查询相关API ====================
    
    /**
     * 日期筛选
     * @param {string} date - 日期 (YYYY-MM-DD)
     * @returns {Promise<Object>}
     */
    async filterData(date) {
        return this.request(`/api/v1/query/filter_data?date=${encodeURIComponent(date)}`);
    }
    
    /**
     * 客户订单查询
     * @param {string} customerName - 客户姓名
     * @returns {Promise<Object>}
     */
    async getCustomerOrders(customerName) {
        return this.request(`/api/v1/query/filter_orders_by_customer_name?customer_name=${encodeURIComponent(customerName)}`);
    }
    
    /**
     * 逾期订单查询
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>}
     */
    async getOverdueOrders(params = {}) {
        const {
            page = 1,
            limit = 10,
            forceRefresh = false,
            searchQuery = ''
        } = params;
        
        const queryParams = new URLSearchParams({
            page: page.toString(),
            limit: limit.toString(),
            force_refresh: forceRefresh.toString(),
            search_query: searchQuery
        });
        
        return this.request(`/api/v1/query/overdue?${queryParams}`);
    }
    
    // ==================== 统计相关API ====================
    
    /**
     * 客户汇总
     * @param {string} customerName - 客户姓名
     * @returns {Promise<Object>}
     */
    async getCustomerSummary(customerName) {
        return this.request(`/api/v1/stats/customer_summary?customer_name=${encodeURIComponent(customerName)}`);
    }
    
    /**
     * 数据汇总
     * @param {string} startDate - 开始日期
     * @param {string} endDate - 结束日期
     * @returns {Promise<Object>}
     */
    async getSummaryData(startDate, endDate) {
        return this.request(`/api/v1/stats/summary_data?start_date=${startDate}&end_date=${endDate}`);
    }
    
    /**
     * 订单汇总
     * @param {string} endDate - 结束日期
     * @returns {Promise<Object>}
     */
    async getOrderSummary(endDate) {
        return this.request(`/api/v1/stats/order_summary?end_date=${endDate}`);
    }
    
    // ==================== 兼容性方法 ====================
    
    /**
     * 兼容性方法：筛选数据
     * @param {string} date - 日期
     * @returns {Promise<Object>}
     */
    async filter(date) {
        return this.filterData(date);
    }
    
    /**
     * 兼容性方法：客户查询
     * @param {string} customerName - 客户姓名
     * @returns {Promise<Object>}
     */
    async customer(customerName) {
        return this.getCustomerOrders(customerName);
    }
    
    // ==================== 工具方法 ====================
    
    /**
     * 健康检查
     * @returns {Promise<Object>}
     */
    async healthCheck() {
        return this.request('/health');
    }
    
    /**
     * 获取API状态
     * @returns {Promise<Object>}
     */
    async getStatus() {
        return this.request('/api/v1/stats/status');
    }
    
    /**
     * 批量请求
     * @param {Array} requests - 请求数组
     * @returns {Promise<Array>}
     */
    async batchRequest(requests) {
        const promises = requests.map(req => this.request(req.url, req.options));
        return Promise.allSettled(promises);
    }
}

// 创建全局实例
const apiClient = new ApiClient();

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ApiClient;
}
