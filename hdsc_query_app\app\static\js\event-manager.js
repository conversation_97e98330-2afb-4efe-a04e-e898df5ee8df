/**
 * 事件管理器 - 统一管理所有事件绑定和解绑
 * 解决重复绑定和内存泄漏问题
 */

const EventManager = {
    // 存储所有事件绑定信息
    bindings: new Map(),
    
    // 绑定事件
    bind(element, event, handler, options = {}) {
        try {
            const key = this.generateKey(element, event);
            
            // 如果已经绑定过相同事件，先解绑
            if (this.bindings.has(key)) {
                this.unbind(element, event);
            }
            
            // 创建包装后的处理函数
            const wrappedHandler = this.wrapHandler(handler, options);
            
            // 绑定事件
            if (element.addEventListener) {
                element.addEventListener(event, wrappedHandler, options.capture || false);
            } else if (element.attachEvent) {
                // IE兼容
                element.attachEvent('on' + event, wrappedHandler);
            }
            
            // 存储绑定信息
            this.bindings.set(key, {
                element,
                event,
                handler: wrappedHandler,
                originalHandler: handler,
                options
            });
            
            console.log(`EventManager: 绑定事件 ${event} 到`, element);
            
        } catch (error) {
            console.error('EventManager: 绑定事件失败:', error);
        }
    },
    
    // 解绑事件
    unbind(element, event) {
        try {
            const key = this.generateKey(element, event);
            
            if (this.bindings.has(key)) {
                const binding = this.bindings.get(key);
                
                // 移除事件监听器
                if (element.removeEventListener) {
                    element.removeEventListener(event, binding.handler, binding.options.capture || false);
                } else if (element.detachEvent) {
                    // IE兼容
                    element.detachEvent('on' + event, binding.handler);
                }
                
                // 从存储中移除
                this.bindings.delete(key);
                
                console.log(`EventManager: 解绑事件 ${event} 从`, element);
            }
            
        } catch (error) {
            console.error('EventManager: 解绑事件失败:', error);
        }
    },
    
    // 解绑元素的所有事件
    unbindAll(element) {
        for (const [key, binding] of this.bindings) {
            if (binding.element === element) {
                this.unbind(element, binding.event);
            }
        }
    },
    
    // 清理所有事件绑定
    cleanup() {
        console.log('EventManager: 开始清理所有事件绑定');
        
        for (const [key, binding] of this.bindings) {
            try {
                if (binding.element.removeEventListener) {
                    binding.element.removeEventListener(
                        binding.event, 
                        binding.handler, 
                        binding.options.capture || false
                    );
                } else if (binding.element.detachEvent) {
                    binding.element.detachEvent('on' + binding.event, binding.handler);
                }
            } catch (error) {
                console.warn('EventManager: 清理事件时出错:', error);
            }
        }
        
        this.bindings.clear();
        console.log('EventManager: 事件清理完成');
    },
    
    // 生成唯一键
    generateKey(element, event) {
        // 使用元素ID或生成唯一标识
        const elementId = element.id || this.generateElementId(element);
        return `${elementId}_${event}`;
    },
    
    // 为元素生成唯一ID
    generateElementId(element) {
        if (!element._eventManagerId) {
            element._eventManagerId = `em_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        return element._eventManagerId;
    },
    
    // 包装处理函数，添加错误处理和性能监控
    wrapHandler(handler, options) {
        return function(event) {
            try {
                // 性能监控（可选）
                if (options.monitor) {
                    const start = performance.now();
                    const result = handler.call(this, event);
                    const end = performance.now();
                    
                    if (end - start > 16) { // 超过一帧的时间
                        console.warn(`EventManager: 事件处理耗时 ${end - start}ms`, event.type);
                    }
                    
                    return result;
                } else {
                    return handler.call(this, event);
                }
            } catch (error) {
                console.error('EventManager: 事件处理函数出错:', error);
                
                // 可选择是否阻止错误传播
                if (!options.suppressErrors) {
                    throw error;
                }
            }
        };
    },
    
    // 委托事件绑定
    delegate(container, selector, event, handler, options = {}) {
        const delegateHandler = function(e) {
            const target = e.target.closest(selector);
            if (target && container.contains(target)) {
                handler.call(target, e);
            }
        };
        
        this.bind(container, event, delegateHandler, options);
    },
    
    // 一次性事件绑定
    once(element, event, handler, options = {}) {
        const onceHandler = function(e) {
            handler.call(this, e);
            EventManager.unbind(element, event);
        };
        
        this.bind(element, event, onceHandler, options);
    },
    
    // 节流事件绑定
    throttle(element, event, handler, delay = 100, options = {}) {
        let lastTime = 0;
        
        const throttledHandler = function(e) {
            const now = Date.now();
            if (now - lastTime >= delay) {
                lastTime = now;
                handler.call(this, e);
            }
        };
        
        this.bind(element, event, throttledHandler, options);
    },
    
    // 防抖事件绑定
    debounce(element, event, handler, delay = 300, options = {}) {
        let timeoutId;
        
        const debouncedHandler = function(e) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => {
                handler.call(this, e);
            }, delay);
        };
        
        this.bind(element, event, debouncedHandler, options);
    }
};

// 页面卸载时自动清理
if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', function() {
        EventManager.cleanup();
    });
    
    // 导出到全局
    window.EventManager = EventManager;
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EventManager;
}