<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>太享查询_{{ version }} - 登录</title>
    <!-- Bootstrap CSS -->
    <link href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <style>
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
            background: #0c1445;
        }
        
        /* 动态背景 */
        .dynamic-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            overflow: hidden;
        }
        
        #tech-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        .login-container {
            max-width: 500px;
            margin: 100px auto;
            padding: 30px;
            background-color: rgba(255, 255, 255, 0.85);
            border-radius: 12px;
            box-shadow: 0 0 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }
        
        .login-container:hover {
            box-shadow: 0 0 50px rgba(63, 81, 181, 0.5);
        }
        
        .logo-container {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo-container img {
            max-width: 150px;
            height: auto;
        }
        .login-title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
            color: #3f51b5;
        }
        .form-control {
            height: 50px;
            font-size: 16px;
        }
        .btn-login {
            height: 50px;
            font-size: 18px;
            background-color: #3f51b5;
            border-color: #3f51b5;
            width: 100%;
        }
        .btn-login:hover {
            background-color: #303f9f;
            border-color: #303f9f;
        }
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-error {
            color: #dc3545;
            padding: 10px;
            background-color: #f8d7da;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .captcha-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        .captcha-image {
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            height: 50px;
            object-fit: cover;
        }
        .captcha-input {
            flex: 1;
        }
        .refresh-captcha {
            background: none;
            border: none;
            color: #3f51b5;
            cursor: pointer;
            font-size: 20px;
            padding: 5px;
        }
        @media (max-width: 576px) {
            .login-container {
                margin: 50px auto;
                padding: 20px;
            }
            .captcha-container {
                flex-direction: row;
                align-items: center;
                gap: 10px;
                width: 100%;
            }
            .captcha-input {
                width: 45%;
                min-width: 100px;
            }
            .captcha-image-container {
                display: flex;
                width: 50%;
                justify-content: space-between;
                align-items: center;
            }
            .captcha-image {
                height: 40px;
                max-width: 75%;
            }
            .refresh-captcha {
                padding: 5px 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 动态背景元素 -->
    <div class="dynamic-background">
        <canvas id="tech-canvas"></canvas>
    </div>
    
    <div class="container">
        <div class="login-container">
            <div class="logo-container">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="企业Logo" onerror="this.src='{{ url_for('static', filename='images/default_logo.png') }}'; this.onerror='';">
            </div>
            <h1 class="login-title">太享查询系统</h1>
            
            <!-- 显示错误消息 -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <div class="flash-messages">
                        {% for category, message in messages %}
                            <div class="flash-{{ category }}">{{ message }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endwith %}
            
            <form method="POST" action="{{ url_for('auth.login') }}">
                <div class="mb-3">
                    <label for="password" class="form-label">请输入密码：</label>
                    <input type="password" class="form-control" id="password" name="password" placeholder="请输入您的密码" required>
                </div>
                
                <!-- 验证码部分 -->
                <div class="mb-3">
                    <label for="captcha" class="form-label">验证码：</label>
                    <div class="captcha-container">
                        <div class="captcha-input">
                            <input type="text" class="form-control" id="captcha" name="captcha" placeholder="请输入验证码" required>
                        </div>
                        <div class="captcha-image-container">
                            <img id="captcha-image" class="captcha-image" src="{{ captcha_image }}" alt="验证码" title="点击刷新">
                            <button type="button" class="refresh-captcha" id="refresh-captcha" title="刷新验证码">
                                <i class="fa fa-refresh">↻</i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <button type="submit" class="btn btn-primary btn-login">登录</button>
                </div>
            </form>
            
            <div class="text-center mt-4">
                <small class="text-muted">版本: {{ version }}</small>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap Bundle with Popper -->
    <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    
    <!-- 验证码刷新脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const captchaImage = document.getElementById('captcha-image');
            const refreshButton = document.getElementById('refresh-captcha');
            
            function refreshCaptcha() {
                fetch('{{ url_for("auth.refresh_captcha") }}')
                    .then(response => response.json())
                    .then(data => {
                        captchaImage.src = data.captcha_image;
                    })
                    .catch(error => console.error('刷新验证码失败:', error));
            }
            
            // 点击图片或刷新按钮刷新验证码
            captchaImage.addEventListener('click', refreshCaptcha);
            refreshButton.addEventListener('click', refreshCaptcha);
            
            // 创建Canvas动态背景效果
            createCanvasBackground();
        });
        
        // 创建Canvas动态背景效果
        function createCanvasBackground() {
            const canvas = document.getElementById('tech-canvas');
            const ctx = canvas.getContext('2d');
            
            // 设置Canvas大小为窗口大小
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            
            // 响应窗口大小变化
            window.addEventListener('resize', function() {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
            });
            
            // 配置参数 - 可调整以获得最佳性能和视觉效果
            const config = {
                particleCount: 80,           // 粒子数量
                particleColor: '#4285f4',    // 粒子颜色
                lineColor: 'rgba(66, 133, 244, 0.15)', // 连接线颜色
                particleRadius: 2,           // 粒子半径
                lineWidth: 1,                // 线宽
                connectionDistance: 150,     // 连接距离
                moveSpeed: 0.5,              // 移动速度
                interactiveMode: true        // 交互模式
            };
            
            // 粒子数组
            const particles = [];
            
            // 鼠标位置
            let mouseX = null;
            let mouseY = null;
            
            // 粒子类
            class Particle {
                constructor() {
                    this.x = Math.random() * canvas.width;
                    this.y = Math.random() * canvas.height;
                    this.vx = (Math.random() - 0.5) * config.moveSpeed;
                    this.vy = (Math.random() - 0.5) * config.moveSpeed;
                    this.radius = Math.random() * config.particleRadius + 1;
                }
                
                // 更新粒子位置
                update() {
                    this.x += this.vx;
                    this.y += this.vy;
                    
                    // 边界处理 - 反弹效果
                    if (this.x < 0 || this.x > canvas.width) {
                        this.vx = -this.vx;
                    }
                    if (this.y < 0 || this.y > canvas.height) {
                        this.vy = -this.vy;
                    }
                }
                
                // 绘制粒子
                draw() {
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                    ctx.fillStyle = config.particleColor;
                    ctx.fill();
                }
                
                // 连接附近粒子
                connect() {
                    for (let i = 0; i < particles.length; i++) {
                        const particle = particles[i];
                        const distance = getDistance(this.x, this.y, particle.x, particle.y);
                        
                        if (distance < config.connectionDistance) {
                            // 距离越远，线条越透明
                            const opacity = 1 - (distance / config.connectionDistance);
                            const lineColor = config.lineColor.replace('0.15', opacity * 0.15);
                            
                            ctx.beginPath();
                            ctx.moveTo(this.x, this.y);
                            ctx.lineTo(particle.x, particle.y);
                            ctx.strokeStyle = lineColor;
                            ctx.lineWidth = config.lineWidth;
                            ctx.stroke();
                        }
                    }
                    
                    // 与鼠标交互
                    if (config.interactiveMode && mouseX !== null && mouseY !== null) {
                        const distance = getDistance(this.x, this.y, mouseX, mouseY);
                        if (distance < config.connectionDistance * 1.5) {
                            // 鼠标互动时产生更明显的连接
                            const opacity = 1 - (distance / (config.connectionDistance * 1.5));
                            
                            ctx.beginPath();
                            ctx.moveTo(this.x, this.y);
                            ctx.lineTo(mouseX, mouseY);
                            ctx.strokeStyle = `rgba(66, 133, 244, ${opacity * 0.3})`;
                            ctx.lineWidth = config.lineWidth;
                            ctx.stroke();
                        }
                    }
                }
            }
            
            // 计算两点距离
            function getDistance(x1, y1, x2, y2) {
                return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
            }
            
            // 初始化粒子
            function init() {
                for (let i = 0; i < config.particleCount; i++) {
                    particles.push(new Particle());
                }
            }
            
            // 添加鼠标交互
            canvas.addEventListener('mousemove', function(e) {
                mouseX = e.clientX;
                mouseY = e.clientY;
            });
            
            canvas.addEventListener('mouseleave', function() {
                mouseX = null;
                mouseY = null;
            });
            
            // 触摸设备支持
            canvas.addEventListener('touchmove', function(e) {
                if (e.touches.length > 0) {
                    mouseX = e.touches[0].clientX;
                    mouseY = e.touches[0].clientY;
                }
            });
            
            canvas.addEventListener('touchend', function() {
                mouseX = null;
                mouseY = null;
            });
            
            // 动画循环
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制科技风格背景
                drawTechBackground();
                
                // 更新并绘制所有粒子
                for (let i = 0; i < particles.length; i++) {
                    particles[i].update();
                    particles[i].draw();
                }
                
                // 连接粒子
                for (let i = 0; i < particles.length; i++) {
                    particles[i].connect();
                }
                
                requestAnimationFrame(animate);
            }
            
            // 绘制科技风格的背景元素
            function drawTechBackground() {
                // 绘制淡蓝色扫描线
                const scanLineCount = 100;
                const scanLineHeight = canvas.height / scanLineCount;
                
                ctx.fillStyle = 'rgba(66, 133, 244, 0.02)';
                for (let i = 0; i < scanLineCount; i += 2) {
                    ctx.fillRect(0, i * scanLineHeight, canvas.width, scanLineHeight);
                }
                
                // 绘制网格
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.03)';
                ctx.lineWidth = 0.5;
                
                const gridSize = 50;
                for (let x = 0; x < canvas.width; x += gridSize) {
                    ctx.beginPath();
                    ctx.moveTo(x, 0);
                    ctx.lineTo(x, canvas.height);
                    ctx.stroke();
                }
                
                for (let y = 0; y < canvas.height; y += gridSize) {
                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(canvas.width, y);
                    ctx.stroke();
                }
            }
            
            // 开始动画
            init();
            animate();
        }
    </script>
</body>
</html>