<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FinDataCore - 用户登录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h2 {
            color: #333;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .login-header p {
            color: #666;
            font-size: 14px;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-login:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            font-size: 14px;
        }
        
        .user-levels {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            font-size: 12px;
        }
        
        .user-levels h6 {
            color: #495057;
            margin-bottom: 10px;
        }
        
        .user-level {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h2>FinDataCore</h2>
            <p>金融数据查询系统</p>
        </div>
        
        <div id="alertContainer"></div>
        
        <form id="loginForm">
            <div class="mb-3">
                <label for="password" class="form-label">访问密码</label>
                <input type="password" class="form-control" id="password" name="password" 
                       placeholder="请输入访问密码" required autocomplete="current-password">
            </div>
            
            <button type="submit" class="btn btn-primary btn-login" id="loginBtn">
                <span class="btn-text">登录</span>
                <div class="loading-spinner"></div>
            </button>
        </form>
        
        <div class="user-levels">
            <h6>权限级别说明</h6>
            <div class="user-level">
                <span><strong>TT2024</strong> - 有限权限</span>
                <span class="text-muted">基础查询</span>
            </div>
            <div class="user-level">
                <span><strong>881017</strong> - 标准权限</span>
                <span class="text-muted">查询 + 导出</span>
            </div>
            <div class="user-level">
                <span><strong>Doolin</strong> - 完全权限</span>
                <span class="text-muted">所有功能</span>
            </div>
        </div>
    </div>

    <script src="auth-manager.js"></script>
    <script>
        // 页面加载时检查是否已登录
        document.addEventListener('DOMContentLoaded', () => {
            if (authManager.isAuthenticated()) {
                // 已登录，跳转到主页
                window.location.href = '/dashboard';
                return;
            }
        });

        // 登录表单处理
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const password = document.getElementById('password').value.trim();
            const loginBtn = document.getElementById('loginBtn');
            const btnText = loginBtn.querySelector('.btn-text');
            const spinner = loginBtn.querySelector('.loading-spinner');
            
            if (!password) {
                showAlert('请输入访问密码', 'warning');
                return;
            }
            
            // 显示加载状态
            setLoadingState(true);
            
            try {
                const result = await authManager.login(password);
                
                if (result.success) {
                    showAlert('登录成功！正在跳转...', 'success');
                    
                    // 延迟跳转，让用户看到成功消息
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 1000);
                } else {
                    showAlert(result.error || '登录失败', 'danger');
                    setLoadingState(false);
                }
            } catch (error) {
                console.error('登录异常:', error);
                showAlert('登录过程中发生错误，请稍后重试', 'danger');
                setLoadingState(false);
            }
        });
        
        /**
         * 设置加载状态
         * @param {boolean} loading - 是否加载中
         */
        function setLoadingState(loading) {
            const loginBtn = document.getElementById('loginBtn');
            const btnText = loginBtn.querySelector('.btn-text');
            const spinner = loginBtn.querySelector('.loading-spinner');
            const passwordInput = document.getElementById('password');
            
            if (loading) {
                loginBtn.disabled = true;
                btnText.style.display = 'none';
                spinner.style.display = 'inline-block';
                passwordInput.disabled = true;
            } else {
                loginBtn.disabled = false;
                btnText.style.display = 'inline';
                spinner.style.display = 'none';
                passwordInput.disabled = false;
                passwordInput.focus();
            }
        }
        
        /**
         * 显示警告消息
         * @param {string} message - 消息内容
         * @param {string} type - 消息类型 (success, danger, warning, info)
         */
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            
            // 清除现有警告
            alertContainer.innerHTML = '';
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            alertContainer.appendChild(alertDiv);
            
            // 自动隐藏成功和信息消息
            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    alertDiv.remove();
                }, 3000);
            }
        }
        
        // 密码输入框回车事件
        document.getElementById('password').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
        
        // 页面焦点到密码输入框
        document.getElementById('password').focus();
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
