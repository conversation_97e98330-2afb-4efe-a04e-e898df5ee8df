"""
认证相关API端点
"""
from datetime import timedelta
from fastapi import APIRouter, HTTPException, status, Depends
from fastapi.security import HTT<PERSON><PERSON>earer
from pydantic import BaseModel

from app.core.config import settings
from app.core.security import authenticate_user, create_access_token, Token, User
from app.core.dependencies import get_current_active_user

router = APIRouter()
security = HTTPBearer()

class LoginRequest(BaseModel):
    """登录请求模型"""
    password: str

class LoginResponse(BaseModel):
    """登录响应模型"""
    access_token: str
    token_type: str
    expires_in: int
    user_level: str
    permissions: list

@router.post("/login", response_model=LoginResponse)
async def login(login_data: LoginRequest):
    """
    用户登录
    
    Args:
        login_data: 登录数据
        
    Returns:
        登录响应，包含访问令牌
    """
    # 认证用户
    user_info = authenticate_user(login_data.password)
    if not user_info:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={
            "sub": user_info["username"],
            "user_level": user_info["user_level"]
        },
        expires_delta=access_token_expires
    )
    
    return LoginResponse(
        access_token=access_token,
        token_type="bearer",
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # 转换为秒
        user_level=user_info["user_level"],
        permissions=user_info["permissions"]
    )

@router.get("/me", response_model=User)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取当前用户信息
    
    Args:
        current_user: 当前用户
        
    Returns:
        用户信息
    """
    return current_user

@router.post("/logout")
async def logout():
    """
    用户登出
    
    Note:
        由于使用JWT，实际的登出需要在客户端删除token
        这个端点主要用于兼容性和日志记录
    """
    return {"message": "Successfully logged out"}

@router.get("/permissions")
async def get_user_permissions(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取用户权限信息
    
    Args:
        current_user: 当前用户
        
    Returns:
        权限信息
    """
    return {
        "username": current_user.username,
        "user_level": current_user.user_level,
        "permissions": current_user.permissions
    }
