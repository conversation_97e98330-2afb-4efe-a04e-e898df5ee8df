"""
API请求模型定义
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import date

class FilterDataRequest(BaseModel):
    """日期筛选请求模型"""
    date: str = Field(..., description="筛选日期，格式：YYYY-MM-DD")

class CustomerOrdersRequest(BaseModel):
    """客户订单查询请求模型"""
    customer_name: str = Field(..., description="客户姓名")

class OverdueOrdersRequest(BaseModel):
    """逾期订单查询请求模型"""
    page: int = Field(1, ge=1, description="页码，从1开始")
    limit: int = Field(10, ge=1, le=100, description="每页数据量，最大100")
    force_refresh: bool = Field(False, description="是否强制刷新缓存")
    search_query: str = Field("", description="搜索关键词")

class CustomerSummaryRequest(BaseModel):
    """客户汇总查询请求模型"""
    customer_name: str = Field(..., description="客户姓名")

class SummaryDataRequest(BaseModel):
    """数据汇总请求模型"""
    start_date: str = Field(..., description="开始日期，格式：YYYY-MM-DD")
    end_date: str = Field(..., description="结束日期，格式：YYYY-MM-DD")

class OrderSummaryRequest(BaseModel):
    """订单汇总请求模型"""
    end_date: str = Field(..., description="结束日期，格式：YYYY-MM-DD")

class OverdueSummaryRequest(BaseModel):
    """逾期订单汇总请求模型"""
    end_date: str = Field(..., description="结束日期，格式：YYYY-MM-DD")

class ApiProxyRequest(BaseModel):
    """API代理请求模型"""
    endpoint: str = Field(..., description="API端点")
    params: Optional[Dict[str, Any]] = Field(None, description="请求参数")
    data: Optional[Dict[str, Any]] = Field(None, description="请求数据")
