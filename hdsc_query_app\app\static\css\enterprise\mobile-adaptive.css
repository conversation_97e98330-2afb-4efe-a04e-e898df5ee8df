/*
 * 太享查询系统 - 移动端适配专用样式
 * Mobile Adaptive Styles for Enterprise Customer Summary
 * Version: 3.0
 * Last Updated: 2024-12-28
 */

/* ===============================================
   移动端优先设计令牌系统
   ============================================= */

:root {
    /* 移动端专用间距 */
    --mobile-spacing-xs: 0.25rem;
    --mobile-spacing-sm: 0.5rem;
    --mobile-spacing-md: 0.75rem;
    --mobile-spacing-lg: 1rem;
    --mobile-spacing-xl: 1.25rem;
    
    /* 移动端字体系统 */
    --mobile-font-xs: 0.75rem;
    --mobile-font-sm: 0.875rem;
    --mobile-font-md: 1rem;
    --mobile-font-lg: 1.125rem;
    --mobile-font-xl: 1.25rem;
    
    /* 移动端触摸目标 */
    --mobile-touch-target: 44px;
    --mobile-touch-target-sm: 36px;
    
    /* 移动端颜色系统 */
    --mobile-bg-primary: #f8fafc;
    --mobile-bg-card: #ffffff;
    --mobile-bg-hover: #f1f5f9;
    --mobile-border: #e2e8f0;
    --mobile-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --mobile-shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    
    /* 状态颜色 */
    --status-normal: #10b981;
    --status-overdue: #ef4444;
    --status-business: #3b82f6;
    --status-warning: #f59e0b;
    
    /* 金额颜色 */
    --amount-positive: #10b981;
    --amount-negative: #ef4444;
    --amount-neutral: #6b7280;
}

/* ===============================================
   移动端标签页导航系统
   ============================================= */

.mobile-tab-selector {
    position: relative;
    background: var(--gradient-primary);
    padding: var(--mobile-spacing-lg) var(--mobile-spacing-md) var(--mobile-spacing-md);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mobile-tab-select {
    display: none;
}

/* 标签页分类标题 */
.mobile-tab-category {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--mobile-font-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--mobile-spacing-sm);
    text-align: center;
}

.mobile-tab-indicator {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(255, 255, 255, 0.18);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: var(--radius-lg);
    padding: var(--mobile-spacing-lg) var(--mobile-spacing-md);
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    min-height: var(--mobile-touch-target);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.mobile-tab-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease-in-out;
}

.mobile-tab-indicator:hover::before {
    left: 100%;
}

.mobile-tab-indicator:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.mobile-tab-indicator .indicator-content {
    display: flex;
    align-items: center;
    gap: var(--mobile-spacing-sm);
    flex: 1;
}

.mobile-tab-indicator .indicator-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-sm);
    font-size: var(--mobile-font-md);
}

.mobile-tab-indicator .indicator-text {
    font-size: var(--mobile-font-md);
    color: white;
}

.mobile-tab-indicator .indicator-badge {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    font-size: var(--mobile-font-xs);
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
    margin-left: auto;
}

.mobile-tab-indicator .dropdown-arrow {
    font-size: var(--mobile-font-md);
    transition: transform var(--transition-normal);
    margin-left: var(--mobile-spacing-sm);
    width: 20px;
    text-align: center;
}

.mobile-tab-indicator.expanded .dropdown-arrow {
    transform: rotate(180deg);
}

/* 移动端标签页下拉菜单 */
.mobile-tab-dropdown {
    position: absolute;
    top: calc(100% + 4px);
    left: var(--mobile-spacing-md);
    right: var(--mobile-spacing-md);
    background: white;
    border: 1px solid var(--mobile-border);
    border-radius: var(--radius-lg);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    z-index: 1000;
    display: none;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.mobile-tab-dropdown.show {
    display: block;
    animation: slideDown 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-15px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.mobile-tab-option {
    display: flex;
    align-items: center;
    padding: var(--mobile-spacing-lg);
    border-bottom: 1px solid #f1f5f9;
    cursor: pointer;
    transition: all var(--transition-normal);
    min-height: var(--mobile-touch-target);
    position: relative;
}

.mobile-tab-option:last-child {
    border-bottom: none;
}

.mobile-tab-option:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transform: translateX(4px);
}

.mobile-tab-option.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    transform: translateX(6px);
}

.mobile-tab-option.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0 2px 2px 0;
}

.mobile-tab-option .option-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f1f5f9;
    border-radius: var(--radius-md);
    margin-right: var(--mobile-spacing-md);
    font-size: var(--mobile-font-md);
    color: var(--primary-color);
    transition: all var(--transition-normal);
}

.mobile-tab-option.active .option-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.mobile-tab-option .option-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.mobile-tab-option .option-title {
    font-size: var(--mobile-font-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.mobile-tab-option.active .option-title {
    color: white;
}

.mobile-tab-option .option-subtitle {
    font-size: var(--mobile-font-xs);
    color: var(--text-secondary);
    font-weight: 400;
}

.mobile-tab-option.active .option-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.mobile-tab-option .option-badge {
    background: #e2e8f0;
    color: var(--text-secondary);
    font-size: var(--mobile-font-xs);
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
    margin-left: var(--mobile-spacing-sm);
}

.mobile-tab-option.active .option-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* ===============================================
   移动端卡片视图系统
   ============================================= */

.mobile-card-view {
    padding: var(--mobile-spacing-lg);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    min-height: 300px;
    position: relative;
}

.mobile-card-view::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--mobile-border), transparent);
}

/* 卡片分组容器 */
.mobile-card-group {
    margin-bottom: var(--mobile-spacing-xl);
}

.mobile-card-group:last-child {
    margin-bottom: 0;
}

.mobile-card-group-title {
    font-size: var(--mobile-font-sm);
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--mobile-spacing-md);
    padding: 0 var(--mobile-spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--mobile-spacing-sm);
}

.mobile-card-group-title::before {
    content: '';
    width: 3px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 2px;
}

.mobile-data-card {
    background: var(--mobile-bg-card);
    border: 1px solid var(--mobile-border);
    border-radius: var(--radius-xl);
    margin-bottom: var(--mobile-spacing-lg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all var(--transition-normal);
    overflow: hidden;
    position: relative;
}

.mobile-data-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.mobile-data-card:hover {
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-4px);
    border-color: rgba(59, 130, 246, 0.2);
}

.mobile-data-card:last-child {
    margin-bottom: 0;
}

.mobile-data-card.priority-high {
    border-left: 4px solid #ef4444;
}

.mobile-data-card.priority-medium {
    border-left: 4px solid #f59e0b;
}

.mobile-data-card.priority-normal {
    border-left: 4px solid #10b981;
}

/* 卡片头部 */
.mobile-data-card .card-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: var(--mobile-spacing-xl) var(--mobile-spacing-lg) var(--mobile-spacing-md);
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(59, 130, 246, 0.05) 100%);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    position: relative;
    min-height: 64px;
}

.mobile-data-card .card-title {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: var(--mobile-font-lg);
    color: var(--text-primary);
    line-height: 1.2;
    flex: 1;
    margin-right: var(--mobile-spacing-md);
}

.mobile-data-card .card-title i {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    border-radius: var(--radius-md);
    margin-right: var(--mobile-spacing-md);
    font-size: var(--mobile-font-md);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.mobile-data-card .card-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--mobile-spacing-xs);
}

.mobile-data-card .card-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--mobile-spacing-xs);
}

.mobile-data-card .meta-date {
    font-size: var(--mobile-font-xs);
    color: var(--text-secondary);
    font-family: 'Monaco', monospace;
    background: rgba(255, 255, 255, 0.8);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.mobile-data-card .meta-flow {
    display: flex;
    align-items: center;
    gap: var(--mobile-spacing-xs);
    font-size: var(--mobile-font-xs);
    padding: var(--mobile-spacing-xs) var(--mobile-spacing-sm);
    border-radius: var(--radius-md);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mobile-data-card .meta-flow.flow-in {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.15) 100%);
    color: var(--status-normal);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.mobile-data-card .meta-flow.flow-out {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.15) 100%);
    color: var(--status-overdue);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* 订单详情卡片专用样式 */
.order-detail-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--mobile-spacing-lg);
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(59, 130, 246, 0.08) 100%);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    min-height: auto;
}

.order-detail-card .card-title-section {
    flex: 1;
    margin-right: var(--mobile-spacing-md);
}

.order-detail-card .card-title {
    display: flex;
    align-items: center;
    margin-bottom: var(--mobile-spacing-sm);
    font-weight: 700;
    font-size: var(--mobile-font-md);
    color: var(--text-primary);
}

.order-detail-card .card-title .title-text {
    margin-left: var(--mobile-spacing-sm);
    word-break: break-all;
    line-height: 1.3;
}

.order-detail-card .card-date {
    display: flex;
    align-items: center;
    font-size: var(--mobile-font-xs);
    color: var(--text-secondary);
    background: rgba(255, 255, 255, 0.8);
    padding: var(--mobile-spacing-xs) var(--mobile-spacing-sm);
    border-radius: var(--radius-md);
    border: 1px solid rgba(59, 130, 246, 0.1);
    width: fit-content;
}

.order-detail-card .card-date i {
    margin-right: var(--mobile-spacing-xs);
    color: var(--primary-color);
}

.order-detail-card .card-date .date-text {
    font-family: 'SF Mono', 'Monaco', monospace;
    font-weight: 500;
    white-space: nowrap;
}

.order-detail-card .card-status-section {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

/* 订单汇总区域 */
.order-summary-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--mobile-spacing-md);
    margin-bottom: var(--mobile-spacing-lg);
    padding: var(--mobile-spacing-md);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(226, 232, 240, 0.5);
}

.summary-item {
    text-align: center;
    padding: var(--mobile-spacing-md);
    border-radius: var(--radius-md);
    background: white;
    border: 1px solid rgba(226, 232, 240, 0.3);
    transition: all var(--transition-normal);
}

.summary-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.summary-item.primary {
    border-left: 3px solid var(--primary-color);
}

.summary-item.secondary {
    border-left: 3px solid #10b981;
}

.summary-label {
    font-size: var(--mobile-font-xs);
    color: var(--text-secondary);
    margin-bottom: var(--mobile-spacing-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-value {
    font-size: var(--mobile-font-lg);
    font-weight: 700;
    line-height: 1.2;
}

.summary-value.amount-primary {
    color: var(--primary-color);
    font-family: 'SF Mono', 'Monaco', monospace;
}

.summary-value.amount-secondary {
    color: #059669;
    font-family: 'SF Mono', 'Monaco', monospace;
}

/* 订单详情区域 */
.order-details-section {
    margin-top: var(--mobile-spacing-lg);
}

.detail-group {
    background: rgba(248, 250, 252, 0.5);
    border-radius: var(--radius-md);
    padding: var(--mobile-spacing-md);
    border: 1px solid rgba(226, 232, 240, 0.3);
}

.order-details-section .detail-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--mobile-spacing-md);
    margin-bottom: var(--mobile-spacing-md);
}

.order-details-section .detail-row:last-child {
    margin-bottom: 0;
}

.order-details-section .detail-item {
    background: white;
    padding: var(--mobile-spacing-md);
    border-radius: var(--radius-sm);
    border: 1px solid rgba(226, 232, 240, 0.4);
    text-align: center;
    transition: all var(--transition-normal);
}

.order-details-section .detail-item:hover {
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.order-details-section .detail-label {
    display: block;
    font-size: var(--mobile-font-xs);
    color: var(--text-secondary);
    margin-bottom: var(--mobile-spacing-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.order-details-section .detail-value {
    display: block;
    font-size: var(--mobile-font-sm);
    color: var(--text-primary);
    font-weight: 600;
    line-height: 1.3;
}

/* 卡片主体 */
.mobile-data-card .card-body {
    padding: var(--mobile-spacing-lg);
}

/* 数据网格布局 */
.card-data-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--mobile-spacing-lg);
    margin-bottom: var(--mobile-spacing-lg);
}

.card-data-grid .data-item {
    text-align: center;
}

.card-data-grid .data-item.highlight {
    grid-column: 1 / -1;
    background: var(--mobile-bg-hover);
    padding: var(--mobile-spacing-md);
    border-radius: var(--radius-md);
    border: 2px solid var(--primary-color);
}

.card-data-grid .data-label {
    display: block;
    font-size: var(--mobile-font-sm);
    color: var(--text-secondary);
    margin-bottom: var(--mobile-spacing-xs);
    font-weight: 500;
}

.card-data-grid .data-value {
    display: block;
    font-size: var(--mobile-font-lg);
    color: var(--text-primary);
    font-weight: 600;
}

.card-data-grid .data-value.amount {
    color: var(--primary-color);
    font-family: 'Monaco', monospace;
    font-size: var(--mobile-font-xl);
}

/* 主要信息布局 */
.card-main-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--mobile-spacing-lg);
    padding: var(--mobile-spacing-md);
    background: var(--mobile-bg-hover);
    border-radius: var(--radius-md);
}

.main-amount {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.amount-label {
    font-size: var(--mobile-font-xs);
    color: var(--text-secondary);
    margin-bottom: var(--mobile-spacing-xs);
    text-transform: uppercase;
    font-weight: 500;
}

.amount-value {
    font-size: var(--mobile-font-xl);
    font-weight: 700;
    color: var(--primary-color);
    font-family: 'Monaco', monospace;
}

.main-amount.amount-positive .amount-value {
    color: var(--amount-positive);
}

.main-amount.amount-negative .amount-value {
    color: var(--amount-negative);
}

.amount-symbol {
    font-size: var(--mobile-font-lg);
    margin-right: var(--mobile-spacing-xs);
}

.main-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.main-reference {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.reference-label {
    font-size: var(--mobile-font-xs);
    color: var(--text-secondary);
    margin-bottom: var(--mobile-spacing-xs);
}

.reference-value {
    font-size: var(--mobile-font-sm);
    color: var(--text-primary);
    font-weight: 500;
    font-family: 'Monaco', monospace;
}

/* 详细信息网格 */
.card-detail-grid {
    margin-top: var(--mobile-spacing-lg);
}

.detail-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--mobile-spacing-md);
    margin-bottom: var(--mobile-spacing-md);
}

.detail-row:last-child {
    margin-bottom: 0;
}

.detail-item {
    display: flex;
    flex-direction: column;
    padding: var(--mobile-spacing-sm);
    background: var(--mobile-bg-primary);
    border-radius: var(--radius-sm);
    border: 1px solid var(--mobile-border);
}

.detail-item.highlight {
    background: rgba(59, 130, 246, 0.05);
    border-color: var(--primary-color);
}

.detail-label {
    font-size: var(--mobile-font-xs);
    color: var(--text-secondary);
    margin-bottom: var(--mobile-spacing-xs);
    font-weight: 500;
    text-transform: uppercase;
}

.detail-value {
    font-size: var(--mobile-font-sm);
    color: var(--text-primary);
    font-weight: 600;
}

.detail-value.amount {
    color: var(--primary-color);
    font-family: 'Monaco', monospace;
}

/* 交易详情 */
.card-transaction-details {
    margin-top: var(--mobile-spacing-lg);
    padding-top: var(--mobile-spacing-lg);
    border-top: 1px solid var(--mobile-border);
}

.transaction-id,
.transaction-remarks {
    margin-bottom: var(--mobile-spacing-md);
}

.transaction-id:last-child,
.transaction-remarks:last-child {
    margin-bottom: 0;
}

/* 卡片操作按钮 */
.mobile-data-card .card-actions {
    display: flex;
    gap: var(--mobile-spacing-sm);
    padding: var(--mobile-spacing-md) var(--mobile-spacing-lg);
    background: var(--mobile-bg-primary);
    border-top: 1px solid var(--mobile-border);
}

.mobile-data-card .card-actions .btn {
    flex: 1;
    min-height: var(--mobile-touch-target-sm);
    font-size: var(--mobile-font-sm);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--mobile-spacing-xs);
    transition: all var(--transition-normal);
}

.mobile-data-card .card-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--mobile-shadow);
}

/* ===============================================
   状态徽章系统
   ============================================= */

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--mobile-spacing-xs);
    padding: var(--mobile-spacing-xs) var(--mobile-spacing-sm);
    font-size: var(--mobile-font-xs);
    font-weight: 500;
    border-radius: var(--radius-sm);
    text-align: center;
    white-space: nowrap;
    line-height: 1.2;
}

.status-badge.status-normal {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--status-normal);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.status-overdue {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--status-overdue);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-badge.status-business {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--status-business);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.status-badge.status-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--status-warning);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge i {
    font-size: var(--mobile-font-xs);
}

/* ===============================================
   移动端空状态
   ============================================= */

.mobile-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--mobile-spacing-xl) var(--mobile-spacing-lg);
    text-align: center;
    min-height: 200px;
    color: var(--text-secondary);
}

.mobile-empty-state i {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: var(--mobile-spacing-lg);
}

.mobile-empty-state h5 {
    font-size: var(--mobile-font-lg);
    color: var(--text-primary);
    margin-bottom: var(--mobile-spacing-sm);
    font-weight: 600;
}

.mobile-empty-state p {
    font-size: var(--mobile-font-sm);
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
}

/* ===============================================
   移动端表格适配
   ============================================= */

@media (max-width: 1199.98px) {
    .desktop-table-view {
        display: none !important;
    }
    
    .mobile-card-view {
        display: block !important;
    }
}

@media (min-width: 1200px) {
    .mobile-card-view {
        display: none !important;
    }
    
    .desktop-table-view {
        display: block !important;
    }
    
    .mobile-tab-selector {
        display: none !important;
    }
}

/* ===============================================
   移动端专用响应式断点
   ============================================= */

/* 超小屏幕 (小于 576px) */
@media (max-width: 575.98px) {
    .mobile-card-view {
        padding: var(--mobile-spacing-sm);
    }
    
    .mobile-data-card .card-header,
    .mobile-data-card .card-body,
    .mobile-data-card .card-actions {
        padding: var(--mobile-spacing-md);
    }
    
    .card-data-grid {
        grid-template-columns: 1fr;
        gap: var(--mobile-spacing-md);
    }
    
    .card-data-grid .data-item.highlight {
        grid-column: 1;
    }
    
    .card-main-info {
        flex-direction: column;
        align-items: stretch;
        gap: var(--mobile-spacing-md);
    }
    
    .main-status,
    .main-reference {
        align-items: flex-start;
    }
    
    .detail-row {
        grid-template-columns: 1fr;
        gap: var(--mobile-spacing-sm);
    }
    
    .mobile-data-card .card-actions {
        flex-direction: column;
    }
    
    .mobile-data-card .card-actions .btn {
        min-height: var(--mobile-touch-target);
    }
    
    .mobile-tab-indicator {
        padding: var(--mobile-spacing-sm) var(--mobile-spacing-md);
    }
    
    /* 订单详情卡片小屏幕优化 */
    .order-detail-card .card-header {
        flex-direction: column;
        align-items: stretch;
        padding: var(--mobile-spacing-md);
    }
    
    .order-detail-card .card-title-section {
        margin-right: 0;
        margin-bottom: var(--mobile-spacing-md);
    }
    
    .order-detail-card .card-title {
        margin-bottom: var(--mobile-spacing-sm);
    }
    
    .order-detail-card .card-title .title-text {
        font-size: var(--mobile-font-sm);
        line-height: 1.4;
    }
    
    .order-detail-card .card-date {
        align-self: flex-start;
    }
    
    .order-detail-card .card-status-section {
        align-items: flex-start;
    }
    
    .order-summary-section {
        grid-template-columns: 1fr;
        gap: var(--mobile-spacing-sm);
        padding: var(--mobile-spacing-sm);
    }
    
    .order-details-section .detail-row {
        grid-template-columns: 1fr;
        gap: var(--mobile-spacing-sm);
    }
    
    .order-details-section .detail-item {
        padding: var(--mobile-spacing-sm);
    }
    
    .summary-item {
        padding: var(--mobile-spacing-sm);
    }
    
    .summary-value {
        font-size: var(--mobile-font-md);
    }
}

/* 小屏幕 (576px - 767.98px) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .mobile-card-view {
        padding: var(--mobile-spacing-md);
    }
    
    .detail-row {
        grid-template-columns: 1fr 1fr;
    }
    
    .card-main-info {
        flex-direction: row;
    }
}

/* 中等屏幕 (768px - 1199.98px) */
@media (min-width: 768px) and (max-width: 1199.98px) {
    .mobile-card-view {
        padding: var(--mobile-spacing-lg);
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: var(--mobile-spacing-lg);
    }
    
    .mobile-data-card {
        margin-bottom: 0;
    }
    
    .card-data-grid {
        grid-template-columns: 1fr 1fr 1fr;
    }
    
    .card-data-grid .data-item.highlight {
        grid-column: 1 / -1;
    }
}

/* ===============================================
   移动端交互增强
   ============================================= */

/* 触摸反馈 */
.mobile-data-card,
.mobile-tab-indicator,
.mobile-tab-option {
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
    tap-highlight-color: rgba(59, 130, 246, 0.1);
}

/* 长按效果 */
.mobile-data-card:active {
    transform: scale(0.98);
    transition: transform 0.1s ease-in-out;
}

.mobile-tab-indicator:active,
.mobile-tab-option:active {
    background: rgba(255, 255, 255, 0.3);
}

.mobile-data-card .btn:active {
    transform: scale(0.95);
}

/* 焦点样式 */
.mobile-tab-indicator:focus,
.mobile-tab-option:focus,
.mobile-data-card .btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===============================================
   移动端卡片进入动画
   ============================================= */

.mobile-data-card {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.4s ease-out;
}

.mobile-data-card.visible {
    opacity: 1;
    transform: translateY(0);
}

.mobile-data-card:nth-child(2) {
    transition-delay: 0.1s;
}

.mobile-data-card:nth-child(3) {
    transition-delay: 0.2s;
}

.mobile-data-card:nth-child(4) {
    transition-delay: 0.3s;
}

.mobile-data-card:nth-child(5) {
    transition-delay: 0.4s;
}

.mobile-data-card.expanded {
    transform: scale(1.02);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 上下文菜单样式 */
.card-context-menu {
    animation: contextMenuShow 0.2s ease-out;
}

@keyframes contextMenuShow {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-5px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.context-menu-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f1f5f9;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
    font-size: 0.875rem;
    min-height: 44px;
}

.context-menu-item:last-child {
    border-bottom: none;
}

.context-menu-item:hover {
    background: #f8fafc;
}

.context-menu-item:active {
    background: #e2e8f0;
}

.context-menu-item i {
    margin-right: 0.75rem;
    width: 16px;
    color: #64748b;
}

.context-menu-item span {
    color: #1e293b;
    font-weight: 500;
}

/* ===============================================
   移动端动画优化
   ============================================= */

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    .mobile-data-card,
    .mobile-tab-indicator,
    .mobile-tab-dropdown,
    .mobile-data-card .btn {
        animation: none !important;
        transition: none !important;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .mobile-data-card {
        border-width: 2px;
    }
    
    .status-badge {
        border-width: 2px;
    }
    
    .mobile-tab-indicator {
        border-width: 2px;
    }
}

/* ===============================================
   移动端打印样式
   ============================================= */

@media print {
    .mobile-card-view {
        background: white !important;
        padding: 0 !important;
    }
    
    .mobile-data-card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
        margin-bottom: 1rem !important;
        break-inside: avoid;
    }
    
    .mobile-data-card .card-actions {
        display: none !important;
    }
    
    .mobile-tab-selector {
        display: none !important;
    }
    
    .status-badge {
        background: white !important;
        border: 1px solid #000 !important;
        color: #000 !important;
    }
}