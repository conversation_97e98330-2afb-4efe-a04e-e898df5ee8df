"""
数据验证工具
"""
import re
from datetime import datetime, date
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, validator
import logging

logger = logging.getLogger(__name__)

class DateValidator:
    """日期验证器"""
    
    @staticmethod
    def validate_date_string(date_str: str) -> bool:
        """验证日期字符串格式"""
        try:
            datetime.strptime(date_str, '%Y-%m-%d')
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_date_range(start_date: str, end_date: str) -> bool:
        """验证日期范围"""
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d')
            end = datetime.strptime(end_date, '%Y-%m-%d')
            return start <= end
        except ValueError:
            return False
    
    @staticmethod
    def is_future_date(date_str: str) -> bool:
        """检查是否为未来日期"""
        try:
            target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            return target_date > date.today()
        except ValueError:
            return False

class CustomerValidator:
    """客户数据验证器"""
    
    @staticmethod
    def validate_customer_name(name: str) -> bool:
        """验证客户姓名"""
        if not isinstance(name, str):
            return False
        
        # 移除空白字符
        name = name.strip()
        
        # 检查长度
        if len(name) < 2 or len(name) > 50:
            return False
        
        # 检查是否包含有效字符（中文、英文、数字）
        pattern = r'^[\u4e00-\u9fff\w\s\-\.]+$'
        return bool(re.match(pattern, name))
    
    @staticmethod
    def validate_phone_number(phone: str) -> bool:
        """验证手机号"""
        if not isinstance(phone, str):
            return False
        
        # 移除空格和特殊字符
        phone = re.sub(r'[\s\-\(\)]', '', phone)
        
        # 中国手机号格式
        pattern = r'^1[3-9]\d{9}$'
        return bool(re.match(pattern, phone))

class OrderValidator:
    """订单数据验证器"""
    
    @staticmethod
    def validate_order_number(order_no: str) -> bool:
        """验证订单号格式"""
        if not isinstance(order_no, str):
            return False
        
        # 订单号应该是字母数字组合，长度在6-30之间
        pattern = r'^[A-Za-z0-9\-_]{6,30}$'
        return bool(re.match(pattern, order_no))
    
    @staticmethod
    def validate_amount(amount: Union[str, int, float]) -> bool:
        """验证金额格式"""
        try:
            amount_float = float(amount)
            return amount_float >= 0
        except (ValueError, TypeError):
            return False

class PaginationValidator:
    """分页参数验证器"""
    
    @staticmethod
    def validate_page(page: int) -> bool:
        """验证页码"""
        return isinstance(page, int) and page >= 1
    
    @staticmethod
    def validate_limit(limit: int, max_limit: int = 100) -> bool:
        """验证每页数量"""
        return isinstance(limit, int) and 1 <= limit <= max_limit

class ApiValidator:
    """API参数验证器"""
    
    @staticmethod
    def validate_api_key(api_key: str) -> bool:
        """验证API密钥"""
        if not isinstance(api_key, str):
            return False
        
        # API密钥应该是非空字符串
        return len(api_key.strip()) > 0
    
    @staticmethod
    def validate_endpoint(endpoint: str) -> bool:
        """验证API端点"""
        if not isinstance(endpoint, str):
            return False
        
        # 端点应该是有效的路径格式
        pattern = r'^[a-zA-Z0-9_\-\/]+$'
        return bool(re.match(pattern, endpoint))

class DataValidator:
    """通用数据验证器"""
    
    @staticmethod
    def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> List[str]:
        """
        验证必需字段
        
        Args:
            data: 数据字典
            required_fields: 必需字段列表
            
        Returns:
            缺失字段列表
        """
        missing_fields = []
        for field in required_fields:
            if field not in data or data[field] is None or data[field] == '':
                missing_fields.append(field)
        return missing_fields
    
    @staticmethod
    def validate_data_types(data: Dict[str, Any], type_mapping: Dict[str, type]) -> List[str]:
        """
        验证数据类型
        
        Args:
            data: 数据字典
            type_mapping: 字段类型映射
            
        Returns:
            类型错误字段列表
        """
        type_errors = []
        for field, expected_type in type_mapping.items():
            if field in data and not isinstance(data[field], expected_type):
                type_errors.append(f"{field} should be {expected_type.__name__}")
        return type_errors
    
    @staticmethod
    def sanitize_string(text: str) -> str:
        """
        清理字符串，防止注入攻击
        
        Args:
            text: 原始字符串
            
        Returns:
            清理后的字符串
        """
        if not isinstance(text, str):
            return str(text)
        
        # 移除潜在的危险字符
        dangerous_chars = ['<', '>', '"', "'", '&', ';', '(', ')', '|', '`']
        for char in dangerous_chars:
            text = text.replace(char, '')
        
        # 限制长度
        return text[:1000]
    
    @staticmethod
    def validate_search_query(query: str) -> bool:
        """验证搜索查询"""
        if not isinstance(query, str):
            return False
        
        # 搜索查询长度限制
        if len(query) > 100:
            return False
        
        # 检查是否包含危险字符
        dangerous_patterns = [
            r'<script',
            r'javascript:',
            r'on\w+\s*=',
            r'eval\s*\(',
            r'expression\s*\('
        ]
        
        query_lower = query.lower()
        for pattern in dangerous_patterns:
            if re.search(pattern, query_lower):
                return False
        
        return True

# Pydantic验证器示例
class FilterDataValidator(BaseModel):
    """筛选数据验证模型"""
    date: str
    
    @validator('date')
    def validate_date_format(cls, v):
        if not DateValidator.validate_date_string(v):
            raise ValueError('日期格式必须为YYYY-MM-DD')
        return v

class CustomerOrdersValidator(BaseModel):
    """客户订单验证模型"""
    customer_name: str
    
    @validator('customer_name')
    def validate_customer_name(cls, v):
        if not CustomerValidator.validate_customer_name(v):
            raise ValueError('客户姓名格式无效')
        return v

class OverdueOrdersValidator(BaseModel):
    """逾期订单验证模型"""
    page: int = 1
    limit: int = 10
    force_refresh: bool = False
    search_query: str = ""
    
    @validator('page')
    def validate_page(cls, v):
        if not PaginationValidator.validate_page(v):
            raise ValueError('页码必须大于0')
        return v
    
    @validator('limit')
    def validate_limit(cls, v):
        if not PaginationValidator.validate_limit(v):
            raise ValueError('每页数量必须在1-100之间')
        return v
    
    @validator('search_query')
    def validate_search_query(cls, v):
        if not DataValidator.validate_search_query(v):
            raise ValueError('搜索查询包含无效字符')
        return v

class SummaryDataValidator(BaseModel):
    """汇总数据验证模型"""
    start_date: str
    end_date: str
    
    @validator('start_date', 'end_date')
    def validate_date_format(cls, v):
        if not DateValidator.validate_date_string(v):
            raise ValueError('日期格式必须为YYYY-MM-DD')
        return v
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        if 'start_date' in values:
            if not DateValidator.validate_date_range(values['start_date'], v):
                raise ValueError('结束日期必须大于等于开始日期')
        return v
