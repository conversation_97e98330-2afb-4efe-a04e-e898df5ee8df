# FinDataCore API 使用指南

## 概述

FinDataCore 提供了一套完整的 RESTful API，用于金融数据的查询、统计和管理。所有API都基于FastAPI框架构建，提供自动生成的OpenAPI文档。

## 基础信息

- **基础URL**: `http://localhost:8000`
- **API版本**: v1
- **文档地址**: `http://localhost:8000/docs`
- **ReDoc文档**: `http://localhost:8000/redoc`

## 认证

### JWT Token认证

所有API端点都需要JWT Token认证（除了登录和健康检查端点）。

#### 获取Token

```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "password": "TT2024"
}
```

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 28800,
  "user_level": "limited",
  "permissions": ["read"]
}
```

#### 使用Token

在请求头中包含Authorization字段：

```http
Authorization: Bearer <your_token_here>
```

### 用户权限级别

| 密码 | 权限级别 | 权限 | 说明 |
|------|----------|------|------|
| TT2024 | limited | read | 基础查询权限 |
| 881017 | standard | read, export | 标准权限，可导出数据 |
| Doolin | full | read, export, admin | 完全权限，可访问所有功能 |

## API端点

### 认证相关

#### 用户登录
```http
POST /api/v1/auth/login
```

#### 获取当前用户信息
```http
GET /api/v1/auth/me
```

#### 用户登出
```http
POST /api/v1/auth/logout
```

#### 获取用户权限
```http
GET /api/v1/auth/permissions
```

### 查询相关

#### 日期筛选
```http
GET /api/v1/query/filter_data?date=2025-01-15
```

**参数**:
- `date` (必需): 筛选日期，格式 YYYY-MM-DD

**响应示例**:
```json
{
  "success": true,
  "results": [
    {
      "id": 1,
      "customer_name": "张三",
      "order_date": "2025-01-15",
      "amount": 10000
    }
  ],
  "columns": ["id", "customer_name", "order_date", "amount"],
  "total": 1,
  "timestamp": "2025-01-15T10:00:00Z"
}
```

#### 客户订单查询
```http
GET /api/v1/query/filter_orders_by_customer_name?customer_name=张三
```

**参数**:
- `customer_name` (必需): 客户姓名

#### 逾期订单查询
```http
GET /api/v1/query/overdue?page=1&limit=10&search_query=张三
```

**参数**:
- `page` (可选): 页码，默认1
- `limit` (可选): 每页数量，默认10，最大100
- `force_refresh` (可选): 是否强制刷新缓存，默认false
- `search_query` (可选): 搜索关键词

**响应示例**:
```json
{
  "success": true,
  "results": [...],
  "columns": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "pages": 5
  },
  "cache_info": {
    "last_update": "2025-01-15T10:00:00Z",
    "next_update": null
  },
  "timestamp": "2025-01-15T10:00:00Z"
}
```

### 统计相关

#### 客户汇总 (需要标准权限)
```http
GET /api/v1/stats/customer_summary?customer_name=张三
```

#### 数据汇总
```http
GET /api/v1/stats/summary_data?start_date=2025-01-01&end_date=2025-01-31
```

#### 订单汇总
```http
GET /api/v1/stats/order_summary?end_date=2025-01-31
```

### 兼容性端点

为了保持与原Flask版本的兼容性，提供了以下别名端点：

- `GET /api/v1/query/filter` → `GET /api/v1/query/filter_data`
- `GET /api/v1/query/customer` → `GET /api/v1/query/filter_orders_by_customer_name`
- `GET /api/v1/stats/summary` → `GET /api/v1/stats/summary_data`

## 错误处理

### HTTP状态码

- `200`: 成功
- `400`: 请求错误（如参数无效）
- `401`: 未认证（Token无效或过期）
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 验证错误（参数格式错误）
- `500`: 服务器内部错误

### 错误响应格式

```json
{
  "detail": "错误描述信息"
}
```

## 使用示例

### Python示例

```python
import httpx
import asyncio

async def main():
    base_url = "http://localhost:8000"
    
    # 登录获取token
    async with httpx.AsyncClient() as client:
        login_response = await client.post(
            f"{base_url}/api/v1/auth/login",
            json={"password": "TT2024"}
        )
        token = login_response.json()["access_token"]
        
        # 使用token查询数据
        headers = {"Authorization": f"Bearer {token}"}
        response = await client.get(
            f"{base_url}/api/v1/query/filter_data",
            params={"date": "2025-01-15"},
            headers=headers
        )
        
        data = response.json()
        print(f"查询到 {data['total']} 条记录")

asyncio.run(main())
```

### JavaScript示例

```javascript
// 登录获取token
const loginResponse = await fetch('/api/v1/auth/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        password: 'TT2024'
    })
});

const loginData = await loginResponse.json();
const token = loginData.access_token;

// 使用token查询数据
const queryResponse = await fetch('/api/v1/query/filter_data?date=2025-01-15', {
    headers: {
        'Authorization': `Bearer ${token}`
    }
});

const queryData = await queryResponse.json();
console.log(`查询到 ${queryData.total} 条记录`);
```

### cURL示例

```bash
# 登录获取token
TOKEN=$(curl -s -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"password": "TT2024"}' | jq -r '.access_token')

# 使用token查询数据
curl -H "Authorization: Bearer $TOKEN" \
  "http://localhost:8000/api/v1/query/filter_data?date=2025-01-15"
```

## 性能优化

### 缓存机制

API使用多层缓存机制来提高性能：

- **内存缓存**: 用于频繁访问的数据
- **Redis缓存**: 用于跨请求的数据共享
- **智能缓存**: 根据数据类型自动选择缓存策略

### 缓存时间

| 数据类型 | 缓存时间 |
|----------|----------|
| 筛选数据 | 5分钟 |
| 逾期订单 | 10分钟 |
| 客户订单 | 10分钟 |
| 数据汇总 | 30分钟 |
| 客户汇总 | 10分钟 |

### 分页建议

- 使用合适的页面大小（建议10-50条）
- 避免一次性获取大量数据
- 使用搜索功能缩小结果范围

## 监控和调试

### 健康检查

```http
GET /health
```

### API状态

```http
GET /api/v1/stats/status
```

### 性能监控

系统提供了内置的性能监控功能，可以通过日志查看API调用统计信息。

## 常见问题

### Q: Token过期了怎么办？
A: 重新调用登录接口获取新的token。Token默认有效期为8小时。

### Q: 如何提高查询性能？
A: 使用合适的日期范围、启用缓存、使用分页查询。

### Q: 支持批量操作吗？
A: 目前不支持批量操作，建议使用异步方式处理多个请求。

### Q: 如何处理大量数据？
A: 使用分页查询，配合搜索和筛选功能来减少数据量。
