/**
 * 现代化逾期订单页面增强功能
 * 提供额外的交互功能和性能优化
 */

// 懒加载功能
const LazyLoader = {
    // 观察器实例
    observer: null,
    
    // 初始化懒加载
    init() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver(this.handleIntersection.bind(this), {
                rootMargin: '50px 0px',
                threshold: 0.1
            });
            
            // 观察所有卡片
            document.querySelectorAll('.data-card').forEach(card => {
                this.observer.observe(card);
            });
        }
    },
    
    // 处理元素进入视口
    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const card = entry.target;
                this.loadCardContent(card);
                this.observer.unobserve(card);
            }
        });
    },
    
    // 加载卡片内容
    loadCardContent(card) {
        // 添加加载完成的类
        card.classList.add('loaded');
        
        // 可以在这里添加更多的懒加载逻辑
        // 比如加载图片、额外数据等
    }
};

// 虚拟滚动功能（用于大量数据）
const VirtualScroll = {
    // 配置
    config: {
        itemHeight: 120, // 每个卡片的高度
        bufferSize: 5,   // 缓冲区大小
        containerHeight: 0
    },
    
    // 状态
    state: {
        scrollTop: 0,
        startIndex: 0,
        endIndex: 0,
        visibleItems: []
    },
    
    // 初始化虚拟滚动
    init(container, items) {
        if (!container || !items.length) return;
        
        this.container = container;
        this.items = items;
        this.config.containerHeight = container.clientHeight;
        
        this.setupScrollListener();
        this.updateVisibleItems();
    },
    
    // 设置滚动监听器
    setupScrollListener() {
        this.container.addEventListener('scroll', this.handleScroll.bind(this));
    },
    
    // 处理滚动事件
    handleScroll() {
        this.state.scrollTop = this.container.scrollTop;
        this.updateVisibleItems();
    },
    
    // 更新可见项目
    updateVisibleItems() {
        const { itemHeight, bufferSize, containerHeight } = this.config;
        const { scrollTop } = this.state;
        
        const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - bufferSize);
        const endIndex = Math.min(
            this.items.length - 1,
            Math.floor((scrollTop + containerHeight) / itemHeight) + bufferSize
        );
        
        this.state.startIndex = startIndex;
        this.state.endIndex = endIndex;
        this.state.visibleItems = this.items.slice(startIndex, endIndex + 1);
        
        this.renderVisibleItems();
    },
    
    // 渲染可见项目
    renderVisibleItems() {
        // 这里可以实现具体的渲染逻辑
        // 根据实际需求来实现
    }
};

// 搜索增强功能
const SearchEnhancer = {
    // 搜索历史
    searchHistory: [],
    
    // 初始化搜索增强
    init() {
        this.loadSearchHistory();
        this.setupSearchSuggestions();
        this.setupSearchShortcuts();
    },
    
    // 加载搜索历史
    loadSearchHistory() {
        const history = localStorage.getItem('overdue_search_history');
        if (history) {
            this.searchHistory = JSON.parse(history);
        }
    },
    
    // 保存搜索历史
    saveSearchHistory(query) {
        if (query && !this.searchHistory.includes(query)) {
            this.searchHistory.unshift(query);
            this.searchHistory = this.searchHistory.slice(0, 10); // 只保留最近10个
            localStorage.setItem('overdue_search_history', JSON.stringify(this.searchHistory));
        }
    },
    
    // 设置搜索建议
    setupSearchSuggestions() {
        const searchInput = document.getElementById('tableSearch');
        if (!searchInput) return;
        
        // 创建建议下拉框
        const suggestions = document.createElement('div');
        suggestions.className = 'search-suggestions';
        suggestions.style.display = 'none';
        searchInput.parentNode.appendChild(suggestions);
        
        // 监听输入事件
        searchInput.addEventListener('input', (e) => {
            this.showSuggestions(e.target.value, suggestions);
        });
        
        // 监听焦点事件
        searchInput.addEventListener('focus', () => {
            if (this.searchHistory.length > 0) {
                this.showSuggestions('', suggestions);
            }
        });
        
        // 点击外部隐藏建议
        document.addEventListener('click', (e) => {
            if (!searchInput.contains(e.target) && !suggestions.contains(e.target)) {
                suggestions.style.display = 'none';
            }
        });
    },
    
    // 显示搜索建议
    showSuggestions(query, container) {
        const filteredHistory = this.searchHistory.filter(item => 
            item.toLowerCase().includes(query.toLowerCase())
        );
        
        if (filteredHistory.length === 0) {
            container.style.display = 'none';
            return;
        }
        
        container.innerHTML = filteredHistory.map(item => 
            `<div class="suggestion-item" data-query="${item}">${item}</div>`
        ).join('');
        
        container.style.display = 'block';
        
        // 添加点击事件
        container.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                document.getElementById('tableSearch').value = item.dataset.query;
                container.style.display = 'none';
            });
        });
    },
    
    // 设置搜索快捷键
    setupSearchShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K 聚焦搜索框
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.getElementById('tableSearch');
                if (searchInput) {
                    searchInput.focus();
                    searchInput.select();
                }
            }
        });
    }
};

// 性能监控
const PerformanceMonitor = {
    // 性能指标
    metrics: {
        loadTime: 0,
        renderTime: 0,
        searchTime: 0
    },
    
    // 开始监控
    startMonitoring() {
        this.metrics.loadTime = performance.now();
        
        // 监控页面加载完成
        window.addEventListener('load', () => {
            this.metrics.loadTime = performance.now() - this.metrics.loadTime;
            this.logMetrics();
        });
    },
    
    // 记录渲染时间
    recordRenderTime(startTime) {
        this.metrics.renderTime = performance.now() - startTime;
    },
    
    // 记录搜索时间
    recordSearchTime(startTime) {
        this.metrics.searchTime = performance.now() - startTime;
    },
    
    // 输出性能指标
    logMetrics() {
        console.group('📊 逾期订单页面性能指标');
        console.log(`页面加载时间: ${this.metrics.loadTime.toFixed(2)}ms`);
        console.log(`渲染时间: ${this.metrics.renderTime.toFixed(2)}ms`);
        console.log(`搜索时间: ${this.metrics.searchTime.toFixed(2)}ms`);
        console.groupEnd();
    }
};

// 无障碍功能增强
const AccessibilityEnhancer = {
    // 初始化无障碍功能
    init() {
        this.setupKeyboardNavigation();
        this.setupScreenReaderSupport();
        this.setupFocusManagement();
    },
    
    // 设置键盘导航
    setupKeyboardNavigation() {
        // 表格行键盘导航
        document.querySelectorAll('.table-row, .data-card').forEach((item, index) => {
            item.setAttribute('tabindex', '0');
            item.setAttribute('role', 'button');
            item.setAttribute('aria-label', `订单记录 ${index + 1}`);
            
            item.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    item.click();
                }
            });
        });
    },
    
    // 设置屏幕阅读器支持
    setupScreenReaderSupport() {
        // 添加ARIA标签
        const table = document.querySelector('.modern-data-table');
        if (table) {
            table.setAttribute('role', 'table');
            table.setAttribute('aria-label', '逾期订单数据表格');
        }
        
        // 为状态卡片添加描述
        document.querySelectorAll('.status-card').forEach(card => {
            const label = card.querySelector('.card-label').textContent;
            const value = card.querySelector('.card-value').textContent;
            card.setAttribute('aria-label', `${label}: ${value}`);
        });
    },
    
    // 设置焦点管理
    setupFocusManagement() {
        // 跳过链接
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.textContent = '跳转到主要内容';
        skipLink.className = 'skip-link';
        document.body.insertBefore(skipLink, document.body.firstChild);
        
        // 主要内容区域
        const mainContent = document.querySelector('.data-container');
        if (mainContent) {
            mainContent.id = 'main-content';
            mainContent.setAttribute('tabindex', '-1');
        }
    }
};

// 初始化所有增强功能
document.addEventListener('DOMContentLoaded', function() {
    // 启动性能监控
    PerformanceMonitor.startMonitoring();
    
    // 初始化懒加载
    LazyLoader.init();
    
    // 初始化搜索增强
    SearchEnhancer.init();
    
    // 初始化无障碍功能
    AccessibilityEnhancer.init();
    
    // 如果数据量大，启用虚拟滚动
    const cardsContainer = document.querySelector('.cards-container');
    const cards = document.querySelectorAll('.data-card');
    if (cards.length > 100) {
        VirtualScroll.init(cardsContainer, Array.from(cards));
    }
    
    console.log('🚀 逾期订单页面增强功能已启用');
});

// 导出功能供全局使用
window.OverdueOrdersEnhancer = {
    LazyLoader,
    VirtualScroll,
    SearchEnhancer,
    PerformanceMonitor,
    AccessibilityEnhancer
};
