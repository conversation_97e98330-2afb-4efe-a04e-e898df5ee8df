# FinDataCore - 金融数据查询系统 FastAPI版

> 🚀 **企业级金融数据查询展示系统** - 基于FastAPI的现代化重构版本

FinDataCore是原hdsc_query_app项目的FastAPI重构版本，专为金融机构设计，提供高性能的数据查询、分析和可视化功能。

## ✨ 核心特性

### 🔐 现代化认证系统
- **JWT Token认证**：基于JSON Web Token的无状态认证
- **三级权限体系**：有限权限、标准权限、完全权限
- **安全性增强**：密码哈希、令牌过期管理

### 📊 高性能API
- **异步处理**：基于FastAPI的异步特性，提升并发性能
- **自动文档**：OpenAPI/Swagger自动生成API文档
- **类型安全**：Pydantic模型确保数据验证和类型安全
- **智能缓存**：多层缓存策略，提升响应速度

### 🔄 完全兼容
- **API兼容**：保持与原Flask版本相同的API接口
- **前端无缝**：前端代码无需修改，直接切换
- **数据库复用**：使用现有PostgreSQL数据库

## 🚀 快速开始

### 开发环境部署

```bash
# 克隆项目
cd FinDataCore

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Docker部署

```bash
# 构建并启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f findatacore
```

**访问地址**：
- API服务：http://localhost:8000
- API文档：http://localhost:8000/docs
- ReDoc文档：http://localhost:8000/redoc

## 🏗️ 技术架构

### 后端技术栈
- **框架**：FastAPI 0.104+ + Uvicorn
- **认证**：JWT + OAuth2 + Passlib
- **数据验证**：Pydantic V2
- **异步HTTP**：httpx + asyncio
- **数据处理**：Pandas + NumPy
- **缓存**：内存缓存 + Redis（可选）
- **数据库**：PostgreSQL + SQLAlchemy

### 项目结构
```
FinDataCore/
├── app/
│   ├── main.py                 # FastAPI应用入口
│   ├── core/
│   │   ├── config.py          # 配置管理
│   │   ├── security.py        # JWT认证
│   │   └── dependencies.py    # 依赖注入
│   ├── api/
│   │   └── v1/
│   │       ├── endpoints/     # API端点
│   │       └── api.py         # 路由汇总
│   ├── services/              # 业务逻辑层
│   ├── models/                # 数据模型
│   └── utils/                 # 工具函数
├── requirements.txt
├── Dockerfile
├── docker-compose.yml
└── .env
```

## 🔑 用户权限

| 权限级别 | 访问密码 | 功能权限 |
|----------|----------|----------|
| **有限权限** | `TT2024` | 基础查询、数据浏览 |
| **标准权限** | `881017` | 数据导出、报表生成 |
| **完全权限** | `Doolin` | 系统管理、高级功能 |

## 📖 API文档

### 认证接口
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/auth/me` - 获取当前用户信息
- `POST /api/v1/auth/logout` - 用户登出

### 查询接口
- `GET /api/v1/query/filter_data` - 日期筛选
- `GET /api/v1/query/customer_orders` - 客户订单查询
- `GET /api/v1/query/overdue_orders` - 逾期订单查询

### 统计接口
- `GET /api/v1/stats/customer_summary` - 客户汇总
- `GET /api/v1/stats/order_summary` - 订单汇总
- `GET /api/v1/stats/summary_data` - 数据汇总

## 🔧 配置说明

### 环境变量
```bash
# 数据库配置
DATABASE_URI=******************************************************/flask_db

# API配置
API_KEY=lxw8025031
API_BASE_URL=http://*************:5000

# 安全配置
SECRET_KEY=your-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=480
```

## 🧪 测试

```bash
# 运行测试
pytest

# 测试覆盖率
pytest --cov=app

# API测试
curl -X POST "http://localhost:8000/api/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"password": "TT2024"}'
```

## 📊 性能优势

| 指标 | Flask版本 | FastAPI版本 | 改善幅度 |
|------|-----------|-------------|----------|
| **并发处理** | 同步 | 异步 | ↑ 300% |
| **API文档** | 手动维护 | 自动生成 | ↑ 100% |
| **类型安全** | 无 | Pydantic | ↑ 100% |
| **开发效率** | 中等 | 高 | ↑ 50% |

## 🔄 迁移指南

### 从Flask版本迁移
1. **保持API兼容**：所有接口路径和参数保持不变
2. **认证方式变更**：从Session改为JWT Token
3. **前端适配**：仅需修改认证逻辑，其他无需变更

### 前端适配示例
```javascript
// 原Flask版本 - Session认证
// 无需特殊处理

// FastAPI版本 - JWT认证
const token = localStorage.getItem('access_token');
fetch('/api/v1/query/filter_data', {
    headers: {
        'Authorization': `Bearer ${token}`
    }
});
```

## 🚀 部署选项

### 🐳 Docker部署 (推荐)
```bash
docker-compose up -d
```

### 🖥️ 传统部署
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### ☁️ 云平台部署
- **支持平台**：AWS、阿里云、腾讯云、Azure
- **容器支持**：Docker、Kubernetes
- **负载均衡**：Nginx、Traefik

## 📞 技术支持

### 联系方式
- **技术支持**：系统管理员
- **问题反馈**：通过GitHub Issues
- **API文档**：http://localhost:8000/docs

## 📜 版本历史

### v1.0.0 (当前版本)
- ✅ **FastAPI重构** - 完整的框架迁移
- ✅ **JWT认证** - 现代化认证系统
- ✅ **异步处理** - 高性能并发支持
- ✅ **自动文档** - OpenAPI/Swagger集成
- ✅ **类型安全** - Pydantic数据验证
- ✅ **Docker部署** - 容器化支持

---

**🏢 维护团队**：FinDataCore开发组  
**📅 创建日期**：2025年1月  
**🚀 版本**：v1.0.0  
**⭐ 性能等级**：企业级高性能
