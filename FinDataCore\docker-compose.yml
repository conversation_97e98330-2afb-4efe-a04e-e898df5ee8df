version: '3.8'

services:
  findatacore:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: findatacore-api
    ports:
      - "8000:8000"
    environment:
      - PROJECT_NAME=FinDataCore
      - VERSION=1.0.0
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8000
      - SECRET_KEY=${SECRET_KEY:-your-production-secret-key-change-this}
      - ACCESS_TOKEN_EXPIRE_MINUTES=480
      - DATABASE_URI=******************************************************/flask_db
      - API_KEY=lxw8025031
      - API_BASE_URL=http://*************:5000
      - API_TIMEOUT=30
      - API_RETRY_TIMES=3
      - LOG_FILE_PATH=logs/server.log
      - LOG_LEVEL=INFO
      - CACHE_TYPE=redis
      - CACHE_REDIS_URL=redis://redis:6379/0
      - <PERSON><PERSON><PERSON>_DEFAULT_TIMEOUT=300
    volumes:
      - ./logs:/app/logs
      - app_data:/app/data
    restart: unless-stopped
    networks:
      - findatacore-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: findatacore-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    restart: unless-stopped
    networks:
      - findatacore-network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # 可选：Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: findatacore-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    restart: unless-stopped
    networks:
      - findatacore-network
    depends_on:
      - findatacore
    profiles:
      - production

  # 可选：监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: findatacore-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    restart: unless-stopped
    networks:
      - findatacore-network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: findatacore-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    restart: unless-stopped
    networks:
      - findatacore-network
    depends_on:
      - prometheus
    profiles:
      - monitoring

networks:
  findatacore-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis_data:
    driver: local
  app_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
