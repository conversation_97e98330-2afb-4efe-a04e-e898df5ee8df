"""
导出相关路由模块
"""
from flask import Blueprint, request, jsonify, send_file
from flask_login import login_required
import pandas as pd
import io
import datetime
import logging

from app.services.route_handler import DataValidator, ResponseFormatter
from app.services.data_service import filter_data

logger = logging.getLogger(__name__)

# 创建导出蓝图
export_bp = Blueprint('export', __name__)


@export_bp.route('/<export_type>', methods=['POST'])
@login_required
def export_data(export_type):
    """导出数据功能"""
    logger.info(f"收到数据导出请求，类型: {export_type}")
    
    # 获取要导出的数据
    data = request.json.get('data', [])
    search_query = request.json.get('search_query', '')
    
    # 验证数据
    is_valid, error_msg = DataValidator.validate_export_data(data)
    if not is_valid:
        return ResponseFormatter.format_error_response(error_msg)
    
    logger.info(f"准备导出数据，总记录数: {len(data)}, "
               f"搜索条件: {search_query if search_query else '无'}")
    
    try:
        # 处理数据
        cleaned_data = _process_export_data(data, search_query)
        
        if not cleaned_data:
            return ResponseFormatter.format_error_response("没有有效数据可导出")
        
        # 转换为DataFrame
        df = pd.DataFrame(cleaned_data)
        
        # 创建内存文件对象
        output = io.BytesIO()
        
        # 根据导出类型处理
        if export_type == 'excel':
            filename, mimetype = _export_excel(df, output)
        elif export_type == 'csv':
            filename, mimetype = _export_csv(df, output)
        else:
            return ResponseFormatter.format_error_response(
                f"不支持的导出类型: {export_type}"
            )
        
        # 记录导出信息
        export_info = f"导出{len(cleaned_data)}条数据"
        if search_query:
            export_info += f"，搜索条件：{search_query}"
        logger.info(export_info)
        
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype=mimetype
        )
    
    except Exception as e:
        logger.exception(f"数据导出发生错误: {str(e)}")
        return ResponseFormatter.format_error_response(
            f'导出过程发生错误: {str(e)}'
        )


def _process_export_data(data, search_query=""):
    """处理导出数据"""
    # 构造数据结构
    full_data = {
        'results': data,
        'columns': list(data[0].keys()) if data and isinstance(data[0], dict) else []
    }
    
    # 如果有搜索查询，使用filter_data函数处理
    if search_query:
        logger.info(f"应用搜索过滤：{search_query}")
        filtered_data = filter_data(full_data, search_query=search_query)
        cleaned_data = filtered_data.get('results', [])
        logger.info(f"过滤后数据量: {len(cleaned_data)}")
    else:
        # 处理数据，移除空列或无关列
        cleaned_data = []
        if data and len(data) > 0:
            # 检查是否为字典列表
            if isinstance(data[0], dict):
                # 移除空值和None值列
                for item in data:
                    cleaned_item = {
                        k: v for k, v in item.items() 
                        if v is not None and v != ''
                    }
                    cleaned_data.append(cleaned_item)
            else:
                cleaned_data = data
        else:
            cleaned_data = data
    
    return cleaned_data


def _export_excel(df, output):
    """导出Excel格式"""
    logger.info("导出为Excel格式")
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='数据导出')
    output.seek(0)
    
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"数据导出_{timestamp}.xlsx"
    mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    
    return filename, mimetype


def _export_csv(df, output):
    """导出CSV格式"""
    logger.info("导出为CSV格式")
    df.to_csv(output, index=False, encoding='utf-8-sig')
    output.seek(0)
    
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"数据导出_{timestamp}.csv"
    mimetype = 'text/csv'
    
    return filename, mimetype 