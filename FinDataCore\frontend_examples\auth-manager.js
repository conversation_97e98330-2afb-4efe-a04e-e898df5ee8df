/**
 * 认证管理器 - 处理JWT Token认证
 * 用于替换原Flask项目的Session认证
 */
class AuthManager {
    constructor() {
        this.token = localStorage.getItem('access_token');
        this.userLevel = localStorage.getItem('user_level');
        this.permissions = JSON.parse(localStorage.getItem('permissions') || '[]');
        this.tokenExpiry = localStorage.getItem('token_expiry');
    }
    
    /**
     * 用户登录
     * @param {string} password - 登录密码
     * @returns {Promise<{success: boolean, data?: any, error?: string}>}
     */
    async login(password) {
        try {
            const response = await fetch('/api/v1/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ password })
            });
            
            if (response.ok) {
                const data = await response.json();
                
                // 保存认证信息
                this.token = data.access_token;
                this.userLevel = data.user_level;
                this.permissions = data.permissions;
                
                // 计算过期时间
                const expiryTime = new Date();
                expiryTime.setSeconds(expiryTime.getSeconds() + data.expires_in);
                this.tokenExpiry = expiryTime.toISOString();
                
                // 保存到localStorage
                localStorage.setItem('access_token', this.token);
                localStorage.setItem('user_level', this.userLevel);
                localStorage.setItem('permissions', JSON.stringify(this.permissions));
                localStorage.setItem('token_expiry', this.tokenExpiry);
                
                console.log(`用户登录成功: ${this.userLevel} 权限`);
                return { success: true, data };
            } else {
                const error = await response.json();
                console.error('登录失败:', error.detail);
                return { success: false, error: error.detail };
            }
        } catch (error) {
            console.error('登录请求异常:', error);
            return { success: false, error: '网络错误，请检查连接' };
        }
    }
    
    /**
     * 用户登出
     */
    async logout() {
        try {
            // 调用登出API（可选）
            if (this.token) {
                await fetch('/api/v1/auth/logout', {
                    method: 'POST',
                    headers: this.getAuthHeaders()
                });
            }
        } catch (error) {
            console.warn('登出API调用失败:', error);
        } finally {
            // 清理本地数据
            this.token = null;
            this.userLevel = null;
            this.permissions = [];
            this.tokenExpiry = null;
            
            localStorage.removeItem('access_token');
            localStorage.removeItem('user_level');
            localStorage.removeItem('permissions');
            localStorage.removeItem('token_expiry');
            
            console.log('用户已登出');
        }
    }
    
    /**
     * 检查是否已认证
     * @returns {boolean}
     */
    isAuthenticated() {
        if (!this.token) {
            return false;
        }
        
        // 检查Token是否过期
        if (this.tokenExpiry) {
            const expiry = new Date(this.tokenExpiry);
            if (new Date() >= expiry) {
                console.log('Token已过期');
                this.logout();
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取认证请求头
     * @returns {Object}
     */
    getAuthHeaders() {
        if (!this.isAuthenticated()) {
            throw new Error('用户未登录或Token已过期');
        }
        
        return {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
        };
    }
    
    /**
     * 检查用户权限
     * @param {string} permission - 权限名称
     * @returns {boolean}
     */
    hasPermission(permission) {
        return this.permissions.includes(permission);
    }
    
    /**
     * 检查用户级别
     * @param {string} requiredLevel - 所需级别
     * @returns {boolean}
     */
    hasUserLevel(requiredLevel) {
        const levelHierarchy = {
            'limited': 1,
            'standard': 2,
            'full': 3
        };
        
        const userLevelValue = levelHierarchy[this.userLevel] || 0;
        const requiredLevelValue = levelHierarchy[requiredLevel] || 0;
        
        return userLevelValue >= requiredLevelValue;
    }
    
    /**
     * 获取当前用户信息
     * @returns {Promise<Object>}
     */
    async getCurrentUser() {
        if (!this.isAuthenticated()) {
            throw new Error('用户未登录');
        }
        
        try {
            const response = await fetch('/api/v1/auth/me', {
                headers: this.getAuthHeaders()
            });
            
            if (response.ok) {
                return await response.json();
            } else {
                throw new Error('获取用户信息失败');
            }
        } catch (error) {
            console.error('获取用户信息异常:', error);
            throw error;
        }
    }
    
    /**
     * 刷新Token（如果支持）
     * @returns {Promise<boolean>}
     */
    async refreshToken() {
        // 当前实现不支持Token刷新
        // 如果需要，可以在这里实现刷新逻辑
        console.warn('Token刷新功能暂未实现');
        return false;
    }
    
    /**
     * 自动登录检查
     * 在页面加载时调用，检查是否有有效的Token
     */
    autoLogin() {
        if (this.isAuthenticated()) {
            console.log(`自动登录成功: ${this.userLevel} 权限`);
            return true;
        } else {
            console.log('无有效Token，需要重新登录');
            return false;
        }
    }
    
    /**
     * 获取用户显示信息
     * @returns {Object}
     */
    getUserInfo() {
        return {
            isAuthenticated: this.isAuthenticated(),
            userLevel: this.userLevel,
            permissions: this.permissions,
            tokenExpiry: this.tokenExpiry
        };
    }
}

// 创建全局实例
const authManager = new AuthManager();

// 页面加载时自动检查登录状态
document.addEventListener('DOMContentLoaded', () => {
    // 自动登录检查
    authManager.autoLogin();
    
    // 设置定时器检查Token过期
    setInterval(() => {
        if (!authManager.isAuthenticated() && window.location.pathname !== '/login') {
            console.log('Token已过期，跳转到登录页');
            window.location.href = '/login';
        }
    }, 60000); // 每分钟检查一次
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
