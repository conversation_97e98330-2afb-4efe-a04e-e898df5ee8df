/**
 * 侧边栏管理模块 V2 - 重构版
 * 统一管理所有页面的侧边栏交互，解决事件冲突问题
 */

// 确保依赖模块已加载
if (!window.TaixiangApp || !window.TaixiangApp.Utils) {
    throw new Error('依赖模块未加载，请先加载 app-core.js');
}

// 侧边栏管理器类 - 重构版
TaixiangApp.SidebarManagerV2 = class {
    constructor() {
        this.sidebar = null;
        this.mobileToggle = null;
        this.desktopToggle = null;
        this.overlay = null;
        this.isMobile = window.innerWidth <= 768;
        this.isCollapsed = false;
        this.initialized = false;
        this.eventListeners = new Map(); // 跟踪事件监听器
        
        // 绑定方法到实例
        this.handleMobileToggle = this.handleMobileToggle.bind(this);
        this.handleOverlayClick = this.handleOverlayClick.bind(this);
        this.handleResize = this.handleResize.bind(this);
    }
    
    init() {
        if (this.initialized) {
            console.log('侧边栏管理器V2已初始化，跳过重复初始化');
            return;
        }
        
        console.log('🚀 初始化侧边栏管理器V2');
        
        // 清理可能存在的旧实例
        this.cleanup();
        
        // 获取DOM元素
        this.findElements();
        
        if (!this.sidebar) {
            console.warn('⚠️ 未找到侧边栏元素，延迟重试');
            setTimeout(() => this.init(), 500);
            return;
        }
        
        // 设置初始状态
        this.setupInitialState();
        
        // 注册事件监听器
        this.registerEventListeners();
        
        // 恢复保存的状态
        this.restoreState();
        
        this.initialized = true;
        console.log('✅ 侧边栏管理器V2初始化完成');
        
        // 暴露调试接口
        window.sidebarDebug = {
            manager: this,
            testMobileToggle: () => this.handleMobileToggle({ preventDefault: () => {}, stopPropagation: () => {} }),
            getState: () => ({
                isMobile: this.isMobile,
                isCollapsed: this.isCollapsed,
                sidebarActive: this.sidebar?.classList.contains('sidebar-active'),
                overlayActive: this.overlay?.classList.contains('overlay-active')
            })
        };
    }
    
    findElements() {
        this.sidebar = document.querySelector('.sidebar');
        this.mobileToggle = document.getElementById('sidebarToggle');
        this.desktopToggle = document.getElementById('collapseToggle');
        this.overlay = document.getElementById('sidebarOverlay');
        
        console.log('🔍 元素查找结果:', {
            sidebar: !!this.sidebar,
            mobileToggle: !!this.mobileToggle,
            desktopToggle: !!this.desktopToggle,
            overlay: !!this.overlay
        });
    }
    
    setupInitialState() {
        // 确保移动端按钮在移动端可见
        this.updateMobileToggleVisibility();
        
        // 确保侧边栏初始状态正确
        if (this.isMobile) {
            this.sidebar.classList.remove('sidebar-collapsed');
            this.closeMobileSidebar();
        }
    }
    
    registerEventListeners() {
        console.log('📝 注册事件监听器');
        
        // 移动端切换按钮
        if (this.mobileToggle) {
            this.addEventListenerSafely(this.mobileToggle, 'click', this.handleMobileToggle);
            this.addEventListenerSafely(this.mobileToggle, 'touchend', this.handleMobileToggle);
        }
        
        // 遮罩层点击
        if (this.overlay) {
            this.addEventListenerSafely(this.overlay, 'click', this.handleOverlayClick);
        }
        
        // 桌面端切换按钮
        if (this.desktopToggle) {
            this.addEventListenerSafely(this.desktopToggle, 'click', () => this.toggleDesktopSidebar());
        }
        
        // 窗口大小变化
        this.addEventListenerSafely(window, 'resize', this.handleResize);
        this.addEventListenerSafely(window, 'orientationchange', this.handleResize);
    }
    
    addEventListenerSafely(element, event, handler) {
        // 移除可能存在的旧监听器
        const key = `${element.constructor.name}-${event}`;
        if (this.eventListeners.has(key)) {
            const oldHandler = this.eventListeners.get(key);
            element.removeEventListener(event, oldHandler);
        }
        
        // 添加新监听器
        element.addEventListener(event, handler, { passive: false });
        this.eventListeners.set(key, handler);
    }
    
    handleMobileToggle(e) {
        e.preventDefault();
        e.stopPropagation();
        
        console.log('📱 移动端切换按钮被点击');
        
        if (!this.isMobile) {
            console.log('🖥️ 当前为桌面端模式，忽略移动端切换');
            return;
        }
        
        const isActive = this.sidebar.classList.contains('sidebar-active');
        
        if (isActive) {
            this.closeMobileSidebar();
        } else {
            this.openMobileSidebar();
        }
        
        // 添加视觉反馈
        this.addClickFeedback(this.mobileToggle);
    }
    
    handleOverlayClick(e) {
        e.preventDefault();
        e.stopPropagation();
        
        console.log('🖱️ 遮罩层被点击');
        this.closeMobileSidebar();
    }
    
    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;
        
        console.log(`📐 窗口大小变化: ${wasMobile ? '移动端' : '桌面端'} -> ${this.isMobile ? '移动端' : '桌面端'}`);
        
        // 更新移动端按钮可见性
        this.updateMobileToggleVisibility();
        
        // 如果从移动端切换到桌面端，或反之
        if (wasMobile !== this.isMobile) {
            if (this.isMobile) {
                // 切换到移动端
                this.sidebar.classList.remove('sidebar-collapsed');
                this.closeMobileSidebar();
            } else {
                // 切换到桌面端
                this.closeMobileSidebar();
                this.restoreState();
            }
        }
    }
    
    openMobileSidebar() {
        if (!this.isMobile || !this.sidebar) return;
        
        console.log('📂 打开移动端侧边栏');
        this.sidebar.classList.add('sidebar-active');
        if (this.overlay) {
            this.overlay.classList.add('overlay-active');
        }
    }
    
    closeMobileSidebar() {
        if (!this.sidebar) return;
        
        console.log('📁 关闭移动端侧边栏');
        this.sidebar.classList.remove('sidebar-active');
        if (this.overlay) {
            this.overlay.classList.remove('overlay-active');
        }
    }
    
    toggleDesktopSidebar() {
        if (this.isMobile) return;
        
        if (this.isCollapsed) {
            this.expandDesktopSidebar();
        } else {
            this.collapseDesktopSidebar();
        }
    }
    
    collapseDesktopSidebar() {
        if (this.isMobile || !this.sidebar) return;
        
        console.log('📉 折叠桌面端侧边栏');
        this.sidebar.classList.add('sidebar-collapsed');
        this.isCollapsed = true;
        this.saveState();
        
        // 更新按钮图标
        this.updateDesktopToggleIcon(true);
    }
    
    expandDesktopSidebar() {
        if (this.isMobile || !this.sidebar) return;
        
        console.log('📈 展开桌面端侧边栏');
        this.sidebar.classList.remove('sidebar-collapsed');
        this.isCollapsed = false;
        this.saveState();
        
        // 更新按钮图标
        this.updateDesktopToggleIcon(false);
    }
    
    updateMobileToggleVisibility() {
        if (!this.mobileToggle) return;
        
        if (this.isMobile) {
            // 强制设置移动端按钮样式
            this.mobileToggle.style.cssText = `
                display: flex !important;
                position: fixed !important;
                top: 10px !important;
                left: 10px !important;
                z-index: 1040 !important;
                width: 40px !important;
                height: 40px !important;
                background-color: white !important;
                border-radius: 50% !important;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2) !important;
                align-items: center !important;
                justify-content: center !important;
                cursor: pointer !important;
                border: 1px solid rgba(0,0,0,0.1) !important;
                transition: all 0.2s ease !important;
            `;
            
            // 确保图标样式
            const icon = this.mobileToggle.querySelector('i');
            if (icon) {
                icon.style.cssText = `
                    font-size: 20px !important;
                    color: #333 !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    line-height: 1 !important;
                `;
            }
        } else {
            this.mobileToggle.style.display = 'none';
        }
    }
    
    updateDesktopToggleIcon(isCollapsed) {
        if (!this.desktopToggle) return;
        
        const icon = this.desktopToggle.querySelector('i');
        if (icon) {
            if (isCollapsed) {
                icon.className = 'bi bi-chevron-right';
            } else {
                icon.className = 'bi bi-chevron-left';
            }
        }
    }
    
    addClickFeedback(element) {
        if (!element) return;
        
        element.style.transform = 'scale(0.95)';
        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, 150);
    }
    
    saveState() {
        if (!this.isMobile) {
            localStorage.setItem('sidebarCollapsed', this.isCollapsed);
        }
    }
    
    restoreState() {
        if (this.isMobile || !this.sidebar) return;
        
        const collapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        
        if (collapsed) {
            this.collapseDesktopSidebar();
        } else {
            this.expandDesktopSidebar();
        }
    }
    
    cleanup() {
        // 清理事件监听器
        this.eventListeners.forEach((handler, key) => {
            const [elementType, event] = key.split('-');
            // 这里可以添加更详细的清理逻辑
        });
        this.eventListeners.clear();
        
        // 清理全局调试接口
        if (window.sidebarDebug) {
            delete window.sidebarDebug;
        }
    }
    
    destroy() {
        console.log('🗑️ 销毁侧边栏管理器V2');
        this.cleanup();
        this.initialized = false;
    }
};

// 侧边栏功能管理器 - 保持不变
TaixiangApp.SidebarFunctionManager = {
    // 初始化所有侧边栏功能
    init: function() {
        console.log('初始化侧边栏功能管理器');
        
        // 注册各种功能
        this.registerDateFilter();
        this.registerCustomerSearch();
        this.registerOverdueQuery();
        this.registerLogout();
        this.setupDatePickerIcon();
    },
    
    // 注册日期筛选功能
    registerDateFilter: function() {
        const dateFilterForm = document.getElementById('dateFilterForm');
        if (dateFilterForm) {
            dateFilterForm.addEventListener('submit', function(event) {
                event.preventDefault();
                const date = document.getElementById('date').value;
                if (!date) {
                    alert('请选择日期');
                    return;
                }
                
                TaixiangApp.Utils.showLoading();
                
                const currentPath = window.location.pathname;
                if (currentPath === '/') {
                    TaixiangApp.Navigation.filterByDate(date);
                } else {
                    const url = new URL(window.location.origin + '/');
                    url.searchParams.set('date', date);
                    url.searchParams.set('tab', 'filter');
                    window.location.href = url.toString();
                }
            });
        }
    },
    
    // 注册客户搜索功能
    registerCustomerSearch: function() {
        const customerSearchForm = document.getElementById('customerSearchForm');
        if (customerSearchForm) {
            customerSearchForm.addEventListener('submit', function(event) {
                event.preventDefault();
                const customerName = document.getElementById('customerName').value;
                if (!customerName) {
                    alert('请输入客户姓名');
                    return;
                }
                
                TaixiangApp.Utils.showLoading();
                
                const currentPath = window.location.pathname;
                if (currentPath === '/') {
                    TaixiangApp.Navigation.searchCustomer();
                } else {
                    const url = new URL(window.location.origin + '/');
                    url.searchParams.set('customerName', customerName);
                    url.searchParams.set('tab', 'customer');
                    window.location.href = url.toString();
                }
            });
        }
    },
    
    // 注册逾期订单查询功能
    registerOverdueQuery: function() {
        const overdueButton = document.getElementById('overdueButton');
        if (overdueButton) {
            overdueButton.addEventListener('click', function(event) {
                event.preventDefault();
                
                const currentPath = window.location.pathname;
                if (currentPath === '/overdue') {
                    // 如果已经在逾期订单页面，刷新数据
                    if (typeof loadOverdueData === 'function') {
                        loadOverdueData(1, true); // 强制刷新
                    }
                } else {
                    // 导航到逾期订单页面
                    TaixiangApp.Utils.showLoading();
                    window.location.href = '/overdue';
                }
            });
        }
    },
    
    // 注册退出登录功能
    registerLogout: function() {
        const logoutButton = document.getElementById('logoutButton');
        if (logoutButton) {
            logoutButton.addEventListener('click', function(event) {
                event.preventDefault();
                
                if (confirm('确定要退出登录吗？')) {
                    TaixiangApp.Utils.showLoading();
                    window.location.href = '/logout';
                }
            });
        }
    },
    
    // 设置日期选择器图标
    setupDatePickerIcon: function() {
        const dateInput = document.getElementById('date');
        if (dateInput) {
            // 为日期输入框添加图标点击功能
            const iconButton = dateInput.parentElement.querySelector('.input-group-text');
            if (iconButton) {
                iconButton.addEventListener('click', function() {
                    dateInput.focus();
                    dateInput.showPicker && dateInput.showPicker();
                });
            }
        }
    }
}; 