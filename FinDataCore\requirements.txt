# FastAPI核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# HTTP客户端
httpx==0.25.2
requests==2.31.0

# 数据处理
pandas==2.0.3
numpy==1.25.2

# 缓存
cachetools==5.3.2

# 数据库（如果需要直接连接）
psycopg2-binary==2.9.9
sqlalchemy==2.0.23

# 日志和监控
structlog==23.2.0

# 开发和测试
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# 环境配置
python-dotenv==1.0.0

# 文档处理（保持与原项目兼容）
openpyxl==3.1.2
python-docx==0.8.11

# 图像处理
Pillow==9.5.0

# 二维码生成
qrcode[pil]==7.4.2

# 日期处理
python-dateutil==2.8.2

# 系统监控
psutil==5.9.5
