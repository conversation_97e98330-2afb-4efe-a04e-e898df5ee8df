#!/bin/bash
# 企业信用查询功能修复部署脚本

echo "=== 企业信用查询功能修复部署 ==="

# 1. 停止当前容器
echo "停止当前容器..."
docker stop web_20250803 || true
docker rm web_20250803 || true

# 2. 重新构建镜像
echo "重新构建Docker镜像..."
docker build -f Dockerfile.optimized -t hdsc_query_app:fixed .

# 3. 启动新容器
echo "启动修复后的容器..."
docker run -d \
    --name web_20250804_fixed \
    --restart unless-stopped \
    -p 5000:5000 \
    -v $(pwd)/logs:/app/logs \
    -v $(pwd)/cache:/app/cache \
    -v $(pwd)/config.py:/app/config.py:ro \
    -v $(pwd)/gunicorn_config.py:/app/gunicorn_config.py:ro \
    -e FLASK_ENV=production \
    -e FLASK_APP=run.py \
    -e PYTHONPATH=/app \
    -e TZ=Asia/Shanghai \
    hdsc_query_app:fixed

# 4. 等待容器启动
echo "等待容器启动..."
sleep 10

# 5. 检查容器状态
echo "检查容器状态..."
docker ps | grep web_20250804_fixed

# 6. 检查应用健康状态
echo "检查应用健康状态..."
curl -f http://localhost:5000/ || echo "应用可能还在启动中，请稍等片刻"

# 7. 查看日志
echo "查看最新日志..."
docker logs --tail 20 web_20250804_fixed

echo ""
echo "=== 修复完成！==="
echo "主要修改内容："
echo "1. 增加Coze API请求超时时间：60秒 -> 600秒（10分钟）"
echo "2. 添加重试机制：最多重试3次，每次间隔2秒"
echo "3. 改善错误处理：提供更友好的错误提示"
echo "4. 前端错误处理优化：区分不同类型的错误"
echo "5. 用户体验优化：查询开始时提醒用户可能需要几分钟时间"
echo ""
echo "现在可以测试企业信用查询功能了！"
echo "如果还有问题，请查看容器日志："
echo "docker logs -f web_20250804_fixed"