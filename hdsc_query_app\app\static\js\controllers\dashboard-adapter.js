/**
 * Dashboard适配器
 * 确保新旧代码兼容性，提供平滑过渡
 */
const DashboardAdapter = {
    // 初始化
    init: function() {
        console.log('初始化Dashboard适配器');
        this.patchLegacyFunctions();
        this.setupCompatibilityListeners();
    },
    
    // 修补旧的全局函数，确保它们继续工作
    patchLegacyFunctions: function() {
        // 保存原始函数引用
        if (typeof window.enhanceDataTables === 'function') {
            this.originalEnhanceDataTables = window.enhanceDataTables;
        }
        
        if (typeof window.optimizeForMobile === 'function') {
            this.originalOptimizeForMobile = window.optimizeForMobile;
        }
        
        if (typeof window.fillTable === 'function') {
            this.originalFillTable = window.fillTable;
        }
        
        // 替换为适配器版本
        window.enhanceDataTables = this.enhanceDataTables.bind(this);
        window.optimizeForMobile = this.optimizeForMobile.bind(this);
        window.fillTable = this.fillTable.bind(this);
    },
    
    // 设置兼容性事件监听器
    setupCompatibilityListeners: function() {
        document.addEventListener('app:ready', () => {
            // 当应用初始化完成后，执行额外的兼容性代码
            this.ensureTablesInitialized();
        });
    },
    
    // 确保所有表格已初始化
    ensureTablesInitialized: function() {
        // 获取所有表格
        const tables = document.querySelectorAll('.data-table');
        
        // 检查表格是否已初始化为DataTable
        tables.forEach(table => {
            if (!$.fn.DataTable.isDataTable(table)) {
                console.log('检测到未初始化的表格，使用DataQueryController初始化');
                
                // 获取表格所在的标签页ID
                const tabPane = table.closest('.tab-pane');
                if (tabPane) {
                    const tabId = tabPane.id;
                    
                    // 使用DataQueryController初始化表格
                    if (window.DataQueryController) {
                        DataQueryController.initializeDataTable(tabId);
                    } else {
                        // 如果DataQueryController不可用，使用旧方法
                        this.initFallbackTable(table);
                    }
                }
            }
        });
    },
    
    // 使用旧方法初始化表格（后备方案）
    initFallbackTable: function(tableElement) {
        // 获取表格所在的标签页ID
        const tabPane = tableElement.closest('.tab-pane');
        if (!tabPane) return;
        
        const tabId = tabPane.id;
        
        // 尝试获取数据源
        let dataSource = null;
        if (tabId === 'filter' && window.filterResults) {
            dataSource = window.filterResults;
        } else if (tabId === 'overdue' && window.overdueResults) {
            dataSource = window.overdueResults;
        } else if (tabId === 'customer' && window.customerResults) {
            dataSource = window.customerResults;
        }
        
        if (dataSource && dataSource.results && dataSource.results.length > 0) {
            console.log(`使用后备方法初始化 ${tabId} 表格，数据来源有 ${dataSource.results.length} 条数据`);
            
            // 如果原始fillTable函数可用，使用它
            if (this.originalFillTable) {
                this.originalFillTable(tableElement, dataSource);
            }
        }
    },
    
    // 适配版本的enhanceDataTables
    enhanceDataTables: function() {
        console.log('调用增强表格显示(适配器版本)');
        
        // 如果StyleController可用，使用它
        if (window.StyleController) {
            const tables = document.querySelectorAll('.data-table');
            tables.forEach(table => {
                // 获取表格类型
                const tableType = table.getAttribute('data-type') || 'filter';
                
                // 使用StyleController增强表格
                StyleController.enhanceTable(table, tableType);
            });
            return;
        }
        
        // 否则使用原始函数
        if (this.originalEnhanceDataTables) {
            this.originalEnhanceDataTables();
        }
    },
    
    // 适配版本的optimizeForMobile
    optimizeForMobile: function() {
        console.log('调用移动设备优化(适配器版本)');
        
        // 如果存在原始函数，使用它
        if (this.originalOptimizeForMobile) {
            this.originalOptimizeForMobile();
        }
    },
    
    // 适配版本的fillTable
    fillTable: function(tableElement, dataSource) {
        console.log('调用填充表格(适配器版本)');
        
        // 如果DataQueryController可用，使用它
        if (window.DataQueryController) {
            // 获取表格所在的标签页ID
            const tabPane = tableElement.closest('.tab-pane');
            if (tabPane) {
                const tabId = tabPane.id;
                
                // 先保存数据到全局变量
                window[`${tabId}Results`] = dataSource;
                
                // 然后使用DataQueryController显示数据
                DataQueryController.displayTabData(tabId, dataSource);
            }
            return;
        }
        
        // 否则使用原始函数
        if (this.originalFillTable) {
            this.originalFillTable(tableElement, dataSource);
        }
    }
};

// 当DOM加载完成后初始化适配器
document.addEventListener('DOMContentLoaded', function() {
    DashboardAdapter.init();
}); 