"""
辅助工具函数
"""
import re
import hashlib
from datetime import datetime, date
from typing import Any, Dict, List, Optional, Union
import logging

logger = logging.getLogger(__name__)

def validate_date_format(date_str: str) -> bool:
    """
    验证日期格式是否为YYYY-MM-DD
    
    Args:
        date_str: 日期字符串
        
    Returns:
        是否为有效格式
    """
    try:
        datetime.strptime(date_str, '%Y-%m-%d')
        return True
    except ValueError:
        return False

def format_date(date_obj: Union[datetime, date, str]) -> str:
    """
    格式化日期为YYYY-MM-DD字符串
    
    Args:
        date_obj: 日期对象
        
    Returns:
        格式化的日期字符串
    """
    if isinstance(date_obj, str):
        return date_obj
    elif isinstance(date_obj, datetime):
        return date_obj.strftime('%Y-%m-%d')
    elif isinstance(date_obj, date):
        return date_obj.strftime('%Y-%m-%d')
    else:
        raise ValueError(f"不支持的日期类型: {type(date_obj)}")

def generate_hash(data: str) -> str:
    """
    生成数据的MD5哈希值
    
    Args:
        data: 要哈希的数据
        
    Returns:
        MD5哈希字符串
    """
    return hashlib.md5(data.encode('utf-8')).hexdigest()

def clean_string(text: str) -> str:
    """
    清理字符串，移除特殊字符
    
    Args:
        text: 原始字符串
        
    Returns:
        清理后的字符串
    """
    if not isinstance(text, str):
        return str(text)
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 移除特殊字符（保留中文、英文、数字、基本标点）
    text = re.sub(r'[^\w\s\u4e00-\u9fff\-\.\,\(\)\[\]\/]', '', text)
    
    return text

def safe_float(value: Any, default: float = 0.0) -> float:
    """
    安全转换为浮点数
    
    Args:
        value: 要转换的值
        default: 默认值
        
    Returns:
        浮点数
    """
    try:
        if value is None or value == '':
            return default
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int(value: Any, default: int = 0) -> int:
    """
    安全转换为整数
    
    Args:
        value: 要转换的值
        default: 默认值
        
    Returns:
        整数
    """
    try:
        if value is None or value == '':
            return default
        return int(float(value))
    except (ValueError, TypeError):
        return default

def format_currency(amount: Union[int, float, str], currency: str = "¥") -> str:
    """
    格式化货币金额
    
    Args:
        amount: 金额
        currency: 货币符号
        
    Returns:
        格式化的货币字符串
    """
    try:
        amount_float = safe_float(amount)
        return f"{currency}{amount_float:,.2f}"
    except Exception:
        return f"{currency}0.00"

def extract_numbers(text: str) -> List[float]:
    """
    从文本中提取所有数字
    
    Args:
        text: 文本字符串
        
    Returns:
        数字列表
    """
    if not isinstance(text, str):
        return []
    
    # 匹配整数和小数
    pattern = r'-?\d+\.?\d*'
    matches = re.findall(pattern, text)
    
    numbers = []
    for match in matches:
        try:
            numbers.append(float(match))
        except ValueError:
            continue
    
    return numbers

def truncate_string(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """
    截断字符串
    
    Args:
        text: 原始字符串
        max_length: 最大长度
        suffix: 后缀
        
    Returns:
        截断后的字符串
    """
    if not isinstance(text, str):
        text = str(text)
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def merge_dicts(*dicts: Dict[str, Any]) -> Dict[str, Any]:
    """
    合并多个字典
    
    Args:
        *dicts: 要合并的字典
        
    Returns:
        合并后的字典
    """
    result = {}
    for d in dicts:
        if isinstance(d, dict):
            result.update(d)
    return result

def filter_dict_by_keys(data: Dict[str, Any], keys: List[str]) -> Dict[str, Any]:
    """
    根据键列表过滤字典
    
    Args:
        data: 原始字典
        keys: 要保留的键列表
        
    Returns:
        过滤后的字典
    """
    return {k: v for k, v in data.items() if k in keys}

def get_nested_value(data: Dict[str, Any], path: str, default: Any = None) -> Any:
    """
    获取嵌套字典中的值
    
    Args:
        data: 字典数据
        path: 路径，用点分隔，如 "user.profile.name"
        default: 默认值
        
    Returns:
        值或默认值
    """
    try:
        keys = path.split('.')
        value = data
        for key in keys:
            value = value[key]
        return value
    except (KeyError, TypeError):
        return default

def set_nested_value(data: Dict[str, Any], path: str, value: Any) -> None:
    """
    设置嵌套字典中的值
    
    Args:
        data: 字典数据
        path: 路径，用点分隔
        value: 要设置的值
    """
    keys = path.split('.')
    current = data
    
    for key in keys[:-1]:
        if key not in current:
            current[key] = {}
        current = current[key]
    
    current[keys[-1]] = value

def calculate_percentage(part: Union[int, float], total: Union[int, float]) -> float:
    """
    计算百分比
    
    Args:
        part: 部分值
        total: 总值
        
    Returns:
        百分比（0-100）
    """
    try:
        if total == 0:
            return 0.0
        return (safe_float(part) / safe_float(total)) * 100
    except Exception:
        return 0.0

def is_valid_phone(phone: str) -> bool:
    """
    验证手机号格式
    
    Args:
        phone: 手机号字符串
        
    Returns:
        是否为有效手机号
    """
    if not isinstance(phone, str):
        return False
    
    # 中国手机号格式验证
    pattern = r'^1[3-9]\d{9}$'
    return bool(re.match(pattern, phone))

def mask_sensitive_data(data: str, mask_char: str = "*", keep_start: int = 3, keep_end: int = 4) -> str:
    """
    遮蔽敏感数据
    
    Args:
        data: 原始数据
        mask_char: 遮蔽字符
        keep_start: 保留开头字符数
        keep_end: 保留结尾字符数
        
    Returns:
        遮蔽后的数据
    """
    if not isinstance(data, str) or len(data) <= keep_start + keep_end:
        return data
    
    start = data[:keep_start]
    end = data[-keep_end:] if keep_end > 0 else ""
    middle = mask_char * (len(data) - keep_start - keep_end)
    
    return start + middle + end
