from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import login_user, logout_user, login_required

from app.models.user import User
from app.services.captcha_service import CaptchaService
from config import Config

# 创建认证蓝图
auth = Blueprint('auth', __name__)


@auth.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录处理"""
    if request.method == 'POST':
        password = request.form.get('password')
        captcha = request.form.get('captcha')
        
        # 验证验证码
        if not CaptchaService.validate(captcha):
            flash('验证码错误，请重试。', 'error')
            return render_template('login.html', version=Config.VERSION, captcha_image=CaptchaService.generate_and_save())
        
        # 获取用户级别
        user_level = Config.USER_LEVELS.get(password)
        
        if user_level:
            # 创建用户实例
            user = User(password, user_level)
            
            # 将会话标记为永久性，确保会话超时设置生效
            session.permanent = True
            
            # 设置会话超时时间为8小时
            session.permanent_lifetime = 28800
            
            # 记录用户登录状态
            login_user(user, remember=True)
            
            # 获取下一个链接（如果有）
            next_page = request.args.get('next')
            # 重定向到工作台页面或请求的页面
            return redirect(next_page or url_for('main.homepage'))
        else:
            flash('密码错误，请重试。', 'error')
    
    # 生成验证码
    captcha_image = CaptchaService.generate_and_save()
    
    # 渲染登录页面
    return render_template('login.html', version=Config.VERSION, captcha_image=captcha_image)


@auth.route('/refresh-captcha', methods=['GET'])
def refresh_captcha():
    """刷新验证码"""
    captcha_image = CaptchaService.generate_and_save()
    return jsonify({'captcha_image': captcha_image})


@auth.route('/logout')
@login_required
def logout():
    """用户注销处理"""
    logout_user()
    flash('您已成功退出登录。', 'info')
    return redirect(url_for('auth.login'))