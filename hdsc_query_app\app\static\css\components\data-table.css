/**
 * 数据表格组件 (Data Table Component)
 * 遵循BEM命名规范，使用设计令牌
 */

/* ========================================
   表格容器 (.data-table)
 ======================================== */

.data-table {
  /* 基础样式 */
  width: 100%;
  margin: 0;
  border-collapse: separate;
  border-spacing: 0;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  background-color: var(--table-bg);
  border: var(--border-width-1) solid var(--table-border-color);
  border-radius: var(--card-border-radius);
  overflow: hidden;
  
  /* 响应式容器 */
  &__container {
    position: relative;
    width: 100%;
    overflow-x: auto;
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: var(--spacing-6);
    background-color: var(--table-bg);
  }
  
  /* 加载状态 */
  &--loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
    
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 2rem;
      height: 2rem;
      margin-top: -1rem;
      margin-left: -1rem;
      border: 0.25rem solid var(--color-gray-200);
      border-top-color: var(--color-primary-500);
      border-radius: var(--border-radius-full);
      animation: data-table-loading-spin 1s linear infinite;
    }
  }
  
  /* 空状态 */
  &--empty {
    .data-table__body {
      text-align: center;
      color: var(--color-gray-500);
      font-style: italic;
    }
  }
}

/* ========================================
   表格头部 (.data-table__head)
 ======================================== */

.data-table__head {
  background-color: var(--table-head-bg);
  
  .data-table__row {
    border-bottom: var(--border-width-2) solid var(--table-border-color);
  }
  
  .data-table__cell {
    padding: var(--table-cell-padding-y) var(--table-cell-padding-x);
    font-weight: var(--font-weight-semibold);
    color: var(--table-head-color);
    text-align: left;
    vertical-align: middle;
    white-space: nowrap;
    user-select: none;
    position: relative;
    
    /* 可排序列 */
    &--sortable {
      cursor: pointer;
      transition: var(--transition-colors);
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }
      
      /* 排序指示器 */
      &::after {
        content: "";
        position: absolute;
        right: var(--spacing-2);
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 0.25rem solid transparent;
        border-right: 0.25rem solid transparent;
        border-bottom: 0.25rem solid var(--color-gray-400);
        opacity: 0.3;
        transition: var(--transition-opacity);
      }
      
      &:hover::after {
        opacity: 0.6;
      }
      
      /* 升序排序 */
      &--asc::after {
        border-bottom: 0.25rem solid var(--color-primary-500);
        border-top: none;
        opacity: 1;
      }
      
      /* 降序排序 */
      &--desc::after {
        border-top: 0.25rem solid var(--color-primary-500);
        border-bottom: none;
        opacity: 1;
      }
    }
    
    /* 居中对齐 */
    &--center {
      text-align: center;
    }
    
    /* 右对齐 */
    &--right {
      text-align: right;
    }
    
    /* 固定宽度列 */
    &--fixed {
      width: 1%;
      white-space: nowrap;
    }
    
    /* 控制列（响应式） */
    &--control {
      width: var(--spacing-10);
      text-align: center;
      padding: var(--spacing-2);
    }
  }
}

/* ========================================
   表格主体 (.data-table__body)
 ======================================== */

.data-table__body {
  .data-table__row {
    border-bottom: var(--border-width-1) solid var(--table-border-color);
    transition: var(--transition-colors);
    
    /* 悬停效果 */
    &:hover {
      background-color: var(--table-hover-bg);
    }
    
    /* 斑马纹 */
    &:nth-of-type(odd) {
      background-color: var(--table-stripe-bg);
    }
    
    /* 选中状态 */
    &--selected {
      background-color: rgba(13, 110, 253, 0.1);
      border-color: var(--color-primary-300);
    }
    
    /* 状态行 */
    &--success {
      background-color: var(--color-status-ontime);
      border-color: var(--color-status-ontime-border);
    }
    
    &--warning {
      background-color: var(--color-status-upcoming);
      border-color: var(--color-status-upcoming-border);
    }
    
    &--danger {
      background-color: var(--color-status-overdue);
      border-color: var(--color-status-overdue-border);
    }
    
    &--info {
      background-color: var(--color-status-early);
      border-color: var(--color-status-early-border);
    }
  }
  
  .data-table__cell {
    padding: var(--table-cell-padding-y) var(--table-cell-padding-x);
    vertical-align: middle;
    text-align: left;
    
    /* 居中对齐 */
    &--center {
      text-align: center;
    }
    
    /* 右对齐 */
    &--right {
      text-align: right;
    }
    
    /* 数字列 */
    &--numeric {
      text-align: right;
      font-variant-numeric: tabular-nums;
      font-family: var(--font-family-mono);
    }
    
    /* 货币列 */
    &--currency {
      text-align: right;
      font-weight: var(--font-weight-medium);
      color: var(--color-success-600);
    }
    
    /* 状态列 */
    &--status {
      text-align: center;
      font-weight: var(--font-weight-medium);
    }
    
    /* 操作列 */
    &--actions {
      text-align: center;
      white-space: nowrap;
    }
    
    /* 长文本列 */
    &--text {
      max-width: 12rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    /* 可展开文本 */
    &--expandable {
      cursor: pointer;
      position: relative;
      
      &:hover {
        color: var(--color-primary-500);
      }
      
      &::after {
        content: "...";
        position: absolute;
        right: 0;
        bottom: 0;
        padding-left: var(--spacing-2);
        background: linear-gradient(to right, transparent, var(--table-bg) 50%);
      }
      
      &--expanded {
        white-space: normal;
        word-break: break-word;
        max-width: none;
        
        &::after {
          display: none;
        }
      }
    }
  }
}

/* ========================================
   响应式控制 (.data-table__responsive)
 ======================================== */

.data-table__responsive {
  /* 控制按钮 */
  &-control {
    position: relative;
    text-align: center;
    cursor: pointer;
    width: var(--spacing-10);
    min-width: var(--spacing-10);
    
    &::before {
      content: "+";
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: var(--spacing-5);
      height: var(--spacing-5);
      background-color: var(--color-primary-500);
      color: var(--color-white);
      border-radius: var(--border-radius-full);
      font-weight: var(--font-weight-bold);
      font-size: var(--font-size-sm);
      transition: var(--transition-all);
    }
    
    /* 展开状态 */
    &--expanded::before {
      content: "-";
      background-color: var(--color-danger-500);
    }
    
    &:hover::before {
      transform: scale(1.1);
      box-shadow: var(--shadow-md);
    }
  }
  
  /* 详情内容 */
  &-details {
    width: 100%;
    background-color: var(--color-gray-50);
    border-radius: var(--border-radius-base);
    margin: var(--spacing-2) 0;
    
    &__list {
      list-style: none;
      margin: 0;
      padding: 0;
    }
    
    &__item {
      display: flex;
      padding: var(--spacing-2) var(--spacing-3);
      border-bottom: var(--border-width-1) solid var(--color-gray-200);
      
      &:last-child {
        border-bottom: none;
      }
    }
    
    &__label {
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-700);
      min-width: 8rem;
      margin-right: var(--spacing-3);
      flex-shrink: 0;
    }
    
    &__value {
      flex: 1;
      word-break: break-word;
      color: var(--color-gray-900);
    }
  }
}

/* ========================================
   表格分页 (.data-table__pagination)
 ======================================== */

.data-table__pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: var(--spacing-4) 0;
  padding: var(--spacing-2) 0;
  flex-wrap: wrap;
  gap: var(--spacing-1);
  
  /* 分页信息 */
  &-info {
    color: var(--color-gray-600);
    font-size: var(--font-size-sm);
    margin-right: var(--spacing-4);
    
    @media (max-width: 576px) {
      width: 100%;
      text-align: center;
      margin-right: 0;
      margin-bottom: var(--spacing-2);
    }
  }
  
  /* 分页按钮 */
  &-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: var(--spacing-9);
    height: var(--spacing-9);
    padding: var(--pagination-padding-y) var(--pagination-padding-x);
    margin: 0 var(--spacing-0-5);
    border: var(--border-width-1) solid var(--color-gray-300);
    border-radius: var(--pagination-border-radius);
    background-color: var(--color-white);
    color: var(--color-gray-700);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-tight);
    cursor: pointer;
    transition: var(--transition-all);
    user-select: none;
    
    &:hover {
      background-color: var(--pagination-hover-bg);
      border-color: var(--color-gray-400);
      color: var(--color-primary-600);
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }
    
    /* 当前页 */
    &--current {
      background-color: var(--pagination-active-bg);
      border-color: var(--pagination-active-bg);
      color: var(--pagination-active-color);
      font-weight: var(--font-weight-semibold);
      box-shadow: var(--shadow-md);
      
      &:hover {
        background-color: var(--color-primary-600);
        border-color: var(--color-primary-600);
        transform: none;
      }
    }
    
    /* 禁用状态 */
    &--disabled {
      color: var(--pagination-disabled-color);
      background-color: var(--color-gray-100);
      border-color: var(--color-gray-200);
      cursor: not-allowed;
      opacity: 0.6;
      
      &:hover {
        background-color: var(--color-gray-100);
        border-color: var(--color-gray-200);
        color: var(--pagination-disabled-color);
        transform: none;
        box-shadow: none;
      }
    }
    
    /* 导航按钮 */
    &--nav {
      font-weight: var(--font-weight-medium);
    }
  }
  
  /* 省略号 */
  &-ellipsis {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: var(--spacing-9);
    height: var(--spacing-9);
    color: var(--color-gray-500);
    font-size: var(--font-size-sm);
  }
  
  /* 移动端优化 */
  @media (max-width: 576px) {
    justify-content: center;
    
    .data-table__pagination-button {
      min-width: var(--spacing-8);
      height: var(--spacing-8);
      padding: var(--spacing-1-5) var(--spacing-2);
      margin: 0 var(--spacing-0-5);
      font-size: var(--font-size-xs);
    }
    
    /* 隐藏非关键按钮 */
    .data-table__pagination-button:not(.data-table__pagination-button--nav):not(.data-table__pagination-button--current) {
      display: none;
    }
  }
}

/* ========================================
   表格工具栏 (.data-table__toolbar)
 ======================================== */

.data-table__toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  background-color: var(--color-gray-50);
  border-radius: var(--border-radius-base);
  
  &__left,
  &__right {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }
  
  &__search {
    position: relative;
    
    input {
      padding-left: var(--spacing-8);
      border-radius: var(--border-radius-base);
    }
    
    &-icon {
      position: absolute;
      left: var(--spacing-2);
      top: 50%;
      transform: translateY(-50%);
      color: var(--color-gray-400);
    }
  }
  
  &__length {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    color: var(--color-gray-700);
    
    select {
      border-radius: var(--border-radius-base);
      padding: var(--spacing-1) var(--spacing-2);
    }
  }
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-3);
    
    &__left,
    &__right {
      width: 100%;
      justify-content: center;
    }
  }
}

/* ========================================
   表格状态样式
 ======================================== */

.data-table__status {
  &-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-base);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wide);
    
    &--success {
      background-color: var(--color-success-100);
      color: var(--color-success-800);
    }
    
    &--warning {
      background-color: var(--color-warning-100);
      color: var(--color-warning-800);
    }
    
    &--danger {
      background-color: var(--color-danger-100);
      color: var(--color-danger-800);
    }
    
    &--info {
      background-color: var(--color-info-100);
      color: var(--color-info-800);
    }
  }
}

/* ========================================
   动画效果
 ======================================== */

@keyframes data-table-loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 行展开动画 */
.data-table__body .data-table__row {
  animation: data-table-row-fade-in var(--duration-200) var(--ease-out);
}

@keyframes data-table-row-fade-in {
  from {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式详情展开动画 */
.data-table__responsive-details {
  animation: data-table-details-slide-down var(--duration-300) var(--ease-out);
}

@keyframes data-table-details-slide-down {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 20rem;
  }
}

/* ========================================
   紧凑模式 (.data-table--compact)
 ======================================== */

.data-table--compact {
  .data-table__head .data-table__cell,
  .data-table__body .data-table__cell {
    padding: var(--table-cell-padding-y-sm) var(--table-cell-padding-x-sm);
  }
  
  font-size: var(--font-size-xs);
}