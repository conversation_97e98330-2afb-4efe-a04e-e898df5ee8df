/**
 * 表格管理模块
 * 负责DataTable初始化、增强功能和数据处理
 */

// 确保依赖模块已加载
if (!window.TaixiangApp || !window.TaixiangApp.Utils) {
    throw new Error('TaixiangApp Core 模块未加载，请先加载 app-core.js');
}

// 表格管理器
TaixiangApp.TableManager = {
    // 初始化单个DataTable
    initDataTable: function(tableElement, options = {}) {
        if (!tableElement) {
            console.error('表格元素不存在');
            return null;
        }
        
        // 检查表格是否已初始化
        if ($.fn.DataTable.isDataTable(tableElement)) {
            console.log('表格已初始化，销毁旧实例');
            $(tableElement).DataTable().destroy();
        }
        
        // 默认配置
        const defaultConfig = {
            responsive: {
                details: {
                    type: 'column',
                    target: 0
                }
            },
            columnDefs: [
                {
                    className: 'dtr-control',
                    orderable: false,
                    targets: 0,
                    width: "40px"
                },
                {
                    targets: '_all',
                    className: 'dt-head-center dt-body-center'
                }
            ],
            order: [[1, 'desc']],
            dom: '<"dataTable-top"lf>rt<"dataTable-bottom"ip>',
            language: {
                search: "搜索:",
                lengthMenu: "显示 _MENU_ 条数据",
                info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                infoEmpty: "显示第 0 至 0 项结果，共 0 项",
                infoFiltered: "(由 _MAX_ 项结果过滤)",
                paginate: {
                    first: "首页",
                    previous: "上页",
                    next: "下页",
                    last: "末页"
                },
                zeroRecords: "没有匹配结果",
                emptyTable: "暂无数据",
                loadingRecords: "加载中...",
                processing: "处理中..."
            }
        };
        
        // 合并配置
        const config = Object.assign({}, defaultConfig, options);
        
        try {
            // 初始化DataTable
            const dataTable = $(tableElement).DataTable(config);
            
            // 表格初始化完成后调整响应式布局
            setTimeout(() => {
                dataTable.columns.adjust().responsive.recalc();
            }, 100);
            
            console.log('DataTable初始化成功');
            return dataTable;
        } catch (error) {
            console.error('DataTable初始化失败:', error);
            return null;
        }
    },
    
    // 增强数据表格功能
    enhanceDataTables: function() {
        console.log('开始应用表格增强功能');
        
        const tables = document.querySelectorAll('.data-table');
        if (!tables.length) {
            console.log('页面上没有发现数据表格');
            return;
        }
        
        console.log(`发现 ${tables.length} 个数据表格需要增强`);
        
        tables.forEach((table, index) => {
            try {
                // 确保表格已初始化为DataTable
                if (!$.fn.DataTable.isDataTable(table)) {
                    console.log(`表格 #${index+1} 尚未初始化为DataTable`);
                    return;
                }
                
                // 获取DataTable实例
                const dataTable = $(table).DataTable();
                
                // 应用各种增强功能
                this.applyAdaptiveColumnWidths(table, dataTable);
                this.processLongTextCells(table, dataTable);
                this.enhanceNumericColumns(table, dataTable);
                this.optimizeForMobile(table, dataTable);
                this.optimizePagination(dataTable, window.innerWidth <= 375);
                this.enhanceMobilePagination(table);
                
                // 强制重新计算列宽
                dataTable.columns.adjust().responsive.recalc();
                
                console.log(`表格 #${index+1} 增强功能应用完成`);
            } catch (error) {
                console.error(`应用表格 #${index+1} 增强功能时出错:`, error);
            }
        });
        
        // 设置单元格点击展开功能
        this.setupCellContentExpansion();
    },
    
    // 应用自适应列宽
    applyAdaptiveColumnWidths: function(table, dataTable) {
        const minWidth = 80;
        const maxWidth = 300;
        
        const columns = dataTable.columns();
        const columnCount = columns && columns[0] ? columns[0].length : 0;
        
        // 如果没有列数据，直接返回
        if (columnCount === 0) {
            console.log('表格没有列数据，跳过自适应列宽设置');
            return;
        }
        
        for (let i = 0; i < columnCount; i++) {
            const columnData = dataTable.column(i).data();
            let maxContentLength = 0;
            let hasLongContent = false;
            
            columnData.each(function(value) {
                if (!value || value === '-') return;
                
                const contentLength = String(value).length;
                maxContentLength = Math.max(maxContentLength, contentLength);
                
                if (contentLength > 30) {
                    hasLongContent = true;
                }
            });
            
            const headerText = $(dataTable.column(i).header()).text().trim();
            const headerLength = headerText.length;
            
            let idealWidth = Math.max(headerLength * 12, maxContentLength * 8);
            idealWidth = Math.max(minWidth, Math.min(idealWidth, maxWidth));
            
            if (hasLongContent) {
                $(dataTable.column(i).nodes()).addClass('long-content-column');
            }
        }
    },
    
    // 处理长文本内容
    processLongTextCells: function(table, dataTable) {
        const cells = table.querySelectorAll('td');
        cells.forEach(cell => {
            const text = cell.textContent || cell.innerText;
            if (text && text.length > 50) {
                cell.classList.add('has-long-content');
                cell.title = text; // 添加hover提示
            }
        });
    },
    
    // 增强数字列的可读性
    enhanceNumericColumns: function(table, dataTable) {
        const columns = dataTable.columns();
        
        columns.every(function(index) {
            const column = this;
            const data = column.data();
            
            // 检查是否为数字列
            let isNumericColumn = true;
            data.each(function(value) {
                if (value && value !== '-' && isNaN(parseFloat(value))) {
                    isNumericColumn = false;
                    return false;
                }
            });
            
            if (isNumericColumn) {
                $(column.nodes()).addClass('numeric-column');
            }
        });
    },
    
    // 移动端优化
    optimizeForMobile: function(table, dataTable) {
        if (window.innerWidth <= 768) {
            // 移动端特殊处理
            table.classList.add('mobile-optimized');
            
            // 调整分页器
            const paginateContainer = table.parentElement.querySelector('.dataTables_paginate');
            if (paginateContainer) {
                paginateContainer.classList.add('mobile-pagination');
            }
        }
    },
    
    // 优化分页控件
    optimizePagination: function(dataTable, isSmallScreen) {
        const paginateContainer = $(dataTable.table().container()).find('.dataTables_paginate');
        
        if (isSmallScreen) {
            paginateContainer.addClass('compact-pagination');
        } else {
            paginateContainer.removeClass('compact-pagination');
        }
    },
    
    // 增强移动端分页体验
    enhanceMobilePagination: function(table) {
        if (window.innerWidth <= 768) {
            const paginateContainer = table.parentElement.querySelector('.dataTables_paginate');
            if (paginateContainer) {
                paginateContainer.addEventListener('click', function(e) {
                    // 移动端分页点击后滚动到表格顶部
                    if (e.target.tagName === 'A') {
                        setTimeout(() => {
                            table.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        }, 100);
                    }
                });
            }
        }
    },
    
    // 设置单元格内容展开功能
    setupCellContentExpansion: function() {
        // 移除之前的事件监听器，避免重复绑定
        $(document).off('click', '.cell-content').on('click', '.cell-content', function() {
            $(this).toggleClass('expanded');
        });
    },
    
    // 填充表格数据
    fillTable: function(tableElement, dataSource) {
        if (!tableElement || !dataSource) {
            console.error('表格元素或数据源不存在');
            return;
        }
        
        // 检查数据格式
        if (!dataSource.results || !dataSource.columns) {
            console.error('数据源格式错误');
            return;
        }
        
        // 构建表格HTML
        const tableHtml = this.buildTableHtml(dataSource);
        
        // 更新表格内容
        const container = tableElement.parentElement;
        container.innerHTML = tableHtml;
        
        // 重新获取表格元素并初始化
        const newTable = container.querySelector('.data-table');
        if (newTable) {
            this.initDataTable(newTable);
            this.enhanceDataTables();
        }
    },
    
    // 构建表格HTML
    buildTableHtml: function(dataSource) {
        let html = '<table class="table table-striped table-hover data-table" style="width:100%">';
        html += '<thead><tr><th class="dtr-control"></th>';
        
        // 添加列标题
        dataSource.columns.forEach(column => {
            html += `<th>${column}</th>`;
        });
        html += '</tr></thead><tbody>';
        
        // 添加数据行
        dataSource.results.forEach(row => {
            html += '<tr><td class="dtr-control"></td>';
            dataSource.columns.forEach(column => {
                const value = row[column] !== undefined ? row[column] : '-';
                html += `<td>${value}</td>`;
            });
            html += '</tr>';
        });
        
        html += '</tbody></table>';
        return html;
    }
};

// 向后兼容的全局函数
window.enhanceDataTables = TaixiangApp.TableManager.enhanceDataTables.bind(TaixiangApp.TableManager);
window.initDataTable = TaixiangApp.TableManager.initDataTable.bind(TaixiangApp.TableManager);
window.fillTable = TaixiangApp.TableManager.fillTable.bind(TaixiangApp.TableManager);

console.log('表格管理模块已加载');