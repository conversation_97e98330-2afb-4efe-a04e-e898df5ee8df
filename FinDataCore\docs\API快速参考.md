# FinDataCore API 快速参考

## 🚀 快速开始

### 1. 启动服务
```bash
cd FinDataCore
python run.py
```

### 2. 登录获取Token
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"password": "TT2024"}'
```

### 3. 使用Token访问API
```bash
curl -X GET "http://localhost:8000/api/v1/query/filter_data?date=2025-01-15" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 📋 API端点速查表

| 端点 | 方法 | 权限 | 参数 | 功能 |
|------|------|------|------|------|
| `/api/v1/auth/login` | POST | 无 | `password` | 登录获取Token |
| `/api/v1/auth/me` | GET | read | 无 | 获取用户信息 |
| `/api/v1/query/filter_data` | GET | read | `date` | 日期筛选 |
| `/api/v1/query/filter_orders_by_customer_name` | GET | read | `customer_name` | 客户订单查询 |
| `/api/v1/query/overdue` | GET | read | `page`, `limit`, `search_query` | 逾期订单查询 |
| `/api/v1/stats/customer_summary` | GET | standard | `customer_name` | 客户汇总 |
| `/api/v1/stats/summary_data` | GET | read | `start_date`, `end_date` | 数据汇总 |
| `/api/v1/stats/order_summary` | GET | read | `end_date` | 订单汇总 |
| `/health` | GET | 无 | 无 | 健康检查 |

## 🔑 用户权限

| 密码 | 权限级别 | 可访问功能 |
|------|----------|------------|
| `TT2024` | limited | 基础查询功能 |
| `881017` | standard | 查询 + 客户汇总 |
| `Doolin` | full | 所有功能 |

## 🔧 常用命令

### 完整测试流程
```bash
# 获取Token
TOKEN=$(curl -s -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"password": "TT2024"}' | jq -r '.access_token')

# 日期筛选
curl -X GET "http://localhost:8000/api/v1/query/filter_data?date=2025-01-15" \
  -H "Authorization: Bearer $TOKEN"

# 客户查询
curl -X GET "http://localhost:8000/api/v1/query/filter_orders_by_customer_name?customer_name=张三" \
  -H "Authorization: Bearer $TOKEN"

# 逾期订单
curl -X GET "http://localhost:8000/api/v1/query/overdue?page=1&limit=10" \
  -H "Authorization: Bearer $TOKEN"
```

### JavaScript示例
```javascript
// 登录
const response = await fetch('/api/v1/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ password: 'TT2024' })
});
const { access_token } = await response.json();

// 查询数据
const data = await fetch('/api/v1/query/filter_data?date=2025-01-15', {
    headers: { 'Authorization': `Bearer ${access_token}` }
}).then(res => res.json());
```

## ❌ 常见错误

| 状态码 | 错误 | 解决方案 |
|--------|------|----------|
| 401 | 未认证 | 检查Token是否有效 |
| 403 | 权限不足 | 使用更高权限的账号 |
| 422 | 参数错误 | 检查参数格式和必需字段 |
| 500 | 服务器错误 | 检查服务状态和日志 |

## 🔗 重要链接

- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **详细文档**: `docs/API接口文档.md`
