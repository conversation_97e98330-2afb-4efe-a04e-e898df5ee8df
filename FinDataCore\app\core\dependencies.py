"""
FastAPI依赖注入
"""
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.core.security import verify_token, get_current_user, User, TokenData

# HTTP Bearer认证方案
security = HTTPBearer()

async def get_token_data(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> TokenData:
    """
    获取并验证令牌数据
    
    Args:
        credentials: HTTP认证凭据
        
    Returns:
        TokenData对象
        
    Raises:
        HTTPException: 认证失败时抛出
    """
    token = credentials.credentials
    token_data = verify_token(token)
    
    if token_data is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return token_data

async def get_current_active_user(
    token_data: TokenData = Depends(get_token_data)
) -> User:
    """
    获取当前活跃用户
    
    Args:
        token_data: 令牌数据
        
    Returns:
        User对象
    """
    return get_current_user(token_data)

def require_permission(permission: str):
    """
    权限要求装饰器工厂
    
    Args:
        permission: 所需权限
        
    Returns:
        依赖函数
    """
    async def permission_dependency(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        if permission not in current_user.permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        return current_user
    
    return permission_dependency

def require_user_level(level: str):
    """
    用户级别要求装饰器工厂
    
    Args:
        level: 所需用户级别
        
    Returns:
        依赖函数
    """
    async def level_dependency(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        from app.core.security import has_permission
        
        if not has_permission(current_user.user_level, level):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"User level '{level}' required"
            )
        return current_user
    
    return level_dependency

# 常用的权限依赖
require_read_permission = require_permission("read")
require_export_permission = require_permission("export")
require_admin_permission = require_permission("admin")

# 常用的用户级别依赖
require_standard_user = require_user_level("standard")
require_full_user = require_user_level("full")
