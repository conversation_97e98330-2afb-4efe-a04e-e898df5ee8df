"""
外部API客户端 - 异步版本
"""
import asyncio
import hashlib
import logging
from typing import Dict, Any, Optional, List
import httpx
from app.core.config import settings

logger = logging.getLogger(__name__)

# 备用API路径配置
FALLBACK_ENDPOINTS = {
    "query": "filter_data_db",
    "overdue": "filter_overdue_orders_db", 
    "customer": "filter_orders_by_customer_name_db",
    "summary": "summary_data_db"
}

class ExternalApiClient:
    """外部API客户端类 - 异步版本"""
    
    def __init__(self):
        """初始化API客户端"""
        self.logger = logging.getLogger(__name__)
        self.timeout = settings.API_TIMEOUT
        self.retry_times = settings.API_RETRY_TIMES
        self.base_url = settings.API_BASE_URL
        self.fallback_urls = settings.API_FALLBACK_URLS
        self.api_key = settings.API_KEY
        
    async def make_api_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        use_fallback: bool = False,
        url_index: int = 0
    ) -> Dict[str, Any]:
        """
        发送异步API请求到外部API服务器
        
        Args:
            method: 请求方法 ('GET', 'POST' 等)
            endpoint: API端点路径
            params: URL参数字典
            data: POST数据
            use_fallback: 是否使用备用路径
            url_index: 当前尝试的URL索引
            
        Returns:
            API响应数据
        """
        if params is None:
            params = {}
        
        # 确保API密钥存在
        params["api_key"] = self.api_key
        
        # 构建URL
        if url_index == 0:
            base_url = self.base_url
        else:
            try:
                base_url = self.fallback_urls[url_index - 1]
            except IndexError:
                return {"error": "所有API服务器都无法访问"}
        
        # 处理端点映射
        if use_fallback and endpoint in FALLBACK_ENDPOINTS:
            endpoint = FALLBACK_ENDPOINTS[endpoint]
        elif not endpoint.endswith("_db") and endpoint not in ["ping", "version"]:
            endpoint = f"{endpoint}_db"
        
        url = f"{base_url}/{endpoint}"
        
        self.logger.info(f"[URL#{url_index}] 发送 {method} 请求到: {url}")
        self.logger.info(f"请求参数: {params}")
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                if method.upper() == "GET":
                    response = await client.get(url, params=params)
                elif method.upper() == "POST":
                    response = await client.post(url, params=params, json=data)
                else:
                    raise ValueError(f"不支持的请求方法: {method}")
                
                self.logger.info(f"[URL#{url_index}] 响应状态码: {response.status_code}")
                
                # 检查响应状态
                response.raise_for_status()
                
                # 解析JSON响应
                try:
                    result = response.json()
                    self.logger.info(f"[URL#{url_index}] API请求成功")
                    return result
                except Exception as e:
                    self.logger.error(f"[URL#{url_index}] JSON解析失败: {str(e)}")
                    return {"error": f"响应格式错误: {str(e)}"}
                    
        except httpx.TimeoutException as e:
            self.logger.error(f"[URL#{url_index}] API请求超时: {str(e)}")
            return await self._handle_timeout_error(method, endpoint, params, data, use_fallback, url_index)
            
        except httpx.HTTPStatusError as e:
            self.logger.error(f"[URL#{url_index}] HTTP错误 {e.response.status_code}: {e.response.text}")
            return await self._handle_http_error(method, endpoint, params, data, use_fallback, url_index, e)
            
        except Exception as e:
            self.logger.error(f"[URL#{url_index}] 请求异常: {str(e)}")
            return await self._handle_general_error(method, endpoint, params, data, use_fallback, url_index, e)
    
    async def _handle_timeout_error(
        self, method: str, endpoint: str, params: Dict, data: Dict,
        use_fallback: bool, url_index: int
    ) -> Dict[str, Any]:
        """处理超时错误"""
        # 尝试下一个URL
        next_url_index = url_index + 1
        if next_url_index - 1 < len(self.fallback_urls) or next_url_index == 1:
            self.logger.info(f"尝试下一个API基础URL (索引: {next_url_index})")
            return await self.make_api_request(method, endpoint, params, data, use_fallback, next_url_index)
        
        # 如果未使用备用路径且存在备用端点，则尝试使用备用路径
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            self.logger.info(f"尝试使用备用端点: {FALLBACK_ENDPOINTS[endpoint]}")
            return await self.make_api_request(method, endpoint, params, data, True, 0)
            
        return {"error": "服务器响应超时，请稍后再试"}
    
    async def _handle_http_error(
        self, method: str, endpoint: str, params: Dict, data: Dict,
        use_fallback: bool, url_index: int, error: httpx.HTTPStatusError
    ) -> Dict[str, Any]:
        """处理HTTP错误"""
        if error.response.status_code >= 500:
            # 服务器错误，尝试其他URL
            return await self._handle_timeout_error(method, endpoint, params, data, use_fallback, url_index)
        else:
            # 客户端错误，直接返回
            return {"error": f"HTTP {error.response.status_code}: {error.response.text}"}
    
    async def _handle_general_error(
        self, method: str, endpoint: str, params: Dict, data: Dict,
        use_fallback: bool, url_index: int, error: Exception
    ) -> Dict[str, Any]:
        """处理一般错误"""
        # 尝试下一个URL
        return await self._handle_timeout_error(method, endpoint, params, data, use_fallback, url_index)

    async def get_filter_data(self, start_date: str = None, end_date: str = None, date: str = None) -> Dict[str, Any]:
        """
        获取筛选数据

        Args:
            start_date: 开始日期
            end_date: 结束日期
            date: 单个日期（用于日期筛选）

        Returns:
            筛选数据结果
        """
        self.logger.info(f"获取筛选数据: start_date={start_date}, end_date={end_date}, date={date}")
        params = {}

        if date:
            params["date"] = date
        if start_date:
            params["start_date"] = start_date
        if end_date:
            params["end_date"] = end_date

        # 添加时间戳防止缓存
        params["_t"] = hashlib.md5(str(self.api_key).encode()).hexdigest()

        return await self.make_api_request("GET", "filter_data_db", params)

    async def get_customer_data(self, customer_name: str) -> Dict[str, Any]:
        """
        获取客户数据

        Args:
            customer_name: 客户姓名

        Returns:
            客户数据结果
        """
        self.logger.info(f"获取客户数据: customer_name={customer_name}")
        params = {"customer_name": customer_name}

        # 添加时间戳防止缓存
        params["_t"] = hashlib.md5(str(self.api_key).encode()).hexdigest()

        return await self.make_api_request("GET", "filter_orders_by_customer_name_db", params)

    async def get_overdue_data(self, limit: int = 100000) -> Dict[str, Any]:
        """
        获取逾期数据

        Args:
            limit: 数据限制数量

        Returns:
            逾期数据结果
        """
        self.logger.info("获取逾期数据")
        params = {"limit": limit}

        return await self.make_api_request("GET", "filter_overdue_orders_db", params)

    async def get_customer_summary(self, customer_name: str) -> Dict[str, Any]:
        """
        获取客户汇总数据

        Args:
            customer_name: 客户姓名

        Returns:
            客户汇总数据
        """
        self.logger.info(f"获取客户汇总数据: customer_name={customer_name}")
        params = {"customer_name": customer_name}

        return await self.make_api_request("GET", "customer_summary_db", params)

    async def get_summary_data(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """
        获取汇总数据

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            汇总数据结果
        """
        self.logger.info(f"获取汇总数据: start_date={start_date}, end_date={end_date}")
        params = {"start_date": start_date, "end_date": end_date}

        return await self.make_api_request("GET", "summary_data_db", params)

    async def get_order_summary(self, end_date: str) -> Dict[str, Any]:
        """
        获取订单汇总数据

        Args:
            end_date: 结束日期

        Returns:
            订单汇总数据
        """
        self.logger.info(f"获取订单汇总数据: end_date={end_date}")
        params = {"end_date": end_date}

        return await self.make_api_request("GET", "order_summary_db", params)

    async def get_overdue_summary(self, end_date: str) -> Dict[str, Any]:
        """
        获取逾期订单汇总数据

        Args:
            end_date: 结束日期

        Returns:
            逾期订单汇总数据
        """
        self.logger.info(f"获取逾期订单汇总数据: end_date={end_date}")
        params = {"end_date": end_date}

        return await self.make_api_request("GET", "overdue_summary_db", params)

    async def check_status(self) -> Dict[str, Any]:
        """
        检查API状态

        Returns:
            API状态信息
        """
        self.logger.info("检查API状态")
        try:
            result = await self.make_api_request("GET", "ping", {})
            return {"status": "ok", "external_api": result}
        except Exception as e:
            return {"status": "error", "message": str(e)}

# 创建全局API客户端实例
api_client = ExternalApiClient()
