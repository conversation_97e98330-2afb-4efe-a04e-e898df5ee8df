"""
应用配置管理
"""
from pydantic_settings import BaseSettings
from typing import List, Dict, Any
import os
from pathlib import Path

class Settings(BaseSettings):
    """应用设置类"""
    
    # 基础配置
    PROJECT_NAME: str = "FinDataCore"
    VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 480  # 8小时
    ALGORITHM: str = "HS256"
    
    # 数据库配置
    DATABASE_URI: str = "******************************************************/flask_db"
    
    # 外部API配置
    API_KEY: str = "lxw8025031"
    API_BASE_URL: str = "http://*************:5000"
    API_TIMEOUT: int = 30
    API_RETRY_TIMES: int = 3
    
    # 备用API服务器
    API_FALLBACK_URLS: List[str] = [
        "http://*************:5000/api",
        "http://*************:8088",
        "http://*************"
    ]
    
    # 日志配置
    LOG_FILE_PATH: str = "logs/server.log"
    LOG_LEVEL: str = "INFO"
    
    # 缓存配置
    CACHE_TYPE: str = "memory"  # memory, redis
    CACHE_DEFAULT_TIMEOUT: int = 300  # 5分钟
    CACHE_REDIS_URL: str = "redis://localhost:6379/0"
    
    # 不同类型数据的缓存时间设置（秒）
    CACHE_TIMEOUTS: Dict[str, int] = {
        "filter_data": 300,         # 筛选数据：5分钟
        "filter_overdue_orders": 600,  # 逾期订单：10分钟
        "filter_orders_by_customer_name": 600,  # 客户订单：10分钟
        "summary_data": 1800,       # 汇总数据：30分钟
        "order_summary": 1800,      # 订单汇总：30分钟
        "customer_summary": 600     # 客户汇总：10分钟
    }
    
    # 用户权限配置
    USER_LEVELS: Dict[str, str] = {
        "TT2024": "limited",   # 有限权限
        "881017": "standard",  # 标准权限
        "Doolin": "full"       # 完全权限
    }
    
    # 状态颜色映射
    STATUS_COLORS: Dict[str, str] = {
        "逾期还款": "#FFFFEB9C",
        "提前还款": "#FFD9E1F2",
        "按时还款": "#FFC6EFCE",
        "账单日": "#FFF4B084",
        "逾期未还": "#FFFFC7CE",
        "无效日期": "#FFDEDEDE",
        "未到还款日期": "#FFFFFFFF",
        "催收": "#FFFFEB9C",
        "诉讼": "#FFFFC7CE",
        "未知状态": "#FFC0C0C0"
    }
    
    # CORS配置
    ALLOWED_HOSTS: List[str] = ["*"]
    CORS_ORIGINS: List[str] = ["*"]
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建设置实例
settings = Settings()

# 确保日志目录存在
log_dir = Path(settings.LOG_FILE_PATH).parent
log_dir.mkdir(parents=True, exist_ok=True)
