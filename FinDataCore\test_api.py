#!/usr/bin/env python3
"""
API测试脚本
"""
import asyncio
import httpx
import json
import sys
from typing import Dict, Any

# API基础URL
BASE_URL = "http://localhost:8000"

class ApiTester:
    """API测试器"""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.token = None
        
    async def login(self, password: str) -> bool:
        """
        登录获取token
        
        Args:
            password: 登录密码
            
        Returns:
            是否登录成功
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/api/v1/auth/login",
                    json={"password": password}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    self.token = data["access_token"]
                    print(f"✅ 登录成功，用户级别: {data['user_level']}")
                    return True
                else:
                    print(f"❌ 登录失败: {response.text}")
                    return False
                    
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return False
    
    def get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        if not self.token:
            return {}
        return {"Authorization": f"Bearer {self.token}"}
    
    async def test_health(self):
        """测试健康检查"""
        print("\n🔍 测试健康检查...")
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/health")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 健康检查通过: {data}")
                else:
                    print(f"❌ 健康检查失败: {response.text}")
                    
        except Exception as e:
            print(f"❌ 健康检查异常: {str(e)}")
    
    async def test_filter_data(self, date: str = "2025-01-15"):
        """测试日期筛选API"""
        print(f"\n🔍 测试日期筛选API (日期: {date})...")
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/api/v1/query/filter_data",
                    params={"date": date},
                    headers=self.get_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 筛选成功，返回 {data.get('total', 0)} 条记录")
                    if data.get('results'):
                        print(f"   示例数据: {json.dumps(data['results'][0], ensure_ascii=False, indent=2)}")
                else:
                    print(f"❌ 筛选失败: {response.text}")
                    
        except Exception as e:
            print(f"❌ 筛选异常: {str(e)}")
    
    async def test_customer_orders(self, customer_name: str = "张三"):
        """测试客户订单查询API"""
        print(f"\n🔍 测试客户订单查询API (客户: {customer_name})...")
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/api/v1/query/filter_orders_by_customer_name",
                    params={"customer_name": customer_name},
                    headers=self.get_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 查询成功，返回 {data.get('total', 0)} 条记录")
                else:
                    print(f"❌ 查询失败: {response.text}")
                    
        except Exception as e:
            print(f"❌ 查询异常: {str(e)}")
    
    async def test_overdue_orders(self):
        """测试逾期订单查询API"""
        print("\n🔍 测试逾期订单查询API...")
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/api/v1/query/overdue",
                    params={"page": 1, "limit": 5},
                    headers=self.get_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    pagination = data.get('pagination', {})
                    print(f"✅ 查询成功，第{pagination.get('page', 1)}页，共{pagination.get('total', 0)}条记录")
                else:
                    print(f"❌ 查询失败: {response.text}")
                    
        except Exception as e:
            print(f"❌ 查询异常: {str(e)}")
    
    async def test_customer_summary(self, customer_name: str = "张三"):
        """测试客户汇总API"""
        print(f"\n🔍 测试客户汇总API (客户: {customer_name})...")
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/api/v1/stats/customer_summary",
                    params={"customer_name": customer_name},
                    headers=self.get_headers()
                )
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 汇总成功")
                    print(f"   客户信息: {data.get('customer_info', {})}")
                else:
                    print(f"❌ 汇总失败: {response.text}")
                    
        except Exception as e:
            print(f"❌ 汇总异常: {str(e)}")
    
    async def test_api_docs(self):
        """测试API文档"""
        print("\n🔍 测试API文档...")
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/docs")
                
                if response.status_code == 200:
                    print("✅ API文档可访问")
                    print(f"   文档地址: {self.base_url}/docs")
                else:
                    print(f"❌ API文档不可访问: {response.status_code}")
                    
        except Exception as e:
            print(f"❌ API文档异常: {str(e)}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始API测试...")
        
        # 测试健康检查
        await self.test_health()
        
        # 测试API文档
        await self.test_api_docs()
        
        # 登录测试
        print("\n🔐 测试登录...")
        login_success = await self.login("TT2024")  # 使用有限权限账号
        
        if login_success:
            # 测试查询API
            await self.test_filter_data()
            await self.test_customer_orders()
            await self.test_overdue_orders()
            
            # 尝试标准权限功能（可能失败）
            await self.test_customer_summary()
        
        print("\n✨ 测试完成！")

async def main():
    """主函数"""
    print("FinDataCore API 测试工具")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/health", timeout=5.0)
            if response.status_code != 200:
                print(f"❌ 服务器未运行或不健康: {response.status_code}")
                return
    except Exception as e:
        print(f"❌ 无法连接到服务器: {str(e)}")
        print(f"请确保服务器在 {BASE_URL} 上运行")
        return
    
    # 运行测试
    tester = ApiTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
