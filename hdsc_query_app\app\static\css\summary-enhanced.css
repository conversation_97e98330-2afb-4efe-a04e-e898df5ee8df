/**
 * 增强型汇总数据展示样式
 */

/* 核心指标卡片样式 */
.stat-icon {
    font-size: 1.75rem;
    opacity: 0.8;
}

.progress {
    height: 4px;
    margin-bottom: 2px;
}

/* 负值文本样式 */
.negative-value {
    color: #dc3545 !important;
}

/* 指标卡片视图样式 */
.metric-card {
    transition: all 0.2s ease;
    border-radius: 0.25rem;
    border: 1px solid rgba(0,0,0,.125);
    height: 100%;
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.metric-value {
    font-size: 1.25rem;
    font-weight: 500;
}

/* 汇总表格样式改进 */
.data-table th {
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 1;
    text-align: center;
}

.data-table td {
    vertical-align: middle;
    text-align: center;
}

/* 指标分组样式 */
.metrics-group {
    margin-bottom: 1.5rem;
}

.metrics-group-header {
    padding: 0.5rem 0;
    margin-bottom: 0.5rem;
    border-bottom: 2px solid #f0f0f0;
}

/* 视图切换下拉菜单样式 */
.view-selector .dropdown-item.active {
    background-color: #007bff;
    color: white;
}

/* 小屏幕响应式调整 */
@media (max-width: 768px) {
    .nav-tabs .nav-link, .nav-pills .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
    }
    
    h3 {
        font-size: 1.5rem;
    }
    
    .card-body.py-2 {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
    }
    
    .metric-value {
        font-size: 1.1rem;
    }
    
    .btn-toolbar .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}
