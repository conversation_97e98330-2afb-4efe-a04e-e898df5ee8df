"""
性能监控和优化工具
"""
import time
import asyncio
import logging
import psutil
from typing import Dict, Any, List, Optional, Callable
from functools import wraps
from datetime import datetime, timedelta
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_records: int = 1000):
        """
        初始化性能监控器
        
        Args:
            max_records: 最大记录数
        """
        self.max_records = max_records
        self.api_stats = defaultdict(lambda: deque(maxlen=max_records))
        self.system_stats = deque(maxlen=max_records)
        self.error_stats = defaultdict(lambda: deque(maxlen=max_records))
        
    def record_api_call(
        self, 
        endpoint: str, 
        method: str, 
        duration: float, 
        status_code: int,
        user_id: Optional[str] = None
    ):
        """
        记录API调用统计
        
        Args:
            endpoint: API端点
            method: HTTP方法
            duration: 执行时间（秒）
            status_code: 状态码
            user_id: 用户ID
        """
        record = {
            "timestamp": datetime.now(),
            "endpoint": endpoint,
            "method": method,
            "duration": duration,
            "status_code": status_code,
            "user_id": user_id
        }
        
        key = f"{method}:{endpoint}"
        self.api_stats[key].append(record)
        
        # 记录错误
        if status_code >= 400:
            self.error_stats[key].append(record)
    
    def record_system_stats(self):
        """记录系统统计信息"""
        try:
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            record = {
                "timestamp": datetime.now(),
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_used": memory.used,
                "memory_total": memory.total,
                "disk_percent": disk.percent,
                "disk_used": disk.used,
                "disk_total": disk.total
            }
            
            self.system_stats.append(record)
        except Exception as e:
            logger.error(f"记录系统统计失败: {str(e)}")
    
    def get_api_stats(
        self, 
        endpoint: Optional[str] = None, 
        time_window: int = 3600
    ) -> Dict[str, Any]:
        """
        获取API统计信息
        
        Args:
            endpoint: 特定端点（None表示所有端点）
            time_window: 时间窗口（秒）
            
        Returns:
            统计信息字典
        """
        cutoff_time = datetime.now() - timedelta(seconds=time_window)
        stats = {}
        
        for key, records in self.api_stats.items():
            if endpoint and endpoint not in key:
                continue
            
            # 过滤时间窗口内的记录
            recent_records = [
                r for r in records 
                if r["timestamp"] >= cutoff_time
            ]
            
            if not recent_records:
                continue
            
            # 计算统计信息
            durations = [r["duration"] for r in recent_records]
            status_codes = [r["status_code"] for r in recent_records]
            
            stats[key] = {
                "total_calls": len(recent_records),
                "avg_duration": sum(durations) / len(durations),
                "min_duration": min(durations),
                "max_duration": max(durations),
                "success_rate": len([s for s in status_codes if s < 400]) / len(status_codes) * 100,
                "error_rate": len([s for s in status_codes if s >= 400]) / len(status_codes) * 100
            }
        
        return stats
    
    def get_system_stats(self, time_window: int = 3600) -> Dict[str, Any]:
        """
        获取系统统计信息
        
        Args:
            time_window: 时间窗口（秒）
            
        Returns:
            系统统计信息
        """
        cutoff_time = datetime.now() - timedelta(seconds=time_window)
        
        # 过滤时间窗口内的记录
        recent_records = [
            r for r in self.system_stats 
            if r["timestamp"] >= cutoff_time
        ]
        
        if not recent_records:
            return {}
        
        # 计算平均值
        cpu_values = [r["cpu_percent"] for r in recent_records]
        memory_values = [r["memory_percent"] for r in recent_records]
        disk_values = [r["disk_percent"] for r in recent_records]
        
        return {
            "avg_cpu_percent": sum(cpu_values) / len(cpu_values),
            "max_cpu_percent": max(cpu_values),
            "avg_memory_percent": sum(memory_values) / len(memory_values),
            "max_memory_percent": max(memory_values),
            "avg_disk_percent": sum(disk_values) / len(disk_values),
            "max_disk_percent": max(disk_values),
            "sample_count": len(recent_records)
        }
    
    def get_error_summary(self, time_window: int = 3600) -> Dict[str, Any]:
        """
        获取错误汇总
        
        Args:
            time_window: 时间窗口（秒）
            
        Returns:
            错误汇总信息
        """
        cutoff_time = datetime.now() - timedelta(seconds=time_window)
        error_summary = {}
        
        for key, records in self.error_stats.items():
            # 过滤时间窗口内的记录
            recent_errors = [
                r for r in records 
                if r["timestamp"] >= cutoff_time
            ]
            
            if recent_errors:
                status_codes = defaultdict(int)
                for error in recent_errors:
                    status_codes[error["status_code"]] += 1
                
                error_summary[key] = {
                    "total_errors": len(recent_errors),
                    "status_codes": dict(status_codes),
                    "latest_error": max(recent_errors, key=lambda x: x["timestamp"])
                }
        
        return error_summary

# 全局性能监控器实例
performance_monitor = PerformanceMonitor()

def monitor_performance(func: Callable) -> Callable:
    """
    性能监控装饰器
    
    Args:
        func: 要监控的函数
        
    Returns:
        装饰后的函数
    """
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        endpoint = getattr(func, '__name__', 'unknown')
        method = 'ASYNC'
        status_code = 200
        
        try:
            result = await func(*args, **kwargs)
            return result
        except Exception as e:
            status_code = 500
            logger.error(f"函数 {endpoint} 执行失败: {str(e)}")
            raise
        finally:
            duration = time.time() - start_time
            performance_monitor.record_api_call(
                endpoint=endpoint,
                method=method,
                duration=duration,
                status_code=status_code
            )
    
    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        endpoint = getattr(func, '__name__', 'unknown')
        method = 'SYNC'
        status_code = 200
        
        try:
            result = func(*args, **kwargs)
            return result
        except Exception as e:
            status_code = 500
            logger.error(f"函数 {endpoint} 执行失败: {str(e)}")
            raise
        finally:
            duration = time.time() - start_time
            performance_monitor.record_api_call(
                endpoint=endpoint,
                method=method,
                duration=duration,
                status_code=status_code
            )
    
    # 根据函数类型返回相应的包装器
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper

class RateLimiter:
    """速率限制器"""
    
    def __init__(self, max_requests: int = 100, time_window: int = 60):
        """
        初始化速率限制器
        
        Args:
            max_requests: 最大请求数
            time_window: 时间窗口（秒）
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = defaultdict(lambda: deque())
    
    def is_allowed(self, identifier: str) -> bool:
        """
        检查是否允许请求
        
        Args:
            identifier: 标识符（如用户ID、IP地址）
            
        Returns:
            是否允许
        """
        now = time.time()
        cutoff_time = now - self.time_window
        
        # 清理过期记录
        user_requests = self.requests[identifier]
        while user_requests and user_requests[0] < cutoff_time:
            user_requests.popleft()
        
        # 检查是否超过限制
        if len(user_requests) >= self.max_requests:
            return False
        
        # 记录新请求
        user_requests.append(now)
        return True
    
    def get_remaining_requests(self, identifier: str) -> int:
        """
        获取剩余请求数
        
        Args:
            identifier: 标识符
            
        Returns:
            剩余请求数
        """
        now = time.time()
        cutoff_time = now - self.time_window
        
        # 清理过期记录
        user_requests = self.requests[identifier]
        while user_requests and user_requests[0] < cutoff_time:
            user_requests.popleft()
        
        return max(0, self.max_requests - len(user_requests))

# 全局速率限制器实例
rate_limiter = RateLimiter()

class CacheOptimizer:
    """缓存优化器"""
    
    @staticmethod
    def generate_cache_key(*args, **kwargs) -> str:
        """
        生成缓存键
        
        Args:
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            缓存键
        """
        import hashlib
        import json
        
        # 将参数转换为字符串
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items())
        }
        
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    @staticmethod
    def should_cache(result: Any) -> bool:
        """
        判断是否应该缓存结果
        
        Args:
            result: 结果数据
            
        Returns:
            是否应该缓存
        """
        # 不缓存错误结果
        if isinstance(result, dict) and 'error' in result:
            return False
        
        # 不缓存空结果
        if not result:
            return False
        
        return True
