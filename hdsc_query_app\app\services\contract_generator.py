import os
import re
import logging
import shutil
import calendar
import pandas as pd

from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.oxml.ns import nsdecls
from docx.oxml import parse_xml


class AppConfig:
    """应用配置和常量管理"""
    
    # 基础目录
    @staticmethod
    def get_base_dir():
        """获取应用的基础目录"""
        return os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    BASE_DIR = get_base_dir.__func__()
    
    # 产品配置
    class Products:
        """产品相关配置"""
        
        # A组/新版本配置
        GROUP_A = {
            "DEVICE_PRICE": 1979.9,
            "EXTENDED_WARRANTY": 1799.85,
            "DOWN_PAYMENT": 119.6,
            "ADDITIONAL_AMOUNT": 119.6,
        }
        
        # B组/旧版本配置
        GROUP_B = {
            "DEVICE_PRICE": 1.7,
            "EXTENDED_WARRANTY": 1499.85,
            "DOWN_PAYMENT": 119.6,
            "ADDITIONAL_AMOUNT": 119.6,
        }
        
        # 产品签收回执配置
        RECEIPT_CONFIG = {
            "月还": {
                "template": "产品签收回执单.docx",
                "monthly_payment": 2599.5,
                "duration": {"months": 6},
            },
            "天还": {
                "template": "产品签收回执单_天还.docx",
                "monthly_payment": 1979.9,
                "duration": {"days": 60},
                "fixed_merchant": "太原市迎泽区刚刚通讯设备店（个体工商户）",
            },
            "新月还": {
                "template": "产品签收回执单.docx",
                "monthly_payment": 2274,
                "duration": {"months": 6},
            },
        }
    
    # 商户配置
    class Merchants:
        """商户相关配置"""
        
        @staticmethod
        def get_options():
            """获取商户选项"""
            return {
                "涛涛租物": "太原市迎泽区涛涛通讯设备店（个体工商户）",
                "刚刚好物": "太原市迎泽区刚刚通讯设备店（个体工商户）",
                "梦缘商贸": "太原梦缘商贸有限公司",
            }


class DocUtils:
    """文档处理工具类"""
    
    @staticmethod
    def get_next_payment_date(current_date, original_day=None):
        """
        计算下一个付款日期
        
        Args:
            current_date (datetime): 当前日期
            original_day (int, optional): 原始付款日. 默认为None (使用current_date的日)
            
        Returns:
            datetime: 下一个付款日期
        """
        if original_day is None:
            original_day = current_date.day
            
        year = current_date.year
        month = current_date.month
        
        # 计算下一个月
        next_month = month + 1
        next_year = year
        if next_month > 12:
            next_month = 1
            next_year += 1
            
        # 获取下一个月的最后一天
        _, last_day = calendar.monthrange(next_year, next_month)
        
        # 对于付款日的处理：尝试使用original_day，如果超出了，使用该月的最后一天
        target_day = min(original_day, last_day)
            
        return datetime(next_year, next_month, target_day)
    
    @staticmethod
    def add_border_to_table(table):
        """为表格添加边框"""
        for cell in table._cells:
            tcPr = cell._element.get_or_add_tcPr()
            tcBorders = parse_xml(
                r'<w:tcBorders %s>'
                r'<w:top w:val="single"/>'
                r'<w:left w:val="single"/>'
                r'<w:bottom w:val="single"/>'
                r'<w:right w:val="single"/>'
                r'</w:tcBorders>' % nsdecls("w")
            )
            tcPr.append(tcBorders)
    
    @staticmethod
    def set_table_cell_font(cell, font_size=8, font_name="等线"):
        """设置表格单元格中的字体"""
        for paragraph in cell.paragraphs:
            for run in paragraph.runs:
                run.font.size = Pt(font_size)
                run.font.name = font_name
    
    @staticmethod
    def insert_table_at_paragraph(doc, table, paragraph_index):
        """在指定段落后插入表格"""
        tbl = table._element
        tbl.getparent().remove(tbl)
        doc.paragraphs[paragraph_index]._element.addnext(tbl)
    
    @staticmethod
    def replace_placeholders(doc, placeholders, center_vars=None):
        """替换文档中的占位符
        
        Args:
            doc (Document): Word文档对象
            placeholders (dict): 占位符及其替换值
            center_vars (list, optional): 需要居中的变量列表. 默认为None
        
        Returns:
            Document: 处理后的文档对象
        """
        if center_vars is None:
            center_vars = []
            
        def process_paragraph(paragraph):
            for key, value in placeholders.items():
                if key in paragraph.text:
                    paragraph.text = paragraph.text.replace(key, value)
                    
                    # 设置所有替换的文本为11号字体
                    for run in paragraph.runs:
                        run.font.size = Pt(11)
                        run.font.name = "等线"
                    
                    # 对需要居中的部分应用居中效果
                    if key in center_vars:
                        paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

        # 处理段落中的占位符
        for paragraph in doc.paragraphs:
            process_paragraph(paragraph)
            
        # 处理表格中的占位符
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        process_paragraph(paragraph)
                        
        return doc
    
    @staticmethod
    def add_rent_payment_table(doc, total_periods, rent_per_period, start_date, insert_index):
        """添加租金支付表"""
        table = doc.add_table(rows=1, cols=3)
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = "租期"
        hdr_cells[1].text = "租金"
        hdr_cells[2].text = "租金付款日"

        # 记住原始日期，用于一致的付款日计算
        original_day = start_date.day
        
        # 第一个付款日期
        current_payment_date = DocUtils.get_next_payment_date(start_date, original_day)

        for i in range(total_periods):
            row_cells = table.add_row().cells
            row_cells[0].text = f"第{i+1}期"
            row_cells[1].text = f"{rent_per_period:.2f}"
            row_cells[2].text = current_payment_date.strftime("%Y-%m-%d")

            # 设置表格字体
            for cell in row_cells:
                DocUtils.set_table_cell_font(cell)

            # 计算下一个月的账单日，始终尝试使用原始日期
            current_payment_date = DocUtils.get_next_payment_date(
                current_payment_date, original_day
            )

        # 添加表格边框
        DocUtils.add_border_to_table(table)

        # 在指定位置插入表格
        DocUtils.insert_table_at_paragraph(doc, table, insert_index)


class ReceiptGenerator:
    """回执单生成器"""
    
    CENTER_VARS = ["[订单编号]", "[型号]", "[串号]", "[时间]", "[截至时间]", "[月租]"]
    
    def __init__(self):
        self.base_dir = AppConfig.BASE_DIR
        self.template_dir = os.path.join(self.base_dir, "HZ")
        # 确保目录存在
        os.makedirs(self.template_dir, exist_ok=True)
        self._verify_resources()

    def _verify_resources(self):
        """验证必要的模板文件存在"""
        required_templates = {
            "月还": "产品签收回执单.docx",
            "天还": "产品签收回执单_天还.docx",
            "新月还": "产品签收回执单.docx",
        }
        missing = []
        for product, template in required_templates.items():
            path = os.path.join(self.template_dir, template)
            if not os.path.exists(path):
                missing.append(f"{product} 模板: {template}")
                # 创建空白文档
                try:
                    doc = Document()
                    # 添加一些基本说明文字
                    doc.add_paragraph("这是一个自动创建的回执单模板")
                    doc.add_paragraph("请替换此文件为正确的回执单模板")
                    doc.add_paragraph("模板中可以使用以下占位符：")
                    doc.add_paragraph("[姓名] - 客户姓名")
                    doc.add_paragraph("[订单编号] - 订单编号")
                    doc.add_paragraph("[型号] - 设备型号")
                    doc.add_paragraph("[串号] - 设备串号")
                    doc.add_paragraph("[时间] - 当前日期")
                    doc.add_paragraph("[截至时间] - 租期结束日期") 
                    doc.add_paragraph("[出租商户] - 商户名称")
                    doc.add_paragraph("[月租] - 月租金额")
                    doc.add_paragraph("[年][月][日] - 当前日期各部分")
                    doc.save(path)
                    logging.warning(f"已创建空白模板文件: {path}")
                except Exception as e:
                    logging.error(f"创建模板文件失败: {path}, 错误: {str(e)}")
                    
        if missing:
            error_msg = "缺少必要模板文件，已创建空白模板：\n" + "\n".join(missing)
            logging.warning(error_msg)

    def generate(self, product_type, placeholders, merchant):
        """生成回执单
        
        Args:
            product_type (str): 产品类型
            placeholders (dict): 占位符和替换值
            merchant (str): 商户名称
            
        Returns:
            str: 生成的文件路径
        """
        try:
            config = AppConfig.Products.RECEIPT_CONFIG.get(product_type)
            if not config:
                raise ValueError(f"无效的产品类型: {product_type}")

            # 商户选择
            if product_type == "天还":
                placeholders["[出租商户]"] = config["fixed_merchant"]
            else:
                merchants = AppConfig.Merchants.get_options()
                placeholders["[出租商户]"] = merchants.get(merchant, "未知商户")

            # 月租
            monthly_payment = config['monthly_payment']
            device_count = int(placeholders.get("[设备数量]", "1"))
            placeholders["[月租]"] = f"{monthly_payment * device_count:.2f}"

            # 时间计算
            current_date = datetime.now()
            end_date = current_date + relativedelta(**config["duration"])

            date_fields = {
                "[时间]": current_date.strftime("%Y-%m-%d"),
                "[截至时间]": end_date.strftime("%Y-%m-%d"),
                "[年]": current_date.strftime("%Y"),
                "[月]": current_date.strftime("%m"),
                "[日]": current_date.strftime("%d"),
                "[下单时间]": current_date.strftime("%Y-%m-%d"),
            }
            placeholders.update(date_fields)

            # 加载模板
            template_path = os.path.join(self.template_dir, config["template"])
            if not os.path.exists(template_path):
                raise FileNotFoundError(f"模板文件不存在: {template_path}")

            doc = Document(template_path)
            doc = DocUtils.replace_placeholders(doc, placeholders, self.CENTER_VARS)

            # 保存路径
            output_dir = os.path.join(self.base_dir, "output")
            os.makedirs(output_dir, exist_ok=True)

            safe_name = re.sub(r'[\\/*?:"<>|]', "", placeholders["[姓名]"])
            timestamp = current_date.strftime("%Y%m%d_%H%M%S")
            filename = f"{safe_name}_{placeholders['[订单编号]']}_{timestamp}.docx"
            output_path = os.path.join(output_dir, filename)

            try:
                doc.save(output_path)
            except PermissionError as e:
                logging.error(f"文件保存失败：{str(e)}")
                raise RuntimeError("文件保存失败：请关闭正在使用的文档后重试") from e

            logging.info(f"成功生成文档: {filename}")
            return output_path

        except Exception as e:
            logging.error(f"生成过程中发生错误: {str(e)}", exc_info=True)
            raise


class ContractGenerator:
    """合同生成器"""
    
    def __init__(self):
        self.base_dir = AppConfig.BASE_DIR
    
    @staticmethod
    def add_order_details_table(doc, data, insert_index):
        """添加订单详情表格"""
        table = doc.add_table(rows=1, cols=5)
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = "订单ID"
        hdr_cells[1].text = "商品名称"
        hdr_cells[2].text = "商品规格颜色"
        hdr_cells[3].text = "设备数量"
        hdr_cells[4].text = "收件人信息"

        first_row = data.iloc[0]
        combined_info = (f"{first_row['收件人姓名']}\n{first_row['收件人手机号']}\n"
                         f"{first_row['收货地址']}")

        # 获取AD列设备数量
        ad_series = pd.to_numeric(data.iloc[:, 29], errors='coerce')

        for i, row in data.iterrows():
            row_cells = table.add_row().cells
            row_cells[0].text = str(row["订单ID"])
            row_cells[1].text = str(row["商品名称"])
            row_cells[2].text = str(row["商品规格颜色"])
            
            # 设置设备数量，空值默认为1
            quantity = ad_series.iloc[i] if pd.notna(ad_series.iloc[i]) else 1
            row_cells[3].text = str(int(quantity))
            
            row_cells[4].text = combined_info
            
            for cell in row_cells:
                DocUtils.set_table_cell_font(cell)

        DocUtils.add_border_to_table(table)
        DocUtils.insert_table_at_paragraph(doc, table, insert_index)

    @staticmethod
    def add_product_list_table(doc, data, insert_index, version="新"):
        """添加产品清单表格"""
        table = doc.add_table(rows=1, cols=5)
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = "订单ID"
        hdr_cells[1].text = "商品名称"
        hdr_cells[2].text = "商品规格颜色"
        hdr_cells[3].text = "设备数量"
        hdr_cells[4].text = "金额"

        config = AppConfig.Products.GROUP_A if version == "新" else AppConfig.Products.GROUP_B
        
        # 从Excel的AD列计算设备数量求和，空值单元格（非空行）默认计为1
        ad_series = pd.to_numeric(data.iloc[:, 29], errors='coerce')
        valid_rows = data.notna().any(axis=1)
        device_count = int(ad_series[valid_rows].fillna(1).sum())

        for i, row in data.iterrows():
            row_cells = table.add_row().cells
            row_cells[0].text = str(row["订单ID"])
            row_cells[1].text = str(row["商品名称"])
            row_cells[2].text = str(row["商品规格颜色"])
            
            # 设置设备数量，空值默认为1
            quantity = ad_series.iloc[i] if pd.notna(ad_series.iloc[i]) else 1
            row_cells[3].text = str(int(quantity))

            # 使用当前行的设备数量计算金额，而不是总设备数量
            amount = row["总租金"] + (config["DOWN_PAYMENT"] * int(quantity))
            row_cells[4].text = f"{amount:.2f}"

            for cell in row_cells:
                DocUtils.set_table_cell_font(cell)

        DocUtils.add_border_to_table(table)
        DocUtils.insert_table_at_paragraph(doc, table, insert_index)

    @staticmethod
    def add_tail_payment_table(doc, total_periods, device_count, start_date, 
                              insert_index, version="新"):
        """添加尾款支付表格"""
        table = doc.add_table(rows=1, cols=3)
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = "租期"
        hdr_cells[1].text = "金额"
        hdr_cells[2].text = "账单日"

        # 无论新旧版本，尾款计算都使用GROUP_A的DEVICE_PRICE
        device_price = AppConfig.Products.GROUP_A["DEVICE_PRICE"]

        for i in range(total_periods):
            row_cells = table.add_row().cells
            row_cells[0].text = f"第{i+1}期"
            amount = device_price * device_count
            row_cells[1].text = f"{amount:.2f}"
            payment_date = start_date + timedelta(days=10 * (i + 1))
            row_cells[2].text = payment_date.strftime("%Y-%m-%d")

            for cell in row_cells:
                DocUtils.set_table_cell_font(cell)

        DocUtils.add_border_to_table(table)
        DocUtils.insert_table_at_paragraph(doc, table, insert_index)

    @staticmethod
    def copy_document(src_path, dst_path):
        """复制文档文件"""
        if not os.path.exists(src_path):
            raise FileNotFoundError(f"Template file not found: {src_path}")
        shutil.copyfile(src_path, dst_path)

    def generate_attachments(self, data, output_path, placeholders):
        """生成附件文件
        
        Args:
            data (DataFrame): Excel数据
            output_path (str): 输出目录路径
            placeholders (dict): 占位符字典
            
        Returns:
            list: 生成的附件文件路径列表
        """
        attachment_files = []
        first_row = data.iloc[0]
        
        # 确保输出目录存在
        os.makedirs(output_path, exist_ok=True)
        
        # 附件模板
        attachment_templates = [
            {"name": "附件", "template": "fj.docx"},
            {"name": "征信授权", "template": "hcd.docx"}
        ]
        
        for attachment in attachment_templates:
            template_path = os.path.join(self.base_dir, "HZ", attachment["template"])
            
            if not os.path.exists(template_path):
                logging.warning(f"附件模板文件不存在: {template_path}")
                continue
                
            output_filename = f"{attachment['name']}_{first_row['收件人姓名']}_{datetime.now().strftime('%Y%m%d%H%M%S')}.{os.path.splitext(attachment['template'])[1]}"
            attachment_path = os.path.join(output_path, output_filename)
            
            try:
                # 复制文档
                self.copy_document(template_path, attachment_path)
                
                # 根据文件扩展名加载文档
                if attachment_path.lower().endswith('.doc'):
                    # 处理.doc文件 - 通过读取二进制内容并替换文本来处理
                    try:
                        # 特别处理几个关键占位符，确保它们被正确转换
                        # 创建针对.doc文件的特定占位符映射
                        doc_placeholders = {}
                        # 同时收集尝试的所有占位符，以便日志记录
                        tried_placeholders = []
                        
                        # 将原有占位符添加到尝试列表
                        for k, v in placeholders.items():
                            tried_placeholders.append((k, v))
                        
                        # 特别确保这三个关键占位符有值(尝试不同编码和格式)
                        name_value = ""
                        id_value = ""
                        date_value = ""
                        
                        # 确定姓名值
                        if "[乙方姓名]" in placeholders:
                            name_value = placeholders["[乙方姓名]"]
                        elif "收件人姓名" in first_row:
                            name_value = str(first_row["收件人姓名"])
                        
                        # 确定身份证号值
                        if "[乙方身份证号]" in placeholders:
                            id_value = placeholders["[乙方身份证号]"]
                        elif "身份证号" in first_row:
                            id_value = str(first_row["身份证号"])
                        
                        # 确定日期值
                        if "[今日日期]" in placeholders:
                            date_value = placeholders["[今日日期]"]
                        else:
                            date_value = datetime.now().strftime("%Y年%m月%d日")
                        
                        # 添加多种可能的格式到尝试列表(UTF-8, GBK编码格式，不同括号类型等)
                        for name_placeholder in ["[乙方姓名]", "【乙方姓名】", "{乙方姓名}"]:
                            tried_placeholders.append((name_placeholder, name_value))
                            doc_placeholders[name_placeholder] = name_value
                            
                        for id_placeholder in ["[乙方身份证号]", "【乙方身份证号】", "{乙方身份证号}"]:
                            tried_placeholders.append((id_placeholder, id_value))
                            doc_placeholders[id_placeholder] = id_value
                            
                        for date_placeholder in ["[今日日期]", "【今日日期】", "{今日日期}"]:
                            tried_placeholders.append((date_placeholder, date_value))
                            doc_placeholders[date_placeholder] = date_value
                            
                        # 读取文件内容
                        with open(attachment_path, 'rb') as file:
                            content = file.read()
                        
                        # 尝试不同的编码方式和替换策略
                        success = False
                        replace_logs = []
                        
                        # 记录原始文件的十六进制信息用于诊断
                        original_hex = content[:100].hex()
                        logging.info(f"原始文件前100字节十六进制: {original_hex}")
                        
                        for encoding in ['utf-8', 'gbk', 'utf-16', 'latin1']:
                            try:
                                # 尝试解码
                                text_content = content.decode(encoding)
                                original_text = text_content[:200]  # 保存替换前的前200个字符用于对比
                                
                                # 先尝试整体替换
                                for placeholder, value in doc_placeholders.items():
                                    if placeholder in text_content:
                                        text_content = text_content.replace(placeholder, value)
                                        replace_logs.append(f"成功用{encoding}编码替换 {placeholder} -> {value}")
                                
                                # 写回文件
                                with open(attachment_path, 'wb') as file:
                                    file.write(text_content.encode(encoding))
                                
                                logging.info(f"成功使用{encoding}编码处理.doc文件")
                                replaced_text = text_content[:200]  # 保存替换后的前200个字符用于对比
                                logging.info(f"原始文本: {original_text}")
                                logging.info(f"替换后文本: {replaced_text}")
                                
                                success = True
                                break
                            except Exception as e:
                                logging.warning(f"使用{encoding}编码处理.doc文件失败: {str(e)}")
                        
                        # 如果常规方法都失败，尝试直接替换二进制内容
                        if not success:
                            logging.warning("常规文本替换方法失败，尝试二进制替换...")
                            binary_content = content
                            binary_replaced = False
                            
                            for placeholder, value in doc_placeholders.items():
                                # 尝试不同编码的二进制替换
                                for encoding in ['utf-8', 'gbk', 'utf-16', 'latin1']:
                                    try:
                                        binary_placeholder = placeholder.encode(encoding)
                                        binary_value = value.encode(encoding)
                                        
                                        # 只有当占位符存在时才进行替换
                                        if binary_placeholder in binary_content:
                                            binary_content = binary_content.replace(binary_placeholder, binary_value)
                                            binary_replaced = True
                                            replace_logs.append(f"二进制替换成功({encoding}): {placeholder} -> {value}")
                                    except Exception as e:
                                        continue
                            
                            if binary_replaced:
                                with open(attachment_path, 'wb') as file:
                                    file.write(binary_content)
                                logging.info("使用二进制替换方法成功处理.doc文件")
                                success = True
                        
                        # 记录替换结果
                        if replace_logs:
                            logging.info("替换日志:\n" + "\n".join(replace_logs))
                        else:
                            logging.warning("没有成功进行任何替换")
                        
                        if not success:
                            logging.warning(f"无法处理.doc文件: {attachment_path}，使用原始文件")
                            # 在日志中记录尝试的所有占位符和值
                            logging.info("尝试的所有占位符和值:")
                            for placeholder, value in tried_placeholders:
                                logging.info(f"  {placeholder} -> {value}")
                    except Exception as e:
                        logging.error(f"处理.doc文件时出错: {str(e)}", exc_info=True)
                    
                    attachment_files.append(attachment_path)
                    continue
                    
                # 加载docx文档并替换占位符
                doc = Document(attachment_path)
                DocUtils.replace_placeholders(doc, placeholders)
                doc.save(attachment_path)
                
                attachment_files.append(attachment_path)
                logging.info(f"成功生成附件: {output_filename}")
                
            except Exception as e:
                logging.error(f"生成附件 {attachment['name']} 时出错: {str(e)}", exc_info=True)
        
        return attachment_files

    def fill_contract_template(self, data_path, output_path, contract_type, version, include_attachments=False):
        """填充合同模板"""
        file_extension = os.path.splitext(data_path)[1].lower()

        if file_extension not in [".xls", ".xlsx"]:
            raise ValueError("仅支持 .xls 或 .xlsx 格式的 Excel 文件。")

        # 读取Excel文件
        if file_extension == ".xls":
            data = pd.read_excel(data_path, engine="xlrd")
        else:
            data = pd.read_excel(data_path, engine="openpyxl")

        # 计算合同数据
        total_rent = data["总租金"].sum()
        # 从Excel的AD列计算设备数量求和，空值单元格（非空行）默认计为1
        ad_series = pd.to_numeric(data.iloc[:, 29], errors='coerce')
        valid_rows = data.notna().any(axis=1)
        device_count = int(ad_series[valid_rows].fillna(1).sum())
        
        config = AppConfig.Products.GROUP_A if version == "新" else AppConfig.Products.GROUP_B

        if version == "新":
            contract_price = total_rent + (device_count * 116)
            buyout_price = device_count * 116
        else:
            contract_price = total_rent + (device_count * config["DEVICE_PRICE"])
            buyout_price = device_count * config["DEVICE_PRICE"]
            
        down_payment = config["DOWN_PAYMENT"] * device_count

        current_date_str = datetime.now().strftime("%Y年%m月%d日")
        total_periods = int(data["总期数"].iloc[0])
        rent_per_period = total_rent / total_periods
        value_added_service_fee = data["增值服务费"].sum()
        extended_service_fee = value_added_service_fee

        start_date_str = data["起租日期"].iloc[0]
        start_date = pd.to_datetime(start_date_str)

        # 设置占位符映射
        column_mapping = {
            "[乙方姓名]": "收件人姓名",
            "[乙方身份证号]": "身份证号",
            "[乙方地址]": "收货地址",
            "[乙方电话]": "收件人手机号",
            "[设备数量]": str(device_count),
            "[总租金]": f"{total_rent:.2f}",
            "[签约总价]": f"{contract_price:.2f}",
            "[买断价]": f"{buyout_price:.2f}",
            "[今日日期]": current_date_str,
            "[总期数]": str(total_periods),
            "[延保服务]": f"{extended_service_fee:.2f}",
            "[首付款]": f"{down_payment:.2f}",
        }

        # 选择模板
        templates = []
        template_folder = "AHT" if version == "新" else "BHT"
        template_dir = os.path.join(self.base_dir, template_folder)
        
        # 确保模板目录存在
        os.makedirs(template_dir, exist_ok=True)
        
        if contract_type == "租赁":
            templates = [os.path.join(template_dir, "xx_ht.docx")]
        else:
            templates = [
                os.path.join(template_dir, "xx_t_sh.docx"),
                os.path.join(template_dir, "xx_t_mm.docx"),
            ]

        # 处理每个模板
        first_row = data.iloc[0]
        result_files = []
        
        # 确保输出目录存在
        os.makedirs(output_path, exist_ok=True)
        
        # 构建占位符字典用于文档替换
        placeholders = {}
        for placeholder, column in column_mapping.items():
            if column in first_row:
                placeholders[placeholder] = str(first_row[column])
            else:
                placeholders[placeholder] = column
                
        # 生成附件文件
        if include_attachments:
            attachment_files = self.generate_attachments(data, output_path, placeholders)
            result_files.extend(attachment_files)
        
        for template_file in templates:
            # 创建空白文档，如果模板不存在
            if not os.path.exists(template_file):
                logging.warning(f"模板文件不存在: {template_file}，将创建空白文档")
                doc = Document()
                doc.save(template_file)
                
            file_suffix = ("售后服务" if "xx_t_sh" in template_file else
                          ("买卖" if "xx_t_mm" in template_file else "租赁"))
            new_contract_path = os.path.join(
                output_path,
                f"{contract_type}_{file_suffix}_{first_row['收件人姓名']}_"
                f"{datetime.now().strftime('%Y%m%d%H%M%S')}.docx",
            )

            self.copy_document(template_file, new_contract_path)
            new_doc = Document(new_contract_path)

            # 替换占位符
            DocUtils.replace_placeholders(new_doc, placeholders)

            # 查找并处理表格插入点
            for i, paragraph in enumerate(new_doc.paragraphs):
                if "[订单详情]" in paragraph.text:
                    paragraph.text = paragraph.text.replace("[订单详情]", "")
                    self.add_order_details_table(new_doc, data, i)
                    
                elif "[租金支付表]" in paragraph.text:
                    paragraph.text = paragraph.text.replace("[租金支付表]", "")
                    DocUtils.add_rent_payment_table(
                        new_doc, total_periods, rent_per_period, start_date, i
                    )
                    
                elif "[产品清单]" in paragraph.text:
                    paragraph.text = paragraph.text.replace("[产品清单]", "")
                    self.add_product_list_table(new_doc, data, i, version)
                    
                elif "[尾款支付表]" in paragraph.text:
                    paragraph.text = paragraph.text.replace("[尾款支付表]", "")
                    self.add_tail_payment_table(
                        new_doc, total_periods, device_count, start_date, i, version
                    )

            new_doc.save(new_contract_path)
            result_files.append(new_contract_path)
            
        return result_files 