@echo off
REM ===================================================================
REM HDSC查询应用 - 优化版Docker镜像构建和导出脚本 (Windows版)
REM 版本: v2.0 (最新优化版)
REM 功能: 自动构建、优化、导出Docker镜像
REM 使用方法: build-and-export-optimized.bat [版本号]
REM ===================================================================

setlocal enabledelayedexpansion

REM 设置默认版本号
if "%1"=="" (
    set VERSION=latest
) else (
    set VERSION=%1
)

set IMAGE_NAME=hdsc-query-app
set FULL_IMAGE_NAME=%IMAGE_NAME%:%VERSION%
set TAR_FILE=%IMAGE_NAME%-optimized-%VERSION%.tar

echo ===================================================================
echo 🚀 HDSC查询应用 - 优化版Docker构建脚本 v2.0
echo ===================================================================
echo 镜像名称: %FULL_IMAGE_NAME%
echo 导出文件: %TAR_FILE%
echo 构建时间: %date% %time%
echo 使用Dockerfile: Dockerfile.optimized (多阶段构建)
echo ===================================================================

REM 检查Docker环境
echo [1/6] 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)

docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: Docker服务未运行，请启动Docker Desktop
    pause
    exit /b 1
)

echo ✅ Docker环境检查通过

REM 检查Dockerfile.optimized是否存在
echo [2/6] 检查构建文件...
if not exist "Dockerfile.optimized" (
    echo ❌ 错误: 未找到Dockerfile.optimized文件
    echo 请确保项目根目录包含优化版Dockerfile
    pause
    exit /b 1
)

if not exist "package.json" (
    echo ❌ 错误: 未找到package.json文件
    echo 多阶段构建需要前端依赖配置文件
    pause
    exit /b 1
)

echo ✅ 构建文件检查通过

REM 清理旧镜像（可选）
echo [3/6] 清理旧镜像...
docker images -q %IMAGE_NAME% >nul 2>&1
if not errorlevel 1 (
    set /p CLEAN_CHOICE="发现已存在的镜像，是否清理？(y/N): "
    if /i "!CLEAN_CHOICE!"=="y" (
        echo 正在清理旧镜像...
        docker rmi %IMAGE_NAME%:%VERSION% >nul 2>&1
        docker rmi %IMAGE_NAME%:latest >nul 2>&1
        echo ✅ 旧镜像清理完成
    )
)

REM 构建优化版Docker镜像
echo [4/6] 构建优化版Docker镜像...
echo 使用多阶段构建，这可能需要几分钟时间...
docker build -f Dockerfile.optimized -t %FULL_IMAGE_NAME% --no-cache .

if errorlevel 1 (
    echo ===================================================================
    echo ❌ Docker镜像构建失败！
    echo 
    echo 可能的解决方案:
    echo 1. 检查网络连接，确保可以访问Docker Hub
    echo 2. 尝试使用简化版构建: build-docker.bat %VERSION%
    echo 3. 检查Dockerfile.optimized语法
    echo ===================================================================
    pause
    exit /b 1
)

REM 添加latest标签
if not "%VERSION%"=="latest" (
    echo 添加latest标签...
    docker tag %FULL_IMAGE_NAME% %IMAGE_NAME%:latest
)

echo ✅ Docker镜像构建成功！

REM 显示镜像信息
echo [5/6] 镜像信息:
docker images %IMAGE_NAME%

REM 导出镜像
echo [6/6] 导出Docker镜像...
echo 正在导出到: %TAR_FILE%
docker save -o %TAR_FILE% %FULL_IMAGE_NAME%

if errorlevel 1 (
    echo ❌ 错误: Docker镜像导出失败！
    pause
    exit /b 1
)

REM 显示文件大小
echo ✅ 镜像导出成功！
for %%A in (%TAR_FILE%) do (
    set /a SIZE_MB=%%~zA/1024/1024
    echo 文件大小: !SIZE_MB! MB
)

echo ===================================================================
echo 🎉 构建和导出完成！
echo ===================================================================
echo 📦 导出文件: %TAR_FILE%
echo 🏷️  镜像标签: %FULL_IMAGE_NAME%
echo 
echo 📋 在目标服务器上使用以下命令:
echo    1. 加载镜像: docker load -i %TAR_FILE%
echo    2. 运行容器: docker run -d -p 5000:5000 --name hdsc-app %FULL_IMAGE_NAME%
echo    3. 查看状态: docker ps
echo    4. 查看日志: docker logs hdsc-app
echo 
echo 🔧 或使用Docker Compose:
echo    docker-compose up -d
echo ===================================================================

REM 询问是否运行测试
set /p TEST_CHOICE="是否启动测试容器验证镜像？(y/N): "
if /i "%TEST_CHOICE%"=="y" (
    echo 启动测试容器...
    docker run -d --name hdsc-test-optimized -p 5001:5000 %FULL_IMAGE_NAME%
    echo ✅ 测试容器已启动
    echo 🌐 访问地址: http://localhost:5001
    echo 🛑 停止命令: docker stop hdsc-test-optimized
    echo 🗑️  删除命令: docker rm hdsc-test-optimized
)

echo.
echo 感谢使用HDSC查询应用优化版构建脚本！
pause