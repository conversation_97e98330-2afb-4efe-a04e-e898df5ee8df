# Docker 部署指南

## 镜像信息
- **镜像名称**: hdsc-query-app:latest
- **镜像文件**: hdsc-query-app-latest.tar
- **镜像大小**: 约 556MB
- **导出时间**: 2025-07-31

## 部署步骤

### 1. 导入镜像
在目标服务器上导入镜像文件：
```bash
docker load -i hdsc-query-app-latest.tar
```

### 2. 验证镜像
确认镜像已成功导入：
```bash
docker images hdsc-query-app
```

### 3. 运行容器
#### 基本运行命令
```bash
docker run -d \
  --name hdsc-query-app \
  -p 5000:5000 \
  hdsc-query-app:latest
```

#### 生产环境推荐配置
```bash
docker run -d \
  --name hdsc-query-app \
  --restart unless-stopped \
  -p 5000:5000 \
  -v /path/to/logs:/app/logs \
  -v /path/to/cache:/app/cache \
  -e FLASK_ENV=production \
  hdsc-query-app:latest
```

### 4. 使用 Docker Compose（推荐）
创建 `docker-compose.yml` 文件：
```yaml
version: '3.8'

services:
  hdsc-query-app:
    image: hdsc-query-app:latest
    container_name: hdsc-query-app
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      - ./logs:/app/logs
      - ./cache:/app/cache
    environment:
      - FLASK_ENV=production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

启动服务：
```bash
docker-compose up -d
```

## 镜像特性

### 多阶段构建
- **Builder Stage**: 使用 Node.js 20 构建前端资源
- **Final Stage**: 使用 Python 3.11-slim 运行应用

### 包含组件
- Flask Web 应用
- Gunicorn WSGI 服务器
- 编译后的前端静态资源
- 所有 Python 依赖包
- 定时任务调度器

### 端口配置
- **应用端口**: 5000
- **健康检查**: HTTP GET /

### 环境变量
- `FLASK_ENV`: 运行环境（production/development）
- `PYTHONDONTWRITEBYTECODE`: 禁用 .pyc 文件生成
- `PYTHONUNBUFFERED`: 禁用输出缓冲

## 监控和维护

### 查看容器状态
```bash
docker ps
docker logs hdsc-query-app
```

### 进入容器调试
```bash
docker exec -it hdsc-query-app /bin/bash
```

### 更新应用
1. 停止当前容器：`docker stop hdsc-query-app`
2. 删除容器：`docker rm hdsc-query-app`
3. 导入新镜像
4. 重新运行容器

### 备份数据
```bash
# 备份日志
docker cp hdsc-query-app:/app/logs ./backup/logs

# 备份缓存
docker cp hdsc-query-app:/app/cache ./backup/cache
```

## 故障排除

### 常见问题
1. **端口冲突**: 确保 5000 端口未被占用
2. **权限问题**: 确保挂载目录有正确的读写权限
3. **内存不足**: 建议至少 1GB 可用内存

### 日志查看
```bash
# 查看应用日志
docker logs hdsc-query-app

# 查看实时日志
docker logs -f hdsc-query-app

# 查看 Gunicorn 日志
docker exec hdsc-query-app cat /app/access.log
docker exec hdsc-query-app cat /app/error.log
```

## 性能优化

### 资源限制
```bash
docker run -d \
  --name hdsc-query-app \
  --memory=1g \
  --cpus=1.0 \
  -p 5000:5000 \
  hdsc-query-app:latest
```

### 网络配置
如需自定义网络，可创建 Docker 网络：
```bash
docker network create hdsc-network
docker run -d \
  --name hdsc-query-app \
  --network hdsc-network \
  -p 5000:5000 \
  hdsc-query-app:latest
```

## 安全建议

1. **不要以 root 用户运行容器**
2. **定期更新基础镜像**
3. **使用 HTTPS 反向代理**
4. **限制容器权限**
5. **定期备份重要数据**

## 联系信息
如有问题，请联系开发团队或查看项目文档。