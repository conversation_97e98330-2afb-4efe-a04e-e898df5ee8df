<!-- 性能优化后的资源加载模板 -->

<!-- 预加载关键资源 -->
<link rel="preload" href="{{ url_for('static', filename='dist/js/app.min.js') }}" as="script">
<link rel="preload" href="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}" as="script">
<link rel="preload" href="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}" as="script">

<!-- 条件加载大型库 -->
{% if page_needs_excel %}
<link rel="preload" href="{{ url_for('static', filename='dist/js/excel.min.js') }}" as="script">
<script>
    // 页面加载完成后加载Excel支持
    document.addEventListener('DOMContentLoaded', function() {
        loadExcelSupport();
    });
</script>
{% endif %}

{% if page_needs_charts %}
<link rel="preload" href="{{ url_for('static', filename='dist/js/charts.min.js') }}" as="script">
<script>
    // 页面加载完成后加载图表支持
    document.addEventListener('DOMContentLoaded', function() {
        loadChartSupport();
    });
</script>
{% endif %}

{% if page_needs_enterprise %}
<link rel="preload" href="{{ url_for('static', filename='dist/js/enterprise.min.js') }}" as="script">
<script>
    // 页面加载完成后加载企业级功能
    document.addEventListener('DOMContentLoaded', function() {
        loadEnterpriseSupport();
    });
</script>
{% endif %}

<!-- 页面特定组件 -->
{% if page_type == 'home' %}
<script src="{{ url_for('static', filename='dist/js/home.min.js') }}" defer></script>
{% endif %}

<!-- 通用组件 -->
<script src="{{ url_for('static', filename='dist/js/components.min.js') }}" defer></script>

<!-- 性能监控 -->
<script>
    // 性能监控代码
    window.addEventListener('load', function() {
        if (window.performance && window.performance.timing) {
            const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
            console.log('页面加载时间:', loadTime + 'ms');
        }
    });
</script>