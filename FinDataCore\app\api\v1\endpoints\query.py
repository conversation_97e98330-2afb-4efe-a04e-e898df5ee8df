"""
查询相关API端点
"""
from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional
import logging

from app.core.dependencies import get_current_active_user, require_read_permission
from app.core.security import User
from app.models.request import (
    FilterDataRequest, 
    CustomerOrdersRequest, 
    OverdueOrdersRequest
)
from app.models.response import (
    FilterDataResponse, 
    CustomerOrdersResponse, 
    OverdueOrdersResponse,
    ErrorResponse
)
from app.services.data_service import data_service

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/filter_data", response_model=FilterDataResponse)
async def filter_data_api(
    date: str = Query(..., description="筛选日期，格式：YYYY-MM-DD"),
    current_user: User = Depends(require_read_permission)
):
    """
    根据日期筛选数据的API
    
    Args:
        date: 筛选日期
        current_user: 当前用户
        
    Returns:
        筛选结果
    """
    try:
        logger.info(f"用户 {current_user.username} 请求筛选数据，日期: {date}")
        
        # 调用数据服务
        results = await data_service.get_filtered_data(date)
        
        # 检查错误
        if "error" in results:
            raise HTTPException(status_code=400, detail=results["error"])
        
        return FilterDataResponse(
            results=results.get("results", []),
            columns=results.get("columns", []),
            total=results.get("total", 0)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"筛选数据时发生异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get("/filter_orders_by_customer_name", response_model=CustomerOrdersResponse)
async def customer_orders_api(
    customer_name: str = Query(..., description="客户姓名"),
    current_user: User = Depends(require_read_permission)
):
    """
    获取客户订单数据的API
    
    Args:
        customer_name: 客户姓名
        current_user: 当前用户
        
    Returns:
        客户订单结果
    """
    try:
        logger.info(f"用户 {current_user.username} 请求客户订单数据，客户: {customer_name}")
        
        # 调用数据服务
        results = await data_service.get_orders_by_customer(customer_name)
        
        # 检查错误
        if "error" in results:
            raise HTTPException(status_code=400, detail=results["error"])
        
        return CustomerOrdersResponse(
            results=results.get("results", []),
            columns=results.get("columns", []),
            total=results.get("total", 0)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取客户订单数据时发生异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get("/overdue", response_model=OverdueOrdersResponse)
async def overdue_orders_api(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    limit: int = Query(10, ge=1, le=100, description="每页数据量，最大100"),
    force_refresh: bool = Query(False, description="是否强制刷新缓存"),
    search_query: str = Query("", description="搜索关键词"),
    current_user: User = Depends(require_read_permission)
):
    """
    获取逾期订单数据的API - 支持分页和搜索
    
    Args:
        page: 页码
        limit: 每页数据量
        force_refresh: 是否强制刷新缓存
        search_query: 搜索关键词
        current_user: 当前用户
        
    Returns:
        逾期订单结果
    """
    try:
        logger.info(f"用户 {current_user.username} 请求逾期订单数据，页码: {page}, 每页: {limit}")
        
        # 调用数据服务
        results = await data_service.get_overdue_orders(
            page=page,
            limit=limit,
            force_refresh=force_refresh,
            search_query=search_query
        )
        
        # 检查错误
        if "error" in results:
            raise HTTPException(status_code=400, detail=results["error"])
        
        return OverdueOrdersResponse(
            results=results.get("results", []),
            columns=results.get("columns", []),
            pagination=results.get("pagination", {}),
            cache_info=results.get("cache_info")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取逾期订单数据时发生异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

# 兼容性路由 - 保持与原Flask API相同的路径
@router.get("/filter", response_model=FilterDataResponse)
async def filter_data_compat(
    date: str = Query(..., description="筛选日期，格式：YYYY-MM-DD"),
    current_user: User = Depends(require_read_permission)
):
    """兼容性路由 - /api/filter"""
    return await filter_data_api(date, current_user)

@router.get("/customer", response_model=CustomerOrdersResponse)
async def customer_orders_compat(
    name: str = Query(..., description="客户姓名", alias="customer_name"),
    current_user: User = Depends(require_read_permission)
):
    """兼容性路由 - /api/customer"""
    return await customer_orders_api(name, current_user)
