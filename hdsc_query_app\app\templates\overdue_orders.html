{% extends "base.html" %}

{% block title %}逾期订单查询_{{ version }}{% endblock %}

{% block styles %}
{{ super() }}
<!-- 现代化逾期订单页面样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/modern-overdue-orders.css') }}">
<!-- 响应式数据表格样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/responsive-data-table.css') }}">
<!-- 移动端优化样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-optimized.css') }}">
{% endblock %}


{% block content %}
<div class="modern-overdue-container">
    <!-- 引入侧边栏模板 -->
    {% include 'sidebar.html' %}

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="page-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        逾期订单查询
                    </h1>
                    <p class="page-subtitle">实时监控和管理逾期订单数据</p>
                </div>
                <div class="header-actions">
                    <button class="action-btn refresh-btn" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        <span>刷新数据</span>
                    </button>
                    <div class="export-dropdown">
                        <button class="action-btn export-btn" onclick="toggleExportMenu()">
                            <i class="fas fa-download"></i>
                            <span>导出数据</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="export-menu" id="exportMenu">
                            <button onclick="exportData('excel')" class="export-option">
                                <i class="fas fa-file-excel"></i>
                                Excel格式
                            </button>
                            <button onclick="exportData('csv')" class="export-option">
                                <i class="fas fa-file-csv"></i>
                                CSV格式
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据状态卡片 -->
        <div class="status-cards">
            <div class="status-card info-card">
                <div class="card-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="card-content">
                    <div class="card-label">最后更新</div>
                    <div class="card-value">{{ last_update or '暂无数据' }}</div>
                </div>
            </div>
            <div class="status-card count-card">
                <div class="card-icon">
                    <i class="fas fa-list"></i>
                </div>
                <div class="card-content">
                    <div class="card-label">总记录数</div>
                    <div class="card-value" id="totalRecords">{{ total_records or 0 }}</div>
                </div>
            </div>
            <div class="status-card filter-card">
                <div class="card-icon">
                    <i class="fas fa-filter"></i>
                </div>
                <div class="card-content">
                    <div class="card-label">当前显示</div>
                    <div class="card-value" id="currentDisplay">
                        {% if search_query %}筛选结果{% else %}全部数据{% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载状态指示器 -->
        <div id="loadingIndicator" class="loading-indicator">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在加载数据...</div>
        </div>

        <!-- 搜索和筛选控制区 -->
        <div class="search-controls">
            <div class="search-section">
                <div class="search-box">
                    <form id="searchForm" action="{{ url_for('main.query.overdue_orders') }}" method="GET">
                        <input type="hidden" name="limit" value="{{ page_limit }}">
                        <div class="search-input-group">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text"
                                   id="tableSearch"
                                   name="search"
                                   class="search-input"
                                   placeholder="搜索订单编号、客户姓名、手机号..."
                                   value="{{ search_query }}"
                                   autocomplete="off">
                            <button type="submit" class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                            {% if search_query %}
                            <button type="button" class="clear-search-btn" onclick="clearSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                            {% endif %}
                        </div>
                    </form>
                </div>
                <div class="filter-controls">
                    <div class="page-size-selector">
                        <label for="pageSizeSelect">每页显示</label>
                        <select id="pageSizeSelect" class="page-size-select">
                            <option value="10" {% if page_limit == 10 %}selected{% endif %}>10 条</option>
                            <option value="25" {% if page_limit == 25 %}selected{% endif %}>25 条</option>
                            <option value="50" {% if page_limit == 50 %}selected{% endif %}>50 条</option>
                            <option value="100" {% if page_limit == 100 %}selected{% endif %}>100 条</option>
                            <option value="200" {% if page_limit == 200 %}selected{% endif %}>200 条</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据展示区域 -->
        <div class="data-container">
            {% if overdue_results and overdue_results.results and overdue_results.results|length > 0 %}
                <!-- 结果统计 -->
                <div class="result-summary">
                    <div class="summary-info">
                        {% if search_query %}
                            <i class="fas fa-search"></i>
                            搜索"<strong>{{ search_query }}</strong>"：找到 <strong>{{ total_records }}</strong> 条匹配记录
                        {% else %}
                            <i class="fas fa-list"></i>
                            共找到 <strong>{{ overdue_results.results|length }}</strong> 条逾期记录
                        {% endif %}
                    </div>
                </div>

                <!-- 桌面端表格视图 -->
                <div class="desktop-table-view">
                    <div class="table-container">
                        <div class="table-wrapper">
                            <table class="modern-data-table data-table" id="overdueTable">
                            <thead>
                                <tr>
                                    {% for column in overdue_results.columns %}
                                    <th class="table-header" data-column="{{ column }}">
                                        <span class="header-text">{{ column }}</span>
                                        <i class="fas fa-sort sort-icon"></i>
                                    </th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody id="tableBody">
                                {%- for row in overdue_results.results -%}
                                <tr class="table-row" data-row-index="{{ loop.index0 }}">
                                    {% for column in overdue_results.columns %}
                                    <td class="table-cell {{ column|replace(' ', '-')|lower }}-cell"
                                        data-label="{{ column }}">
                                        {% if column in row %}
                                            {% if column in ['当前待收', '总待收', '成本'] and row[column]|string|float(0) > 0 %}
                                                <span class="amount-value">{{ "%.2f"|format(row[column]|float) }}</span>
                                            {% elif column in ['逾期天数', '逾期期数'] and row[column]|string|int(0) > 0 %}
                                                <span class="status-badge danger">{{ row[column] }}</span>
                                            {% elif column in ['业务', '客服'] %}
                                                <span class="status-badge primary">{{ row[column] }}</span>
                                            {% elif column in ['产品', '产品类型'] %}
                                                {% if '电商' in row[column] %}
                                                    <span class="status-badge info">{{ row[column] }}</span>
                                                {% elif '租赁' in row[column] %}
                                                    <span class="status-badge info">{{ row[column] }}</span>
                                                {% else %}
                                                    <span class="status-badge info">{{ row[column] }}</span>
                                                {% endif %}
                                            {% elif column in ['订单日期', '账单日期', '首次逾期日期'] %}
                                                <span class="date-value">{{ row[column] }}</span>
                                            {% else %}
                                                <span class="text-value">{{ row[column] }}</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="empty-value">-</span>
                                        {% endif %}
                                    </td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 移动端卡片视图 -->
                <div class="mobile-card-view">
                    <div class="cards-container" id="cardsContainer">
                        {%- for row in overdue_results.results -%}
                        <div class="data-card" data-row-index="{{ loop.index0 }}">
                            <div class="card-header">
                                <div class="card-title">
                                    {% if '订单编号' in row %}
                                        <span class="order-number">{{ row['订单编号'] }}</span>
                                    {% endif %}
                                    {% if '逾期天数' in row and row['逾期天数']|string|int(0) > 0 %}
                                        <span class="overdue-badge">逾期{{ row['逾期天数'] }}天</span>
                                    {% endif %}
                                </div>
                                <div class="card-actions">
                                    <button class="card-expand-btn" onclick="toggleCardDetails({{ loop.index0 }})">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-summary">
                                {% if '客户姓名' in row %}
                                <div class="summary-item">
                                    <span class="item-label">客户</span>
                                    <span class="item-value">{{ row['客户姓名'] }}</span>
                                </div>
                                {% endif %}
                                {% if '当前待收' in row and row['当前待收']|string|float(0) > 0 %}
                                <div class="summary-item">
                                    <span class="item-label">待收金额</span>
                                    <span class="item-value amount">{{ "%.2f"|format(row['当前待收']|float) }}</span>
                                </div>
                                {% endif %}
                            </div>
                            <div class="card-details" id="cardDetails{{ loop.index0 }}">
                                {% for column in overdue_results.columns %}
                                    {% if column not in ['订单编号', '客户姓名', '当前待收', '逾期天数'] and column in row %}
                                    <div class="detail-item">
                                        <span class="detail-label">{{ column }}</span>
                                        <span class="detail-value">
                                            {% if column in ['总待收', '成本'] and row[column]|string|float(0) > 0 %}
                                                {{ "%.2f"|format(row[column]|float) }}
                                            {% else %}
                                                {{ row[column] }}
                                            {% endif %}
                                        </span>
                                    </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- 现代化分页控件 -->
                <div class="pagination-wrapper">
                    <div class="pagination-info">
                        {% if total_records > 0 %}
                            显示第 <strong>{{ (current_page-1) * page_limit + 1 }}</strong> -
                            <strong>{{ (current_page-1) * page_limit + overdue_results.results|length }}</strong> 条，
                            共 <strong>{{ total_records }}</strong> 条记录
                        {% else %}
                            没有记录
                        {% endif %}
                    </div>

                    {% if total_pages > 1 %}
                    <nav class="pagination-nav" aria-label="逾期订单分页">
                        <div class="pagination-controls">
                            <!-- 上一页按钮 -->
                            <a class="pagination-btn prev-btn {% if current_page == 1 %}disabled{% endif %}"
                               href="{{ url_for('main.query.overdue_orders', page=current_page-1, limit=page_limit, search=search_query) if current_page > 1 else '#' }}">
                                <i class="fas fa-chevron-left"></i>
                                <span class="btn-text">上一页</span>
                            </a>

                            <!-- 页码按钮 -->
                            <div class="page-numbers">
                                {% set start_page = [1, current_page - 2]|max %}
                                {% set end_page = [total_pages, current_page + 2]|min %}

                                {% if start_page > 1 %}
                                    <a class="page-btn" href="{{ url_for('main.query.overdue_orders', page=1, limit=page_limit, search=search_query) }}">1</a>
                                    {% if start_page > 2 %}
                                        <span class="page-ellipsis">...</span>
                                    {% endif %}
                                {% endif %}

                                {% for p in range(start_page, end_page + 1) %}
                                    <a class="page-btn {% if p == current_page %}active{% endif %}"
                                       href="{{ url_for('main.query.overdue_orders', page=p, limit=page_limit, search=search_query) }}">{{ p }}</a>
                                {% endfor %}

                                {% if end_page < total_pages %}
                                    {% if end_page < total_pages - 1 %}
                                        <span class="page-ellipsis">...</span>
                                    {% endif %}
                                    <a class="page-btn" href="{{ url_for('main.query.overdue_orders', page=total_pages, limit=page_limit, search=search_query) }}">{{ total_pages }}</a>
                                {% endif %}
                            </div>

                            <!-- 下一页按钮 -->
                            <a class="pagination-btn next-btn {% if current_page >= total_pages %}disabled{% endif %}"
                               href="{{ url_for('main.query.overdue_orders', page=current_page+1, limit=page_limit, search=search_query) if current_page < total_pages else '#' }}">
                                <span class="btn-text">下一页</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </div>
                    </nav>
                    {% endif %}
                </div>

            {% elif overdue_results and 'error' in overdue_results %}
                <!-- 错误状态 -->
                <div class="error-state">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="error-content">
                        <h3 class="error-title">数据加载失败</h3>
                        <p class="error-message">{{ overdue_results.error }}</p>
                        <button class="retry-btn" onclick="retryLoadData()">
                            <i class="fas fa-redo"></i>
                            重试加载
                        </button>
                    </div>
                </div>
            {% else %}
                <!-- 空状态 -->
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-inbox"></i>
                    </div>
                    <div class="empty-content">
                        <h3 class="empty-title">暂无数据</h3>
                        <p class="empty-message">
                            {% if search_query %}
                                没有找到匹配"{{ search_query }}"的逾期订单
                            {% else %}
                                当前没有逾期订单数据
                            {% endif %}
                        </p>
                        {% if search_query %}
                        <button class="clear-search-btn" onclick="clearSearch()">
                            <i class="fas fa-times"></i>
                            清除搜索
                        </button>
                        {% else %}
                        <button class="load-data-btn" onclick="loadInitialData()">
                            <i class="fas fa-download"></i>
                            加载数据
                        </button>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 现代化逾期订单页面脚本 -->
<script type="text/javascript">
// 全局配置和数据
const OverdueOrdersApp = {
    // 应用配置
    config: {
        pageSize: {{ page_limit or 10 }},
        currentPage: {{ current_page or 1 }},
        totalPages: {{ total_pages or 1 }},
        totalRecords: {{ total_records or 0 }},
        searchQuery: '{{ search_query or "" }}',
        apiEndpoints: {
            refresh: '{{ url_for("main.query.overdue_orders", refresh=1) }}',
            export: '/api/export_all_overdue'
        }
    },

    // 数据存储
    data: {
        results: {{ overdue_results.results|tojson|safe if overdue_results and overdue_results.results else '[]' }},
        columns: {{ overdue_results.columns|tojson|safe if overdue_results and overdue_results.columns else '[]' }},
        lastUpdate: '{{ last_update or "" }}'
    },

    // 应用状态
    state: {
        isLoading: false,
        isMobile: window.innerWidth <= 768,
        currentView: 'table', // 'table' or 'cards'
        sortColumn: null,
        sortDirection: 'asc'
    }
};

// 核心功能函数
const AppFunctions = {
    // 初始化应用
    init() {
        console.log('🚀 初始化现代化逾期订单应用');
        this.setupEventListeners();
        this.detectViewMode();
        this.initializeComponents();
    },

    // 设置事件监听器
    setupEventListeners() {
        // 窗口大小变化
        window.addEventListener('resize', this.handleResize.bind(this));

        // 页面大小选择器
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', this.handlePageSizeChange.bind(this));
        }

        // 搜索表单
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', this.handleSearch.bind(this));
        }

        // 表格排序
        document.querySelectorAll('.table-header').forEach(header => {
            header.addEventListener('click', this.handleSort.bind(this));
        });
    },

    // 检测视图模式
    detectViewMode() {
        const isMobile = window.innerWidth <= 768;
        OverdueOrdersApp.state.isMobile = isMobile;
        OverdueOrdersApp.state.currentView = isMobile ? 'cards' : 'table';

        // 切换视图显示
        const desktopView = document.querySelector('.desktop-table-view');
        const mobileView = document.querySelector('.mobile-card-view');

        if (desktopView && mobileView) {
            desktopView.style.display = isMobile ? 'none' : 'block';
            mobileView.style.display = isMobile ? 'block' : 'none';
        }
    },

    // 初始化组件
    initializeComponents() {
        // 初始化响应式列管理
        setTimeout(() => {
            console.log('开始初始化列管理器');
            try {
                ColumnManager.init();
                console.log('列管理器初始化完成');
            } catch (error) {
                console.error('列管理器初始化失败:', error);
            }
        }, 500); // 增加延迟确保DOM完全加载
    },

    // 处理窗口大小变化
    handleResize() {
        clearTimeout(this.resizeTimer);
        this.resizeTimer = setTimeout(() => {
            this.detectViewMode();
        }, 250);
    },

    // 处理页面大小变化
    handlePageSizeChange(event) {
        const newSize = event.target.value;
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set('page', '1');
        currentUrl.searchParams.set('limit', newSize);
        window.location.href = currentUrl.toString();
    },

    // 处理搜索
    handleSearch(event) {
        this.showLoading('正在搜索...');
    },

    // 处理排序
    handleSort(event) {
        const column = event.currentTarget.dataset.column;
        if (!column) return;

        // 更新排序状态
        if (OverdueOrdersApp.state.sortColumn === column) {
            OverdueOrdersApp.state.sortDirection =
                OverdueOrdersApp.state.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            OverdueOrdersApp.state.sortColumn = column;
            OverdueOrdersApp.state.sortDirection = 'asc';
        }

        // 更新UI
        this.updateSortIcons();
        // 这里可以添加客户端排序逻辑或发送请求到服务器
    }
};

// UI更新和交互函数
const UIFunctions = {
    // 显示加载状态
    showLoading(message = '正在加载...') {
        OverdueOrdersApp.state.isLoading = true;
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.style.display = 'flex';
            const text = indicator.querySelector('.loading-text');
            if (text) text.textContent = message;
        }
    },

    // 隐藏加载状态
    hideLoading() {
        OverdueOrdersApp.state.isLoading = false;
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    },

    // 更新排序图标
    updateSortIcons() {
        document.querySelectorAll('.sort-icon').forEach(icon => {
            icon.className = 'fas fa-sort sort-icon';
        });

        const currentHeader = document.querySelector(`[data-column="${OverdueOrdersApp.state.sortColumn}"] .sort-icon`);
        if (currentHeader) {
            const direction = OverdueOrdersApp.state.sortDirection;
            currentHeader.className = `fas fa-sort-${direction === 'asc' ? 'up' : 'down'} sort-icon active`;
        }
    },

    // 更新状态卡片
    updateStatusCards() {
        const totalRecordsEl = document.getElementById('totalRecords');
        if (totalRecordsEl) {
            totalRecordsEl.textContent = OverdueOrdersApp.config.totalRecords;
        }

        const currentDisplayEl = document.getElementById('currentDisplay');
        if (currentDisplayEl) {
            const searchQuery = OverdueOrdersApp.config.searchQuery;
            currentDisplayEl.textContent = searchQuery ? '筛选结果' : '全部数据';
        }
    }
};

// 全局功能函数
function refreshData() {
    UIFunctions.showLoading('正在刷新数据...');
    window.location.href = OverdueOrdersApp.config.apiEndpoints.refresh;
}

function toggleExportMenu() {
    const menu = document.getElementById('exportMenu');
    if (menu) {
        menu.classList.toggle('show');
    }
}

function exportData(format) {
    const menu = document.getElementById('exportMenu');
    if (menu) menu.classList.remove('show');

    UIFunctions.showLoading(`正在导出${format.toUpperCase()}文件...`);

    const searchQuery = OverdueOrdersApp.config.searchQuery;
    const url = `${OverdueOrdersApp.config.apiEndpoints.export}?format=${format}&search=${encodeURIComponent(searchQuery)}`;

    fetch(url)
        .then(response => {
            if (!response.ok) throw new Error('导出失败');
            return response.blob();
        })
        .then(blob => {
            // 修正文件扩展名
            let fileExtension = format;
            if (format === 'excel') {
                fileExtension = 'xlsx';
            }

            const fileName = `逾期订单_${searchQuery ? '筛选数据_' : '全部数据_'}${new Date().toISOString().slice(0, 10)}.${fileExtension}`;
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        })
        .catch(error => {
            console.error('导出失败:', error);
            alert('导出失败，请重试');
        })
        .finally(() => {
            UIFunctions.hideLoading();
        });
}

function clearSearch() {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.delete('search');
    currentUrl.searchParams.set('page', '1');
    window.location.href = currentUrl.toString();
}

function retryLoadData() {
    refreshData();
}

function loadInitialData() {
    refreshData();
}

function toggleCardDetails(index) {
    const details = document.getElementById(`cardDetails${index}`);
    const button = details.parentElement.querySelector('.card-expand-btn i');

    if (details.style.display === 'none' || !details.style.display) {
        details.style.display = 'block';
        button.style.transform = 'rotate(180deg)';
    } else {
        details.style.display = 'none';
        button.style.transform = 'rotate(0deg)';
    }
}

// 增强型响应式列管理功能
const ColumnManager = {
    // 配置
    config: {
        minColumnWidth: 120,
        maxVisibleColumns: 6, // 默认最多显示6列
        breakpoints: {
            mobile: 768,
            tablet: 1024,
            desktop: 1200
        },
        // 列优先级配置（数字越小优先级越高）
        columnPriorities: {
            '订单编号': 1,
            '客户姓名': 2,
            '客户名称': 2,
            '当前待收': 3,
            '逾期天数': 4,
            '逾期期数': 5,
            '总待收': 6,
            '手机号': 7,
            '业务': 8,
            '客服': 9,
            '产品': 10,
            '创建时间': 11,
            '订单日期': 11,
            '成本': 12,
            '操作': 99
        },
        // 边缘吸附配置
        snapToEdge: {
            enabled: true, // 是否启用边缘吸附
            margin: 20, // 吸附边距（像素）
            animationDuration: 300, // 吸附动画持续时间（毫秒）
            threshold: 100 // 吸附触发阈值（距离边缘多少像素内触发吸附）
        }
    },

    // 状态
    state: {
        allColumns: [],
        visibleColumns: [],
        hiddenColumns: [],
        isExpanded: false,
        tableElement: null,
        // 拖拽相关状态
        isDragging: false,
        dragStartX: 0,
        dragStartY: 0,
        buttonStartX: 0,
        buttonStartY: 0
    },

    // 初始化列管理
    init() {
        console.log('🔧 开始初始化增强型列管理器');

        // 检查是否为移动端，移动端不启用列管理功能
        if (this.isMobileDevice()) {
            console.log('📱 检测到移动端设备，跳过列管理功能');
            return;
        }

        // 加载保存的吸附设置
        this.loadSnapSettings();

        this.detectColumns();
        if (this.state.allColumns.length === 0) {
            console.warn('未检测到任何列，跳过列管理初始化');
            return;
        }

        this.calculateVisibleColumns();
        this.createColumnControls();
        this.applyColumnVisibility();
        this.setupScrollIndicator();
        this.addTableClasses();

        // 监听窗口大小变化
        window.addEventListener('resize', this.handleResize.bind(this));

        console.log('✅ 增强型列管理器初始化完成');
        console.log(`📊 共${this.state.allColumns.length}列，显示${this.state.visibleColumns.length}列，隐藏${this.state.hiddenColumns.length}列`);
    },

    // 加载保存的吸附设置
    loadSnapSettings() {
        try {
            // 加载吸附开关状态
            const snapEnabled = localStorage.getItem('columnControlsSnapEnabled');
            if (snapEnabled !== null) {
                this.config.snapToEdge.enabled = snapEnabled === 'true';
                console.log(`📋 已加载吸附开关状态: ${this.config.snapToEdge.enabled ? '启用' : '禁用'}`);
            }

            // 加载吸附阈值
            const snapThreshold = localStorage.getItem('columnControlsSnapThreshold');
            if (snapThreshold !== null) {
                const threshold = parseInt(snapThreshold);
                if (threshold >= 50 && threshold <= 200) {
                    this.config.snapToEdge.threshold = threshold;
                    console.log(`📋 已加载吸附阈值: ${threshold}px`);
                }
            }
        } catch (error) {
            console.warn('加载吸附设置失败:', error);
        }
    },

    // 检测是否为移动端设备
    isMobileDevice() {
        return window.innerWidth <= this.config.breakpoints.mobile;
    },

    // 检测所有列
    detectColumns() {
        // 更精确的表格查找
        const tableSelectors = ['.modern-data-table', '#overdueTable', '.data-table'];
        let table = null;
        
        for (const selector of tableSelectors) {
            table = document.querySelector(selector);
            if (table) {
                console.log(`找到表格: ${selector}`);
                break;
            }
        }

        if (!table) {
            console.error('无法找到数据表格');
            return;
        }

        this.state.tableElement = table;
        const headers = table.querySelectorAll('thead th');

        this.state.allColumns = Array.from(headers).map((th, index) => {
            const text = th.textContent.trim();
            return {
                index,
                element: th,
                text,
                width: th.offsetWidth || 120,
                priority: this.getColumnPriority(text),
                isImportant: this.isImportantColumn(text)
            };
        });

        console.log('检测到列:', this.state.allColumns.map(col => 
            `${col.text}(优先级:${col.priority})`
        ));
    },

    // 获取列优先级
    getColumnPriority(columnText) {
        // 检查完全匹配
        if (this.config.columnPriorities[columnText]) {
            return this.config.columnPriorities[columnText];
        }

        // 检查部分匹配
        for (const [key, priority] of Object.entries(this.config.columnPriorities)) {
            if (columnText.includes(key) || key.includes(columnText)) {
                return priority;
            }
        }

        return 50; // 默认优先级
    },

    // 判断是否为重要列（始终显示）
    isImportantColumn(columnText) {
        const importantKeywords = ['订单编号', '客户姓名', '客户名称', '当前待收', '逾期天数'];
        return importantKeywords.some(keyword => 
            columnText.includes(keyword) || keyword.includes(columnText)
        );
    },

    // 计算可见列
    calculateVisibleColumns() {
        const containerWidth = document.querySelector('.table-container')?.offsetWidth || window.innerWidth;
        
        // 智能计算最大可见列数
        let maxColumns = this.calculateOptimalColumns(containerWidth);
        
        // 按优先级排序
        const sortedColumns = [...this.state.allColumns].sort((a, b) => a.priority - b.priority);

        // 确保重要列始终可见
        const importantColumns = sortedColumns.filter(col => col.isImportant);
        const otherColumns = sortedColumns.filter(col => !col.isImportant);

        // 选择可见列
        this.state.visibleColumns = [
            ...importantColumns,
            ...otherColumns.slice(0, Math.max(0, maxColumns - importantColumns.length))
        ].slice(0, maxColumns);

        this.state.hiddenColumns = sortedColumns.filter(col => 
            !this.state.visibleColumns.includes(col)
        );

        console.log(`📐 屏幕宽度: ${window.innerWidth}px, 容器宽度: ${containerWidth}px, 最优列数: ${maxColumns}`);
        console.log(`👁️ 可见列: ${this.state.visibleColumns.map(col => col.text).join(', ')}`);
        console.log(`🔒 隐藏列: ${this.state.hiddenColumns.map(col => col.text).join(', ')}`);
    },

    // 智能计算最优列数（基于容器宽度和列的最小宽度）
    calculateOptimalColumns(containerWidth) {
        const width = window.innerWidth;
        
        // 移动端直接返回（虽然不会执行到这里）
        if (width <= this.config.breakpoints.mobile) {
            return 3;
        }
        
        // 计算可以容纳的最大列数
        const availableWidth = containerWidth - 60; // 预留滚动条和边距空间
        const theoreticalMaxColumns = Math.floor(availableWidth / this.config.minColumnWidth);
        
        // 根据屏幕大小设置基准和上限
        let baseColumns, maxLimit;
        
        if (width <= this.config.breakpoints.tablet) {
            // 平板端: 基准4列，最多6列
            baseColumns = 4;
            maxLimit = 6;
        } else if (width <= this.config.breakpoints.desktop) {
            // 小桌面端: 基准5列，最多8列
            baseColumns = 5;
            maxLimit = 8;
        } else {
            // 大桌面端: 基准6列，最多10列
            baseColumns = 6;
            maxLimit = 10;
        }
        
        // 取理论最大值、基准值和上限的中间值
        const optimalColumns = Math.min(
            Math.max(baseColumns, theoreticalMaxColumns),
            maxLimit,
            this.state.allColumns.length // 不能超过总列数
        );
        
        console.log(`🔢 列数计算: 理论最大=${theoreticalMaxColumns}, 基准=${baseColumns}, 上限=${maxLimit}, 最终=${optimalColumns}`);
        
        return optimalColumns;
    },

    // 获取当前屏幕下的最大列数（保留作为备用方法）
    getMaxColumns() {
        const width = window.innerWidth;
        if (width <= this.config.breakpoints.mobile) {
            return 3; // 移动端
        } else if (width <= this.config.breakpoints.tablet) {
            return 6; // 平板端（增加到6列）
        } else if (width <= this.config.breakpoints.desktop) {
            return 8; // 小桌面端（增加到8列）
        } else {
            return 10; // 大桌面端（增加到10列）
        }
    },

    // 创建现代化悬浮列控制按钮
    createColumnControls() {
        // 移除现有控制器（可能在页面任何位置）
        const existing = document.querySelector('.column-controls');
        if (existing) existing.remove();

        // 如果没有隐藏列，不显示控制按钮
        if (this.state.hiddenColumns.length === 0) {
            console.log('没有隐藏列，不创建控制按钮');
            return;
        }

        const controls = document.createElement('div');
        controls.className = 'column-controls enhanced';

        // 添加吸附指示器
        const snapIndicator = this.config.snapToEdge.enabled ?
            '<div class="snap-indicator" title="边缘吸附已启用"><i class="fas fa-magnet"></i></div>' : '';

        controls.innerHTML = `
            ${snapIndicator}
            <div class="column-info">
                <div>
                    <span class="visible-count">${this.state.visibleColumns.length}</span>
                    <span style="font-size: 0.6rem;">/${this.state.allColumns.length}</span>
                </div>
                <div style="font-size: 0.6rem; opacity: 0.7;">列</div>
            </div>
            <button class="column-toggle-btn" data-action="toggle" title="切换列显示">
                <i class="fas fa-eye-slash"></i>
                <span class="btn-text">显示全部</span>
                <span class="hidden-count">${this.state.hiddenColumns.length}个隐藏</span>
            </button>
        `;

        // 添加事件监听
        const toggleBtn = controls.querySelector('.column-toggle-btn');
        toggleBtn.addEventListener('click', (e) => {
            // 如果正在拖拽，不触发点击事件
            if (this.state.isDragging) {
                e.preventDefault();
                return;
            }
            this.toggleAllColumns();
        });

        // 添加拖拽功能
        this.setupDragFunctionality(controls);

        // 恢复保存的位置
        this.restoreButtonPosition(controls);

        // 添加到body而不是表格容器，实现真正的悬浮效果
        document.body.appendChild(controls);
        console.log('✅ 创建了现代化可拖拽悬浮列控制按钮');
    },

    // 应用列可见性
    applyColumnVisibility() {
        if (!this.state.tableElement) {
            console.error('表格元素不存在');
            return;
        }

        const table = this.state.tableElement;
        
        // 重置所有列的显示状态
        table.querySelectorAll('th, td').forEach(cell => {
            cell.classList.remove('column-hidden', 'column-fade-in', 'column-fade-out');
        });

        // 添加表格状态类
        table.classList.toggle('columns-expanded', this.state.isExpanded);

        if (!this.state.isExpanded && this.state.hiddenColumns.length > 0) {
            // 隐藏指定列
            this.state.hiddenColumns.forEach(column => {
                const columnIndex = column.index;

                // 隐藏表头
                const th = table.querySelector(`thead th:nth-child(${columnIndex + 1})`);
                if (th) {
                    th.classList.add('column-hidden');
                }

                // 隐藏数据行
                table.querySelectorAll(`tbody td:nth-child(${columnIndex + 1})`).forEach(td => {
                    td.classList.add('column-hidden');
                });
            });
        }

        this.updateScrollIndicator();
        this.updateToggleButton();
    },

    // 切换所有列显示
    toggleAllColumns() {
        this.state.isExpanded = !this.state.isExpanded;
        console.log(`🔄 切换列显示状态: ${this.state.isExpanded ? '展开' : '收起'}`);
        
        // 添加切换动画
        const table = this.state.tableElement;
        if (table) {
            table.classList.add('column-transitioning');
            setTimeout(() => {
                table.classList.remove('column-transitioning');
            }, 300);
        }

        this.applyColumnVisibility();
    },

    // 更新悬浮切换按钮
    updateToggleButton() {
        const btn = document.querySelector('.column-toggle-btn');
        const btnText = btn?.querySelector('.btn-text');
        const btnIcon = btn?.querySelector('i');
        const hiddenCount = btn?.querySelector('.hidden-count');
        const visibleCountEl = document.querySelector('.column-info .visible-count');

        if (!btn) return;

        // 更新列计数显示
        if (visibleCountEl) {
            visibleCountEl.textContent = this.state.visibleColumns.length;
        }

        if (this.state.isExpanded) {
            btn.classList.add('active');
            btn.title = '收起部分列';
            if (btnIcon) btnIcon.className = 'fas fa-eye';
            if (btnText) btnText.textContent = '收起列';
            if (hiddenCount) hiddenCount.textContent = '显示全部';
        } else {
            btn.classList.remove('active');
            btn.title = '显示所有列';
            if (btnIcon) btnIcon.className = 'fas fa-eye-slash';
            if (btnText) btnText.textContent = '显示全部';
            if (hiddenCount) hiddenCount.textContent = `${this.state.hiddenColumns.length}个隐藏`;
        }
    },

    // 添加表格样式类
    addTableClasses() {
        if (this.state.tableElement) {
            this.state.tableElement.classList.add('responsive-table', 'column-managed');
        }
    },

    // 设置滚动指示器
    setupScrollIndicator() {
        const wrapper = document.querySelector('.table-wrapper');
        if (!wrapper) return;

        // 添加滚动监听
        wrapper.addEventListener('scroll', () => {
            this.updateScrollIndicator();
        });

        // 初始化滚动指示器
        this.updateScrollIndicator();
    },

    // 更新滚动指示器
    updateScrollIndicator() {
        const wrapper = document.querySelector('.table-wrapper');
        if (!wrapper) return;

        const hasScroll = wrapper.scrollWidth > wrapper.clientWidth;
        const atStart = wrapper.scrollLeft <= 0;
        const atEnd = wrapper.scrollLeft >= (wrapper.scrollWidth - wrapper.clientWidth - 1);

        wrapper.classList.toggle('has-scroll', hasScroll);
        wrapper.classList.toggle('scroll-at-start', atStart);
        wrapper.classList.toggle('scroll-at-end', atEnd);

        // 更新滚动进度指示器
        if (hasScroll) {
            const progress = wrapper.scrollLeft / (wrapper.scrollWidth - wrapper.clientWidth);
            wrapper.style.setProperty('--scroll-progress', progress);
        }
    },

    // 处理窗口大小变化
    handleResize() {
        clearTimeout(this.resizeTimer);
        this.resizeTimer = setTimeout(() => {
            console.log('🔄 响应窗口大小变化，重新计算列布局');
            
            // 如果切换到移动端，移除列管理功能
            if (this.isMobileDevice()) {
                console.log('📱 切换到移动端，移除列管理功能');
                this.removeColumnControls();
                return;
            }
            
            // PC端重新计算列布局
            const containerWidth = document.querySelector('.table-container')?.offsetWidth || window.innerWidth;
            const oldMaxColumns = this.calculateOptimalColumns(containerWidth);
            this.calculateVisibleColumns();
            const newMaxColumns = this.calculateOptimalColumns(containerWidth);
            
            if (oldMaxColumns !== newMaxColumns) {
                this.createColumnControls();
                this.applyColumnVisibility();
            }
        }, 250);
    },

    // 设置拖拽功能
    setupDragFunctionality(controls) {
        let isDragging = false;
        let hasMoved = false;
        let startX, startY, offsetX, offsetY;

        // 鼠标按下开始拖拽
        controls.addEventListener('mousedown', (e) => {
            // 只有点击按钮本身时才开始拖拽，不包括toggle按钮
            if (e.target.closest('.column-toggle-btn')) return;

            isDragging = true;
            hasMoved = false;
            this.state.isDragging = false; // 初始时还没有移动

            // 记录鼠标起始位置
            startX = e.clientX;
            startY = e.clientY;

            // 获取按钮当前位置
            const rect = controls.getBoundingClientRect();

            // 计算鼠标相对于按钮的偏移量
            offsetX = startX - rect.left;
            offsetY = startY - rect.top;

            // 立即设置为绝对定位，避免transform冲突
            controls.style.position = 'fixed';
            controls.style.left = rect.left + 'px';
            controls.style.top = rect.top + 'px';
            controls.style.right = 'auto';
            controls.style.transform = 'none';
            controls.style.zIndex = '1001';

            controls.classList.add('dragging');
            controls.style.cursor = 'grabbing';

            // 添加body类防止页面滚动
            document.body.classList.add('dragging-column-controls');

            // 阻止文本选择和默认行为
            e.preventDefault();
            e.stopPropagation();

            console.log('🖱️ 开始拖拽悬浮按钮');
        });

        // 鼠标移动
        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            // 阻止默认行为
            e.preventDefault();

            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            // 如果移动距离超过阈值，标记为真正的拖拽
            if (!hasMoved && (Math.abs(deltaX) > 3 || Math.abs(deltaY) > 3)) {
                hasMoved = true;
                this.state.isDragging = true;
            }

            if (hasMoved) {
                // 计算新位置（鼠标位置减去偏移量）
                const newX = e.clientX - offsetX;
                const newY = e.clientY - offsetY;

                // 获取按钮尺寸
                const controlsWidth = controls.offsetWidth;
                const controlsHeight = controls.offsetHeight;

                // 限制在屏幕边界内，留出一些边距
                const margin = 10;
                const maxX = window.innerWidth - controlsWidth - margin;
                const maxY = window.innerHeight - controlsHeight - margin;

                const boundedX = Math.max(margin, Math.min(newX, maxX));
                const boundedY = Math.max(margin, Math.min(newY, maxY));

                // 实时更新位置
                controls.style.left = boundedX + 'px';
                controls.style.top = boundedY + 'px';
            }
        });

        // 鼠标释放结束拖拽
        document.addEventListener('mouseup', (e) => {
            if (isDragging) {
                isDragging = false;
                controls.classList.remove('dragging');
                controls.style.cursor = 'grab';
                controls.style.zIndex = '1000';

                // 移除body类恢复页面滚动
                document.body.classList.remove('dragging-column-controls');

                // 如果有移动，执行边缘吸附
                if (hasMoved) {
                    this.snapToEdge(controls);
                }

                // 延迟重置拖拽状态，避免影响点击事件
                setTimeout(() => {
                    this.state.isDragging = false;
                }, 150);

                console.log('🖱️ 结束拖拽悬浮按钮');
            }
        });

        // 添加触摸支持（移动端）
        controls.addEventListener('touchstart', (e) => {
            if (e.target.closest('.column-toggle-btn')) return;

            const touch = e.touches[0];
            const mouseEvent = new MouseEvent('mousedown', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            controls.dispatchEvent(mouseEvent);
            e.preventDefault();
        });

        document.addEventListener('touchmove', (e) => {
            if (!isDragging) return;

            const touch = e.touches[0];
            const mouseEvent = new MouseEvent('mousemove', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            document.dispatchEvent(mouseEvent);
            e.preventDefault();
        });

        document.addEventListener('touchend', (e) => {
            if (isDragging) {
                const mouseEvent = new MouseEvent('mouseup', {});
                document.dispatchEvent(mouseEvent);
                e.preventDefault();
            }
        });

        // 添加拖拽手势提示
        controls.style.cursor = 'grab';
        controls.title = '拖拽移动位置 | 点击切换列显示';
    },

    // 保存按钮位置到本地存储
    saveButtonPosition(controls) {
        const rect = controls.getBoundingClientRect();
        const position = {
            left: rect.left,
            top: rect.top,
            timestamp: Date.now()
        };
        localStorage.setItem('columnControlsPosition', JSON.stringify(position));
    },

    // 恢复按钮位置
    restoreButtonPosition(controls) {
        try {
            const saved = localStorage.getItem('columnControlsPosition');
            if (saved) {
                const position = JSON.parse(saved);

                // 获取按钮尺寸
                const controlsWidth = controls.offsetWidth || 80;
                const controlsHeight = controls.offsetHeight || 120;
                const margin = 10;

                // 检查保存的位置是否还在有效范围内
                const maxX = window.innerWidth - controlsWidth - margin;
                const maxY = window.innerHeight - controlsHeight - margin;

                if (position.left >= margin && position.left <= maxX &&
                    position.top >= margin && position.top <= maxY) {

                    // 设置为绝对定位
                    controls.style.position = 'fixed';
                    controls.style.left = position.left + 'px';
                    controls.style.top = position.top + 'px';
                    controls.style.right = 'auto';
                    controls.style.transform = 'none';

                    console.log('📍 已恢复悬浮按钮位置:', position);
                } else {
                    console.log('📍 保存的位置超出范围，使用默认位置');
                    this.setDefaultPosition(controls);
                }
            } else {
                console.log('📍 没有保存的位置，使用默认位置');
                this.setDefaultPosition(controls);
            }
        } catch (error) {
            console.warn('恢复按钮位置失败:', error);
            this.setDefaultPosition(controls);
        }
    },

    // 设置默认位置
    setDefaultPosition(controls) {
        controls.style.position = 'fixed';
        controls.style.right = '20px';
        controls.style.top = '50%';
        controls.style.left = 'auto';
        controls.style.transform = 'translateY(-50%)';
    },

    // 边缘吸附功能
    snapToEdge(controls) {
        // 检查是否启用边缘吸附
        if (!this.config.snapToEdge.enabled) {
            this.saveButtonPosition(controls);
            return;
        }

        const rect = controls.getBoundingClientRect();
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const controlsWidth = controls.offsetWidth;
        const controlsHeight = controls.offsetHeight;

        // 当前位置
        const currentX = rect.left;
        const currentY = rect.top;

        // 计算到各边缘的距离
        const distanceToLeft = currentX;
        const distanceToRight = windowWidth - (currentX + controlsWidth);
        const distanceToTop = currentY;
        const distanceToBottom = windowHeight - (currentY + controlsHeight);

        // 找到最近的边缘
        const minDistance = Math.min(distanceToLeft, distanceToRight, distanceToTop, distanceToBottom);

        // 检查是否在吸附阈值范围内
        if (minDistance > this.config.snapToEdge.threshold) {
            console.log('🚫 距离边缘太远，不执行吸附');
            this.saveButtonPosition(controls);
            return;
        }

        // 吸附边距
        const snapMargin = this.config.snapToEdge.margin;
        let targetX = currentX;
        let targetY = currentY;
        let snapDirection = '';

        // 智能吸附：优先考虑左右边缘，因为悬浮按钮通常放在侧边
        const horizontalDistance = Math.min(distanceToLeft, distanceToRight);
        const verticalDistance = Math.min(distanceToTop, distanceToBottom);

        if (horizontalDistance <= verticalDistance) {
            // 吸附到左右边缘
            if (distanceToLeft <= distanceToRight) {
                targetX = snapMargin;
                snapDirection = '左边缘';
            } else {
                targetX = windowWidth - controlsWidth - snapMargin;
                snapDirection = '右边缘';
            }
        } else {
            // 吸附到上下边缘
            if (distanceToTop <= distanceToBottom) {
                targetY = snapMargin;
                snapDirection = '顶部边缘';
            } else {
                targetY = windowHeight - controlsHeight - snapMargin;
                snapDirection = '底部边缘';
            }
        }

        console.log(`🧲 吸附到${snapDirection}，距离: ${minDistance.toFixed(1)}px`);

        // 执行吸附动画
        this.animateToPosition(controls, targetX, targetY);
    },

    // 动画移动到目标位置
    animateToPosition(controls, targetX, targetY) {
        const startX = parseFloat(controls.style.left) || 0;
        const startY = parseFloat(controls.style.top) || 0;

        const deltaX = targetX - startX;
        const deltaY = targetY - startY;

        // 如果移动距离很小，直接设置位置，不执行动画
        const totalDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        if (totalDistance < 5) {
            controls.style.left = targetX + 'px';
            controls.style.top = targetY + 'px';
            this.saveButtonPosition(controls);
            return;
        }

        const duration = this.config.snapToEdge.animationDuration;
        const startTime = performance.now();

        // 添加吸附动画类
        controls.classList.add('snapping');

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数（ease-out cubic）
            const easeProgress = 1 - Math.pow(1 - progress, 3);

            const currentX = startX + deltaX * easeProgress;
            const currentY = startY + deltaY * easeProgress;

            controls.style.left = currentX + 'px';
            controls.style.top = currentY + 'px';

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // 动画完成
                controls.classList.remove('snapping');

                // 确保最终位置精确
                controls.style.left = targetX + 'px';
                controls.style.top = targetY + 'px';

                // 保存最终位置
                this.saveButtonPosition(controls);
                console.log('💾 已保存吸附后的悬浮按钮位置');
            }
        };

        requestAnimationFrame(animate);
    },

    // 移除列控制按钮
    removeColumnControls() {
        const existing = document.querySelector('.column-controls');
        if (existing) {
            existing.remove();
            console.log('🗑️ 已移除列控制按钮');
        }
        
        // 重置所有列为显示状态
        if (this.state.tableElement) {
            this.state.tableElement.querySelectorAll('th, td').forEach(cell => {
                cell.classList.remove('column-hidden');
            });
        }
    },

    // 获取当前状态信息
    getStatus() {
        return {
            totalColumns: this.state.allColumns.length,
            visibleColumns: this.state.visibleColumns.length,
            hiddenColumns: this.state.hiddenColumns.length,
            isExpanded: this.state.isExpanded,
            screenWidth: window.innerWidth,
            maxColumns: this.getMaxColumns()
        };
    }
};

// 应用初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('📱 现代化逾期订单应用启动');

    // 初始化应用
    AppFunctions.init();

    // 更新UI
    UIFunctions.updateStatusCards();

    // 隐藏初始加载状态
    setTimeout(() => {
        UIFunctions.hideLoading();
    }, 500);

    // 点击外部关闭导出菜单
    document.addEventListener('click', function(event) {
        const exportDropdown = document.querySelector('.export-dropdown');
        const exportMenu = document.getElementById('exportMenu');

        if (exportDropdown && exportMenu && !exportDropdown.contains(event.target)) {
            exportMenu.classList.remove('show');
        }
    });

    console.log('✅ 应用初始化完成');
});

// 页面卸载前清理
window.addEventListener('beforeunload', function() {
    // 清理定时器
    if (AppFunctions.resizeTimer) {
        clearTimeout(AppFunctions.resizeTimer);
    }
});

// 增强型测试和调试函数
function testColumnManager() {
    console.log('🧪 手动测试增强型列管理器');
    console.log('当前表格:', document.querySelector('#overdueTable'));
    console.log('表格列数:', document.querySelectorAll('#overdueTable th').length);
    ColumnManager.init();
}

// 获取列管理器状态
function getColumnManagerStatus() {
    const status = ColumnManager.getStatus();
    console.log('📊 列管理器状态:', status);
    return status;
}

// 强制重新初始化列管理器
function reinitColumnManager() {
    console.log('🔄 强制重新初始化列管理器');
    ColumnManager.init();
}

// 切换列展开状态（调试用）
function toggleColumns() {
    console.log('🔀 手动切换列展开状态');
    ColumnManager.toggleAllColumns();
}

// 重置悬浮按钮位置
function resetButtonPosition() {
    console.log('🔄 重置悬浮按钮位置');
    localStorage.removeItem('columnControlsPosition');
    const controls = document.querySelector('.column-controls');
    if (controls) {
        // 使用ColumnManager的默认位置设置方法
        ColumnManager.setDefaultPosition(controls);
        console.log('✅ 悬浮按钮已重置到默认位置');
    }
}

// 切换边缘吸附功能
function toggleSnapToEdge() {
    const currentState = ColumnManager.config.snapToEdge.enabled;
    ColumnManager.config.snapToEdge.enabled = !currentState;
    console.log(`🧲 边缘吸附功能已${ColumnManager.config.snapToEdge.enabled ? '启用' : '禁用'}`);

    // 保存设置到本地存储
    localStorage.setItem('columnControlsSnapEnabled', ColumnManager.config.snapToEdge.enabled);

    return ColumnManager.config.snapToEdge.enabled;
}

// 设置吸附阈值
function setSnapThreshold(threshold) {
    if (threshold >= 50 && threshold <= 200) {
        ColumnManager.config.snapToEdge.threshold = threshold;
        console.log(`🎯 吸附阈值已设置为: ${threshold}px`);

        // 保存设置到本地存储
        localStorage.setItem('columnControlsSnapThreshold', threshold);
    } else {
        console.warn('⚠️ 吸附阈值必须在50-200px之间');
    }
}

// 手动触发吸附
function snapButtonToEdge() {
    const controls = document.querySelector('.column-controls');
    if (controls && ColumnManager.snapToEdge) {
        console.log('🧲 手动触发边缘吸附');
        ColumnManager.snapToEdge(controls);
    }
}

// 获取吸附配置
function getSnapConfig() {
    const config = ColumnManager.config.snapToEdge;
    console.log('🔧 当前吸附配置:', config);
    return config;
}

// 导出全局对象供调试使用
window.OverdueOrdersApp = OverdueOrdersApp;
window.AppFunctions = AppFunctions;
window.UIFunctions = UIFunctions;
window.ColumnManager = ColumnManager;
window.testColumnManager = testColumnManager;
window.getColumnManagerStatus = getColumnManagerStatus;
window.reinitColumnManager = reinitColumnManager;
window.toggleColumns = toggleColumns;
window.resetButtonPosition = resetButtonPosition;
// 边缘吸附相关函数
window.toggleSnapToEdge = toggleSnapToEdge;
window.setSnapThreshold = setSnapThreshold;
window.snapButtonToEdge = snapButtonToEdge;
window.getSnapConfig = getSnapConfig;
</script>

<!-- 引入必要的外部脚本 -->
<script src="{{ url_for('static', filename='js/modern-overdue-orders.js') }}"></script>
{% endblock %}
