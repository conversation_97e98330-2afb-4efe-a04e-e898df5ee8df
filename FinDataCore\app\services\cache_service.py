"""
缓存服务 - 支持内存缓存和Redis缓存
"""
import asyncio
import json
import logging
import time
from typing import Any, Dict, Optional, Union
from cachetools import TTLCache
from app.core.config import settings

logger = logging.getLogger(__name__)

class CacheService:
    """缓存服务类"""
    
    def __init__(self):
        """初始化缓存服务"""
        self.logger = logging.getLogger(__name__)
        self.cache_type = settings.CACHE_TYPE
        self.default_timeout = settings.CACHE_DEFAULT_TIMEOUT
        self.cache_timeouts = settings.CACHE_TIMEOUTS
        
        # 初始化内存缓存
        self.memory_cache = TTLCache(maxsize=1000, ttl=self.default_timeout)
        
        # Redis缓存（如果配置了）
        self.redis_client = None
        if self.cache_type == "redis":
            self._init_redis()
    
    def _init_redis(self):
        """初始化Redis连接"""
        try:
            import redis.asyncio as redis
            self.redis_client = redis.from_url(
                settings.CACHE_REDIS_URL,
                encoding="utf-8",
                decode_responses=True
            )
            self.logger.info("Redis缓存已初始化")
        except ImportError:
            self.logger.warning("Redis未安装，使用内存缓存")
            self.cache_type = "memory"
        except Exception as e:
            self.logger.error(f"Redis连接失败: {str(e)}，使用内存缓存")
            self.cache_type = "memory"
    
    def _get_cache_timeout(self, cache_key: str) -> int:
        """
        获取缓存超时时间
        
        Args:
            cache_key: 缓存键
            
        Returns:
            超时时间（秒）
        """
        # 从缓存键中提取端点名称
        for endpoint, timeout in self.cache_timeouts.items():
            if endpoint in cache_key:
                return timeout
        return self.default_timeout
    
    def _serialize_value(self, value: Any) -> str:
        """序列化值"""
        try:
            return json.dumps(value, ensure_ascii=False, default=str)
        except Exception as e:
            self.logger.error(f"序列化失败: {str(e)}")
            return json.dumps({"error": "序列化失败"})
    
    def _deserialize_value(self, value: str) -> Any:
        """反序列化值"""
        try:
            return json.loads(value)
        except Exception as e:
            self.logger.error(f"反序列化失败: {str(e)}")
            return None
    
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值或None
        """
        try:
            if self.cache_type == "redis" and self.redis_client:
                value = await self.redis_client.get(key)
                if value:
                    return self._deserialize_value(value)
            else:
                # 使用内存缓存
                return self.memory_cache.get(key)
        except Exception as e:
            self.logger.error(f"获取缓存失败 {key}: {str(e)}")
        
        return None
    
    async def set(self, key: str, value: Any, timeout: Optional[int] = None) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            timeout: 超时时间（秒），None使用默认值
            
        Returns:
            是否设置成功
        """
        if timeout is None:
            timeout = self._get_cache_timeout(key)
        
        try:
            if self.cache_type == "redis" and self.redis_client:
                serialized_value = self._serialize_value(value)
                await self.redis_client.setex(key, timeout, serialized_value)
                return True
            else:
                # 使用内存缓存
                self.memory_cache[key] = value
                return True
        except Exception as e:
            self.logger.error(f"设置缓存失败 {key}: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """
        删除缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        try:
            if self.cache_type == "redis" and self.redis_client:
                await self.redis_client.delete(key)
                return True
            else:
                # 使用内存缓存
                if key in self.memory_cache:
                    del self.memory_cache[key]
                return True
        except Exception as e:
            self.logger.error(f"删除缓存失败 {key}: {str(e)}")
            return False
    
    async def exists(self, key: str) -> bool:
        """
        检查缓存键是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            是否存在
        """
        try:
            if self.cache_type == "redis" and self.redis_client:
                return await self.redis_client.exists(key) > 0
            else:
                # 使用内存缓存
                return key in self.memory_cache
        except Exception as e:
            self.logger.error(f"检查缓存存在性失败 {key}: {str(e)}")
            return False
    
    async def clear(self) -> bool:
        """
        清空所有缓存
        
        Returns:
            是否清空成功
        """
        try:
            if self.cache_type == "redis" and self.redis_client:
                await self.redis_client.flushdb()
                return True
            else:
                # 使用内存缓存
                self.memory_cache.clear()
                return True
        except Exception as e:
            self.logger.error(f"清空缓存失败: {str(e)}")
            return False
    
    def generate_cache_key(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> str:
        """
        生成缓存键
        
        Args:
            endpoint: API端点名称
            params: 参数字典
            
        Returns:
            缓存键字符串
        """
        if params is None:
            params = {}
        
        # 将参数排序以确保相同参数生成相同的键
        sorted_params = sorted(params.items())
        params_str = "_".join([f"{k}={v}" for k, v in sorted_params])
        
        # 生成键
        cache_key = f"{endpoint}_{params_str}" if params_str else endpoint
        return cache_key
    
    async def get_or_set(
        self, 
        key: str, 
        func, 
        timeout: Optional[int] = None,
        *args, 
        **kwargs
    ) -> Any:
        """
        获取缓存值，如果不存在则调用函数并缓存结果
        
        Args:
            key: 缓存键
            func: 要调用的函数
            timeout: 超时时间
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            缓存值或函数结果
        """
        # 尝试从缓存获取
        cached_value = await self.get(key)
        if cached_value is not None:
            self.logger.info(f"缓存命中: {key}")
            return cached_value
        
        # 缓存未命中，调用函数
        self.logger.info(f"缓存未命中，调用函数: {key}")
        try:
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # 缓存结果
            await self.set(key, result, timeout)
            return result
        except Exception as e:
            self.logger.error(f"函数调用失败 {key}: {str(e)}")
            raise

# 创建全局缓存服务实例
cache_service = CacheService()
