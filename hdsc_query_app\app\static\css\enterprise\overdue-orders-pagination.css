/**
 * 企业级逾期订单分页样式
 * 专门为逾期订单页面提供美观的移动端分页外观
 * 支持四级响应式断点设计
 */

/* ==================== 分页容器基础样式 ==================== */
.pagination-container {
    padding: 16px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin: 20px 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pagination-container .pagination {
    margin: 0;
    justify-content: center;
    gap: 8px;
    flex-wrap: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
    padding: 8px 16px;
}

.pagination-container .pagination::-webkit-scrollbar {
    display: none;
}

/* ==================== 分页按钮基础样式 ==================== */
.pagination-container .page-item {
    margin: 0;
    flex-shrink: 0;
}

.pagination-container .page-link {
    border: 1.5px solid #e9ecef;
    color: #495057;
    padding: 10px 16px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.4;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.pagination-container .page-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.pagination-container .page-link:hover::before {
    left: 100%;
}

.pagination-container .page-link:hover {
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
}

/* 当前页按钮样式 */
.pagination-container .page-item.active .page-link {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    color: white;
    font-weight: 600;
    box-shadow: 0 3px 8px rgba(0, 123, 255, 0.3);
    transform: scale(1.05);
}

.pagination-container .page-item.active .page-link:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: scale(1.05) translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

/* 禁用按钮样式 */
.pagination-container .page-item.disabled .page-link {
    color: #adb5bd;
    border-color: #e9ecef;
    background: #f8f9fa;
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
    box-shadow: none;
}

.pagination-container .page-item.disabled .page-link:hover {
    color: #adb5bd;
    border-color: #e9ecef;
    background: #f8f9fa;
    transform: none;
    box-shadow: none;
}

/* ==================== 导航按钮样式 ==================== */
.pagination-container .page-link i {
    font-size: 12px;
    margin: 0 4px;
}

.pagination-container .page-item:first-child .page-link,
.pagination-container .page-item:last-child .page-link {
    padding: 10px 18px;
    font-weight: 600;
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    border-color: #6c757d;
}

.pagination-container .page-item:first-child .page-link:hover,
.pagination-container .page-item:last-child .page-link:hover {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
    border-color: #495057;
    color: white;
}

/* ==================== 页码指示器样式 ==================== */
.page-indicator .page-indicator-content {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border-color: #17a2b8;
    color: white;
    font-weight: 600;
    padding: 10px 16px;
    min-width: 80px;
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 8px;
    box-shadow: 0 3px 8px rgba(23, 162, 184, 0.25);
}

.page-indicator .page-indicator-content i {
    font-size: 14px;
    opacity: 0.9;
}

.page-indicator .page-text {
    font-size: 13px;
    letter-spacing: 0.5px;
}

/* ==================== 滑动提示样式 ==================== */
.swipe-hint {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 12px;
    padding: 8px 16px;
    background: rgba(108, 117, 125, 0.1);
    border-radius: 20px;
    color: #6c757d;
    font-size: 12px;
    font-weight: 500;
    opacity: 1;
    transition: opacity 0.3s ease;
    animation: fadeInUp 0.5s ease;
}

.swipe-hint i {
    font-size: 14px;
    animation: bounceX 1.5s ease-in-out infinite;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceX {
    0%, 100% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(4px);
    }
}

/* ==================== 数据信息提示样式 ==================== */
.pagination-container + .mt-2 {
    background: rgba(0, 123, 255, 0.05);
    padding: 8px 16px;
    border-radius: 6px;
    margin-top: 12px !important;
    border-left: 3px solid #007bff;
    font-size: 13px;
    color: #495057;
}

/* ==================== 超小屏优化 (≤375px) ==================== */
.overdue-pagination-extrasmall .pagination {
    gap: 6px;
    padding: 6px 12px;
}

.overdue-pagination-extrasmall .page-link {
    min-width: 36px;
    min-height: 36px;
    padding: 8px 12px;
    font-size: 13px;
}

.overdue-pagination-extrasmall .page-indicator-content {
    min-width: 70px;
    padding: 8px 12px;
    font-size: 12px;
}

.overdue-pagination-extrasmall .page-indicator-content i {
    font-size: 12px;
}

.overdue-pagination-extrasmall .page-text {
    font-size: 12px;
}

.overdue-pagination-extrasmall .swipe-hint {
    font-size: 11px;
    padding: 6px 12px;
    margin-top: 8px;
}

/* 超小屏特有动画 */
.overdue-pagination-extrasmall .page-indicator {
    animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 3px 8px rgba(23, 162, 184, 0.25);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 4px 12px rgba(23, 162, 184, 0.35);
    }
}

/* ==================== 小屏优化 (≤480px) ==================== */
.overdue-pagination-small .pagination {
    gap: 6px;
    padding: 8px 14px;
}

.overdue-pagination-small .page-link {
    min-width: 38px;
    min-height: 38px;
    padding: 8px 14px;
    font-size: 13px;
}

.overdue-pagination-small .page-item:first-child .page-link,
.overdue-pagination-small .page-item:last-child .page-link {
    padding: 8px 16px;
}

/* ==================== 移动端优化 (≤767px) ==================== */
.overdue-pagination-mobile .pagination {
    gap: 8px;
    padding: 8px 16px;
}

.overdue-pagination-mobile .page-link {
    min-width: 40px;
    min-height: 40px;
    padding: 9px 15px;
}

/* 移动端触摸反馈增强 */
.overdue-pagination-mobile .page-link:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

/* ==================== 桌面端优化 (≥768px) ==================== */
.overdue-pagination-desktop .pagination {
    gap: 10px;
    padding: 12px 20px;
}

.overdue-pagination-desktop .page-link {
    min-width: 44px;
    min-height: 44px;
    padding: 10px 16px;
    font-size: 14px;
}

.overdue-pagination-desktop .page-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0, 123, 255, 0.2);
}

/* 桌面端高级动效 */
.overdue-pagination-desktop .page-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.overdue-pagination-desktop .page-item:hover {
    transform: translateY(-2px);
}

/* ==================== 加载和过渡动画 ==================== */
.pagination-container.loading {
    opacity: 0.7;
    pointer-events: none;
}

.pagination-container.loading .page-link {
    animation: shimmer 1.5s ease-in-out infinite;
}

@keyframes shimmer {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

/* 页面切换时的表格动画 */
#overdueTable {
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
                transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ==================== 高对比度和可访问性 ==================== */
@media (prefers-contrast: high) {
    .pagination-container .page-link {
        border-width: 2px;
        font-weight: 600;
    }
    
    .pagination-container .page-item.active .page-link {
        background: #0056b3;
        border-color: #004085;
    }
}

/* 减少动效模式 */
@media (prefers-reduced-motion: reduce) {
    .pagination-container .page-link,
    .pagination-container .page-item,
    #overdueTable,
    .swipe-hint,
    .page-indicator {
        transition: none;
        animation: none;
    }
}

/* ==================== 暗色模式支持 ==================== */
@media (prefers-color-scheme: dark) {
    .pagination-container {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
    
    .pagination-container .page-link {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }
    
    .pagination-container .page-link:hover {
        background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
        border-color: #007bff;
        color: #90cdf4;
    }
    
    .pagination-container .page-item.active .page-link {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }
    
    .page-indicator .page-indicator-content {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    }
    
    .swipe-hint {
        background: rgba(255, 255, 255, 0.1);
        color: #a0aec0;
    }
} 