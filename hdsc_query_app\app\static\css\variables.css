/**
 * CSS变量系统 - 统一管理项目样式
 * 优先级：高 - 必须最先加载
 */

:root {
  /* 断点系统 - 与Bootstrap 5保持一致 */
  --breakpoint-xs: 0px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1400px;
  
  /* 自定义断点 */
  --breakpoint-mobile: 767.98px;
  --breakpoint-small-mobile: 480px;
  --breakpoint-tiny-mobile: 375px;
  
  /* 颜色系统 */
  --color-primary: #007bff;
  --color-primary-hover: #0069d9;
  --color-primary-dark: #0062cc;
  
  --color-success: #28a745;
  --color-warning: #ffc107;
  --color-danger: #dc3545;
  --color-info: #17a2b8;
  
  --color-light: #f8f9fa;
  --color-dark: #343a40;
  --color-muted: #6c757d;
  
  /* 表格状态颜色 */
  --status-overdue-bg: #ffffc7ce;
  --status-early-bg: #ffd9e1f2;
  --status-ontime-bg: #ffc6efce;
  --status-upcoming-bg: #fff4b084;
  --status-collection-bg: #ffffeb9c;
  --status-normal-bg: #d4edda;
  
  /* 间距系统 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 3rem;
  
  /* 边框圆角 */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 1rem;
  
  /* 阴影系统 */
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  
  /* 字体大小 */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  
  /* 表格专用变量 */
  --table-border-color: #dee2e6;
  --table-hover-bg: rgba(0, 123, 255, 0.04);
  --table-stripe-bg: rgba(0, 0, 0, 0.02);
  --table-head-bg: #f8f9fa;
  --table-head-color: #495057;
  
  /* 分页变量 */
  --pagination-padding: 0.375rem 0.75rem;
  --pagination-margin: 0 0.125rem;
  --pagination-border-radius: var(--border-radius-md);
  --pagination-hover-bg: #e9ecef;
  --pagination-active-bg: var(--color-primary);
  --pagination-active-color: #fff;
  --pagination-disabled-color: var(--color-muted);
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Z-index层级 */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal: 1040;
  --z-index-tooltip: 1070;
  
  /* 侧边栏变量 */
  --sidebar-width: 250px;
  --sidebar-width-collapsed: 60px;
  --sidebar-bg: #343a40;
  --sidebar-color: #fff;
  --sidebar-transition: all 0.3s ease;
}

/* 深色模式支持（预留） */
@media (prefers-color-scheme: dark) {
  :root {
    --color-light: #212529;
    --color-dark: #f8f9fa;
    --table-head-bg: #2d3748;
    --table-head-color: #e2e8f0;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --table-border-color: #000;
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.5);
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: 0s;
    --transition-normal: 0s;
    --transition-slow: 0s;
  }
}