"""
认证API测试
"""
import pytest
import httpx
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

class TestAuth:
    """认证测试类"""
    
    def test_login_valid_password(self):
        """测试有效密码登录"""
        response = client.post(
            "/api/v1/auth/login",
            json={"password": "TT2024"}
        )
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert data["user_level"] == "limited"
        assert "permissions" in data
    
    def test_login_invalid_password(self):
        """测试无效密码登录"""
        response = client.post(
            "/api/v1/auth/login",
            json={"password": "invalid_password"}
        )
        assert response.status_code == 401
        assert "Invalid password" in response.json()["detail"]
    
    def test_login_missing_password(self):
        """测试缺少密码"""
        response = client.post(
            "/api/v1/auth/login",
            json={}
        )
        assert response.status_code == 422  # Validation error
    
    def test_get_current_user_with_token(self):
        """测试使用token获取当前用户信息"""
        # 先登录获取token
        login_response = client.post(
            "/api/v1/auth/login",
            json={"password": "881017"}
        )
        token = login_response.json()["access_token"]
        
        # 使用token获取用户信息
        response = client.get(
            "/api/v1/auth/me",
            headers={"Authorization": f"Bearer {token}"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["username"] == "881017"
        assert data["user_level"] == "standard"
        assert "read" in data["permissions"]
        assert "export" in data["permissions"]
    
    def test_get_current_user_without_token(self):
        """测试不使用token获取用户信息"""
        response = client.get("/api/v1/auth/me")
        assert response.status_code == 403  # Forbidden
    
    def test_get_current_user_invalid_token(self):
        """测试使用无效token"""
        response = client.get(
            "/api/v1/auth/me",
            headers={"Authorization": "Bearer invalid_token"}
        )
        assert response.status_code == 401
    
    def test_logout(self):
        """测试登出"""
        response = client.post("/api/v1/auth/logout")
        assert response.status_code == 200
        assert "logged out" in response.json()["message"]
    
    def test_get_permissions(self):
        """测试获取权限信息"""
        # 先登录
        login_response = client.post(
            "/api/v1/auth/login",
            json={"password": "Doolin"}
        )
        token = login_response.json()["access_token"]
        
        # 获取权限信息
        response = client.get(
            "/api/v1/auth/permissions",
            headers={"Authorization": f"Bearer {token}"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["user_level"] == "full"
        assert "admin" in data["permissions"]
    
    def test_different_user_levels(self):
        """测试不同用户级别"""
        test_cases = [
            ("TT2024", "limited", ["read"]),
            ("881017", "standard", ["read", "export"]),
            ("Doolin", "full", ["read", "export", "admin"])
        ]
        
        for password, expected_level, expected_permissions in test_cases:
            response = client.post(
                "/api/v1/auth/login",
                json={"password": password}
            )
            assert response.status_code == 200
            data = response.json()
            assert data["user_level"] == expected_level
            for perm in expected_permissions:
                assert perm in data["permissions"]

@pytest.fixture
def auth_headers():
    """获取认证头的fixture"""
    def _get_headers(user_level="standard"):
        password_map = {
            "limited": "TT2024",
            "standard": "881017", 
            "full": "Doolin"
        }
        
        response = client.post(
            "/api/v1/auth/login",
            json={"password": password_map[user_level]}
        )
        token = response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    
    return _get_headers
