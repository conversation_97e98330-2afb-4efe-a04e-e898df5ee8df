#!/bin/bash

# FinDataCore 部署脚本
# 用于快速部署FastAPI应用

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 检查环境变量
check_env() {
    if [ ! -f .env ]; then
        log_warning ".env文件不存在，将使用默认配置"
        cp .env.example .env 2>/dev/null || true
    fi
    
    log_success "环境配置检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p data
    mkdir -p ssl
    
    # 设置权限
    chmod 755 logs data
    
    log_success "目录创建完成"
}

# 构建Docker镜像
build_image() {
    log_info "构建Docker镜像..."
    
    docker-compose build --no-cache
    
    log_success "Docker镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 停止现有服务
    docker-compose down 2>/dev/null || true
    
    # 启动基础服务
    docker-compose up -d
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待API服务
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8000/health >/dev/null 2>&1; then
            log_success "API服务已就绪"
            break
        fi
        
        log_info "等待API服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "API服务启动超时"
        exit 1
    fi
}

# 运行健康检查
health_check() {
    log_info "运行健康检查..."
    
    # 检查API健康状态
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        log_success "API服务健康检查通过"
    else
        log_error "API服务健康检查失败"
        return 1
    fi
    
    # 检查Redis连接
    if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
        log_success "Redis服务健康检查通过"
    else
        log_warning "Redis服务健康检查失败"
    fi
    
    log_success "健康检查完成"
}

# 显示服务信息
show_info() {
    log_info "服务信息:"
    echo "  API服务: http://localhost:8000"
    echo "  API文档: http://localhost:8000/docs"
    echo "  健康检查: http://localhost:8000/health"
    echo ""
    echo "Docker服务状态:"
    docker-compose ps
}

# 清理函数
cleanup() {
    log_info "清理资源..."
    docker-compose down
    docker system prune -f
    log_success "清理完成"
}

# 备份函数
backup() {
    log_info "备份数据..."
    
    local backup_dir="backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份日志
    cp -r logs "$backup_dir/" 2>/dev/null || true
    
    # 备份配置
    cp .env "$backup_dir/" 2>/dev/null || true
    cp docker-compose.yml "$backup_dir/"
    
    log_success "备份完成: $backup_dir"
}

# 更新函数
update() {
    log_info "更新应用..."
    
    # 备份当前版本
    backup
    
    # 拉取最新代码
    git pull 2>/dev/null || log_warning "无法拉取最新代码"
    
    # 重新构建和部署
    build_image
    start_services
    wait_for_services
    health_check
    
    log_success "更新完成"
}

# 主函数
main() {
    local command=${1:-deploy}
    
    case $command in
        "deploy")
            log_info "开始部署FinDataCore..."
            check_docker
            check_env
            create_directories
            build_image
            start_services
            wait_for_services
            health_check
            show_info
            log_success "部署完成！"
            ;;
        "start")
            log_info "启动服务..."
            start_services
            wait_for_services
            show_info
            ;;
        "stop")
            log_info "停止服务..."
            docker-compose down
            log_success "服务已停止"
            ;;
        "restart")
            log_info "重启服务..."
            docker-compose restart
            wait_for_services
            show_info
            ;;
        "status")
            show_info
            ;;
        "logs")
            docker-compose logs -f
            ;;
        "cleanup")
            cleanup
            ;;
        "backup")
            backup
            ;;
        "update")
            update
            ;;
        "help")
            echo "用法: $0 [命令]"
            echo ""
            echo "命令:"
            echo "  deploy   - 完整部署（默认）"
            echo "  start    - 启动服务"
            echo "  stop     - 停止服务"
            echo "  restart  - 重启服务"
            echo "  status   - 查看状态"
            echo "  logs     - 查看日志"
            echo "  cleanup  - 清理资源"
            echo "  backup   - 备份数据"
            echo "  update   - 更新应用"
            echo "  help     - 显示帮助"
            ;;
        *)
            log_error "未知命令: $command"
            echo "使用 '$0 help' 查看可用命令"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
