"""
API响应模型定义
"""
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime

class PaginationInfo(BaseModel):
    """分页信息模型"""
    page: int = Field(..., description="当前页码")
    limit: int = Field(..., description="每页数据量")
    total: int = Field(..., description="总记录数")
    pages: int = Field(..., description="总页数")

class CacheInfo(BaseModel):
    """缓存信息模型"""
    last_update: Optional[str] = Field(None, description="最后更新时间")
    next_update: Optional[str] = Field(None, description="下次更新时间")

class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(True, description="请求是否成功")
    message: Optional[str] = Field(None, description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")

class ErrorResponse(BaseResponse):
    """错误响应模型"""
    success: bool = Field(False, description="请求失败")
    error: str = Field(..., description="错误信息")
    detail: Optional[str] = Field(None, description="错误详情")

class FilterDataResponse(BaseResponse):
    """日期筛选响应模型"""
    results: List[Dict[str, Any]] = Field(..., description="筛选结果")
    columns: Optional[List[str]] = Field(None, description="列名列表")
    total: int = Field(..., description="结果总数")

class CustomerOrdersResponse(BaseResponse):
    """客户订单响应模型"""
    results: List[Dict[str, Any]] = Field(..., description="订单结果")
    columns: Optional[List[str]] = Field(None, description="列名列表")
    total: int = Field(..., description="结果总数")

class OverdueOrdersResponse(BaseResponse):
    """逾期订单响应模型"""
    results: List[Dict[str, Any]] = Field(..., description="逾期订单结果")
    columns: Optional[List[str]] = Field(None, description="列名列表")
    pagination: PaginationInfo = Field(..., description="分页信息")
    cache_info: Optional[CacheInfo] = Field(None, description="缓存信息")

class CustomerSummaryResponse(BaseResponse):
    """客户汇总响应模型"""
    customer_info: Dict[str, Any] = Field(..., description="客户基本信息")
    summary_stats: Dict[str, Any] = Field(..., description="汇总统计")
    receivable_by_periods: List[Dict[str, Any]] = Field(..., description="按期数待收明细")
    order_details: List[Dict[str, Any]] = Field(..., description="订单详情")
    financial_flow: List[Dict[str, Any]] = Field(..., description="财务流水")

class SummaryDataResponse(BaseResponse):
    """数据汇总响应模型"""
    summary_stats: Dict[str, Any] = Field(..., description="汇总统计")
    period_data: List[Dict[str, Any]] = Field(..., description="期间数据")
    chart_data: Optional[Dict[str, Any]] = Field(None, description="图表数据")

class OrderSummaryResponse(BaseResponse):
    """订单汇总响应模型"""
    monthly_summary: List[Dict[str, Any]] = Field(..., description="月度汇总")
    total_stats: Dict[str, Any] = Field(..., description="总体统计")

class OverdueSummaryResponse(BaseResponse):
    """逾期订单汇总响应模型"""
    overdue_stats: Dict[str, Any] = Field(..., description="逾期统计")
    monthly_data: List[Dict[str, Any]] = Field(..., description="月度逾期数据")

class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    timestamp: float = Field(..., description="时间戳")
    version: str = Field(..., description="版本号")

class ApiStatusResponse(BaseModel):
    """API状态响应模型"""
    status: str = Field(..., description="API状态")
    message: str = Field(..., description="状态消息")
    external_api_status: Optional[Dict[str, Any]] = Field(None, description="外部API状态")
