/**
 * 仪表板适配器 - 将原Flask页面适配到FastAPI
 * 主要处理数据加载和显示逻辑的适配
 */
class DashboardAdapter {
    constructor() {
        this.currentUser = null;
        this.dataCache = new Map();
        this.loadingStates = new Set();
    }
    
    /**
     * 初始化仪表板
     */
    async initialize() {
        try {
            // 检查认证状态
            if (!authManager.isAuthenticated()) {
                window.location.href = '/login';
                return;
            }
            
            // 获取当前用户信息
            this.currentUser = await authManager.getCurrentUser();
            this.updateUserInterface();
            
            // 初始化页面组件
            this.initializeComponents();
            
            console.log('仪表板初始化完成');
        } catch (error) {
            console.error('仪表板初始化失败:', error);
            this.showError('页面初始化失败，请刷新重试');
        }
    }
    
    /**
     * 更新用户界面
     */
    updateUserInterface() {
        // 显示用户信息
        const userInfo = document.querySelector('.user-info');
        if (userInfo) {
            userInfo.textContent = `当前用户: ${this.currentUser.user_level} 权限`;
        }
        
        // 根据权限显示/隐藏功能
        this.updatePermissionBasedUI();
    }
    
    /**
     * 根据权限更新UI
     */
    updatePermissionBasedUI() {
        const permissions = this.currentUser.permissions;
        
        // 隐藏需要export权限的按钮
        if (!permissions.includes('export')) {
            document.querySelectorAll('.export-btn, .download-btn').forEach(btn => {
                btn.style.display = 'none';
            });
        }
        
        // 隐藏需要admin权限的功能
        if (!permissions.includes('admin')) {
            document.querySelectorAll('.admin-only').forEach(element => {
                element.style.display = 'none';
            });
        }
        
        // 隐藏需要standard权限的功能
        if (!authManager.hasUserLevel('standard')) {
            document.querySelectorAll('.standard-only').forEach(element => {
                element.style.display = 'none';
            });
        }
    }
    
    /**
     * 初始化页面组件
     */
    initializeComponents() {
        // 初始化日期选择器
        this.initializeDatePickers();
        
        // 初始化表格
        this.initializeTables();
        
        // 初始化搜索功能
        this.initializeSearch();
        
        // 初始化事件监听器
        this.initializeEventListeners();
    }
    
    /**
     * 初始化日期选择器
     */
    initializeDatePickers() {
        const dateInputs = document.querySelectorAll('input[type="date"]');
        dateInputs.forEach(input => {
            // 设置默认值为今天
            if (!input.value) {
                input.value = new Date().toISOString().split('T')[0];
            }
            
            // 添加变化事件监听器
            input.addEventListener('change', (e) => {
                this.handleDateChange(e.target);
            });
        });
    }
    
    /**
     * 初始化表格
     */
    initializeTables() {
        // 如果使用DataTables，需要重新初始化
        if (typeof $ !== 'undefined' && $.fn.DataTable) {
            $('.data-table').each((index, table) => {
                if ($.fn.DataTable.isDataTable(table)) {
                    $(table).DataTable().destroy();
                }
                
                $(table).DataTable({
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/zh.json'
                    },
                    responsive: true,
                    pageLength: 25,
                    order: [[0, 'desc']]
                });
            });
        }
    }
    
    /**
     * 初始化搜索功能
     */
    initializeSearch() {
        const searchInputs = document.querySelectorAll('.search-input');
        searchInputs.forEach(input => {
            // 添加防抖搜索
            let searchTimeout;
            input.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.handleSearch(e.target.value, e.target.dataset.searchType);
                }, 500);
            });
        });
    }
    
    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 筛选按钮
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleFilter(btn.dataset.filterType);
            });
        });
        
        // 刷新按钮
        document.querySelectorAll('.refresh-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleRefresh(btn.dataset.refreshType);
            });
        });
        
        // 导出按钮
        document.querySelectorAll('.export-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleExport(btn.dataset.exportType);
            });
        });
        
        // 登出按钮
        document.querySelectorAll('.logout-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleLogout();
            });
        });
    }
    
    /**
     * 处理日期变化
     * @param {HTMLElement} dateInput - 日期输入框
     */
    async handleDateChange(dateInput) {
        const date = dateInput.value;
        const filterType = dateInput.dataset.filterType || 'default';
        
        if (date) {
            await this.loadFilterData(date, filterType);
        }
    }
    
    /**
     * 处理搜索
     * @param {string} query - 搜索查询
     * @param {string} searchType - 搜索类型
     */
    async handleSearch(query, searchType) {
        if (!query.trim()) {
            return;
        }
        
        try {
            this.setLoadingState(searchType, true);
            
            switch (searchType) {
                case 'customer':
                    await this.loadCustomerData(query);
                    break;
                case 'overdue':
                    await this.loadOverdueData({ searchQuery: query });
                    break;
                default:
                    console.warn('未知的搜索类型:', searchType);
            }
        } catch (error) {
            console.error('搜索失败:', error);
            this.showError('搜索失败: ' + error.message);
        } finally {
            this.setLoadingState(searchType, false);
        }
    }
    
    /**
     * 处理筛选
     * @param {string} filterType - 筛选类型
     */
    async handleFilter(filterType) {
        try {
            this.setLoadingState(filterType, true);
            
            switch (filterType) {
                case 'date':
                    const dateInput = document.querySelector('input[name="filter_date"]');
                    if (dateInput && dateInput.value) {
                        await this.loadFilterData(dateInput.value);
                    }
                    break;
                case 'overdue':
                    await this.loadOverdueData();
                    break;
                default:
                    console.warn('未知的筛选类型:', filterType);
            }
        } catch (error) {
            console.error('筛选失败:', error);
            this.showError('筛选失败: ' + error.message);
        } finally {
            this.setLoadingState(filterType, false);
        }
    }
    
    /**
     * 处理刷新
     * @param {string} refreshType - 刷新类型
     */
    async handleRefresh(refreshType) {
        try {
            this.setLoadingState(refreshType, true);
            
            // 清除缓存
            this.dataCache.clear();
            
            switch (refreshType) {
                case 'overdue':
                    await this.loadOverdueData({ forceRefresh: true });
                    break;
                case 'all':
                    await this.refreshAllData();
                    break;
                default:
                    window.location.reload();
            }
        } catch (error) {
            console.error('刷新失败:', error);
            this.showError('刷新失败: ' + error.message);
        } finally {
            this.setLoadingState(refreshType, false);
        }
    }
    
    /**
     * 处理导出
     * @param {string} exportType - 导出类型
     */
    async handleExport(exportType) {
        if (!authManager.hasPermission('export')) {
            this.showError('您没有导出权限');
            return;
        }
        
        try {
            this.setLoadingState(exportType, true);
            
            // 这里可以实现导出逻辑
            this.showSuccess('导出功能开发中...');
        } catch (error) {
            console.error('导出失败:', error);
            this.showError('导出失败: ' + error.message);
        } finally {
            this.setLoadingState(exportType, false);
        }
    }
    
    /**
     * 处理登出
     */
    async handleLogout() {
        if (confirm('确定要退出登录吗？')) {
            await authManager.logout();
            window.location.href = '/login';
        }
    }
    
    /**
     * 加载筛选数据
     * @param {string} date - 日期
     * @param {string} filterType - 筛选类型
     */
    async loadFilterData(date, filterType = 'default') {
        const cacheKey = `filter_${date}_${filterType}`;
        
        // 检查缓存
        if (this.dataCache.has(cacheKey)) {
            this.displayFilterData(this.dataCache.get(cacheKey));
            return;
        }
        
        try {
            const data = await apiClient.filterData(date);
            
            // 缓存数据
            this.dataCache.set(cacheKey, data);
            
            // 显示数据
            this.displayFilterData(data);
            
            console.log(`加载筛选数据成功: ${data.total} 条记录`);
        } catch (error) {
            console.error('加载筛选数据失败:', error);
            throw error;
        }
    }
    
    /**
     * 加载客户数据
     * @param {string} customerName - 客户名称
     */
    async loadCustomerData(customerName) {
        const cacheKey = `customer_${customerName}`;
        
        if (this.dataCache.has(cacheKey)) {
            this.displayCustomerData(this.dataCache.get(cacheKey));
            return;
        }
        
        try {
            const data = await apiClient.getCustomerOrders(customerName);
            
            this.dataCache.set(cacheKey, data);
            this.displayCustomerData(data);
            
            console.log(`加载客户数据成功: ${data.total} 条记录`);
        } catch (error) {
            console.error('加载客户数据失败:', error);
            throw error;
        }
    }
    
    /**
     * 加载逾期数据
     * @param {Object} params - 查询参数
     */
    async loadOverdueData(params = {}) {
        try {
            const data = await apiClient.getOverdueOrders(params);
            this.displayOverdueData(data);
            
            console.log(`加载逾期数据成功: ${data.pagination.total} 条记录`);
        } catch (error) {
            console.error('加载逾期数据失败:', error);
            throw error;
        }
    }
    
    /**
     * 显示筛选数据
     * @param {Object} data - 数据
     */
    displayFilterData(data) {
        const container = document.querySelector('#filter-results');
        if (!container) return;
        
        // 更新表格数据
        this.updateTable(container, data.results, data.columns);
        
        // 更新统计信息
        this.updateStats('filter', { total: data.total });
    }
    
    /**
     * 显示客户数据
     * @param {Object} data - 数据
     */
    displayCustomerData(data) {
        const container = document.querySelector('#customer-results');
        if (!container) return;
        
        this.updateTable(container, data.results, data.columns);
        this.updateStats('customer', { total: data.total });
    }
    
    /**
     * 显示逾期数据
     * @param {Object} data - 数据
     */
    displayOverdueData(data) {
        const container = document.querySelector('#overdue-results');
        if (!container) return;
        
        this.updateTable(container, data.results, data.columns);
        this.updatePagination(data.pagination);
        this.updateStats('overdue', data.pagination);
    }
    
    /**
     * 更新表格
     * @param {HTMLElement} container - 容器元素
     * @param {Array} results - 结果数据
     * @param {Array} columns - 列名
     */
    updateTable(container, results, columns) {
        // 这里实现表格更新逻辑
        // 可以使用DataTables或其他表格组件
        console.log('更新表格:', results.length, '条记录');
    }
    
    /**
     * 更新分页
     * @param {Object} pagination - 分页信息
     */
    updatePagination(pagination) {
        const paginationContainer = document.querySelector('.pagination-container');
        if (!paginationContainer) return;
        
        // 实现分页更新逻辑
        console.log('更新分页:', pagination);
    }
    
    /**
     * 更新统计信息
     * @param {string} type - 类型
     * @param {Object} stats - 统计数据
     */
    updateStats(type, stats) {
        const statsContainer = document.querySelector(`#${type}-stats`);
        if (!statsContainer) return;
        
        statsContainer.textContent = `共 ${stats.total} 条记录`;
    }
    
    /**
     * 设置加载状态
     * @param {string} type - 类型
     * @param {boolean} loading - 是否加载中
     */
    setLoadingState(type, loading) {
        if (loading) {
            this.loadingStates.add(type);
        } else {
            this.loadingStates.delete(type);
        }
        
        // 更新UI加载状态
        const loadingIndicator = document.querySelector(`#${type}-loading`);
        if (loadingIndicator) {
            loadingIndicator.style.display = loading ? 'block' : 'none';
        }
        
        // 禁用/启用相关按钮
        const buttons = document.querySelectorAll(`[data-filter-type="${type}"], [data-refresh-type="${type}"]`);
        buttons.forEach(btn => {
            btn.disabled = loading;
        });
    }
    
    /**
     * 显示错误消息
     * @param {string} message - 错误消息
     */
    showError(message) {
        console.error(message);
        // 可以集成通知组件
        if (typeof showNotification === 'function') {
            showNotification(message, 'error');
        } else {
            alert('错误: ' + message);
        }
    }
    
    /**
     * 显示成功消息
     * @param {string} message - 成功消息
     */
    showSuccess(message) {
        console.log(message);
        if (typeof showNotification === 'function') {
            showNotification(message, 'success');
        }
    }
    
    /**
     * 刷新所有数据
     */
    async refreshAllData() {
        // 实现全量数据刷新
        console.log('刷新所有数据...');
    }
}

// 创建全局实例
const dashboardAdapter = new DashboardAdapter();

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', () => {
    dashboardAdapter.initialize();
});
