"""
统计相关API端点
"""
from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional
import logging

from app.core.dependencies import (
    get_current_active_user, 
    require_read_permission,
    require_standard_user
)
from app.core.security import User
from app.models.request import (
    CustomerSummaryRequest,
    SummaryDataRequest,
    OrderSummaryRequest,
    OverdueSummaryRequest
)
from app.models.response import (
    CustomerSummaryResponse,
    SummaryDataResponse,
    OrderSummaryResponse,
    OverdueSummaryResponse,
    ErrorResponse
)
from app.services.data_service import data_service

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/customer_summary", response_model=CustomerSummaryResponse)
async def customer_summary_api(
    customer_name: str = Query(..., description="客户姓名"),
    current_user: User = Depends(require_standard_user)
):
    """
    获取客户订单汇总数据的API
    
    Args:
        customer_name: 客户姓名
        current_user: 当前用户（需要标准权限）
        
    Returns:
        客户汇总数据
    """
    try:
        logger.info(f"用户 {current_user.username} 请求客户汇总数据，客户: {customer_name}")
        
        # 调用数据服务
        summary_data = await data_service.get_customer_summary(customer_name)
        
        # 检查错误
        if "error" in summary_data:
            raise HTTPException(status_code=400, detail=summary_data["error"])
        
        return CustomerSummaryResponse(
            customer_info=summary_data.get("customer_info", {}),
            summary_stats=summary_data.get("summary_stats", {}),
            receivable_by_periods=summary_data.get("receivable_by_periods", []),
            order_details=summary_data.get("order_details", []),
            financial_flow=summary_data.get("financial_flow", [])
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取客户汇总数据时发生异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get("/summary_data", response_model=SummaryDataResponse)
async def summary_data_api(
    start_date: str = Query(..., description="开始日期，格式：YYYY-MM-DD"),
    end_date: str = Query(..., description="结束日期，格式：YYYY-MM-DD"),
    current_user: User = Depends(require_read_permission)
):
    """
    获取数据汇总的API
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        current_user: 当前用户
        
    Returns:
        数据汇总结果
    """
    try:
        logger.info(f"用户 {current_user.username} 请求数据汇总，时间段: {start_date} - {end_date}")
        
        # 调用数据服务
        summary_data = await data_service.get_summary_data(start_date, end_date)
        
        # 检查错误
        if "error" in summary_data:
            raise HTTPException(status_code=400, detail=summary_data["error"])
        
        return SummaryDataResponse(
            summary_stats=summary_data.get("summary_stats", {}),
            period_data=summary_data.get("period_data", []),
            chart_data=summary_data.get("chart_data")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取数据汇总时发生异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get("/order_summary", response_model=OrderSummaryResponse)
async def order_summary_api(
    end_date: str = Query(..., description="结束日期，格式：YYYY-MM-DD"),
    current_user: User = Depends(require_read_permission)
):
    """
    获取订单汇总数据的API
    
    Args:
        end_date: 结束日期
        current_user: 当前用户
        
    Returns:
        订单汇总数据
    """
    try:
        logger.info(f"用户 {current_user.username} 请求订单汇总数据，结束日期: {end_date}")
        
        # 调用数据服务
        summary_data = await data_service.get_order_summary(end_date)
        
        # 检查错误
        if "error" in summary_data:
            raise HTTPException(status_code=400, detail=summary_data["error"])
        
        return OrderSummaryResponse(
            monthly_summary=summary_data.get("monthly_summary", []),
            total_stats=summary_data.get("total_stats", {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取订单汇总数据时发生异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

# 兼容性路由 - 保持与原Flask API相同的路径
@router.get("/summary", response_model=SummaryDataResponse)
async def summary_data_compat(
    start_date: str = Query(..., description="开始日期，格式：YYYY-MM-DD"),
    end_date: str = Query(..., description="结束日期，格式：YYYY-MM-DD"),
    current_user: User = Depends(require_read_permission)
):
    """兼容性路由 - /api/summary"""
    return await summary_data_api(start_date, end_date, current_user)

# API代理端点 - 用于转发到外部API
@router.get("/proxy/{endpoint:path}")
async def api_proxy(
    endpoint: str,
    current_user: User = Depends(require_read_permission)
):
    """
    API代理端点 - 转发请求到外部API服务器
    
    Args:
        endpoint: API端点路径
        current_user: 当前用户
        
    Returns:
        外部API响应
    """
    try:
        logger.info(f"用户 {current_user.username} 请求代理API: {endpoint}")
        
        # 这里可以实现API代理逻辑
        # 暂时返回未实现错误
        raise HTTPException(status_code=501, detail="API代理功能暂未实现")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"API代理时发生异常: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

# 健康检查和状态端点
@router.get("/ping")
async def ping():
    """简单的ping端点，用于检测API状态"""
    return {
        "status": "ok",
        "message": "统计API服务正常运行",
        "timestamp": "2025-01-15T00:00:00Z"
    }

@router.get("/status")
async def get_api_status():
    """获取API状态"""
    try:
        # 检查外部API状态
        from app.services.external_api import api_client
        external_status = await api_client.check_status()
        
        return {
            "status": "ok",
            "message": "API服务正常",
            "external_api": external_status
        }
    except Exception as e:
        logger.error(f"检查API状态时发生异常: {str(e)}")
        return {
            "status": "error",
            "message": f"API状态检查失败: {str(e)}"
        }
