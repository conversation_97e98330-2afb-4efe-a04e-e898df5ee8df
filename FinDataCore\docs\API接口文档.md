# FinDataCore API接口文档

本文档详细说明了FinDataCore系统中所有API端点的使用方法，包括认证、请求参数、响应格式和测试示例。

## 🔗 基础信息

- **服务地址**: `http://localhost:8000`
- **API版本**: v1
- **认证方式**: JWT Bearer <PERSON>ken
- **文档地址**: `http://localhost:8000/docs`
- **ReDoc文档**: `http://localhost:8000/redoc`

## 🔐 认证系统

### 用户登录
- **接口路径**: `/api/v1/auth/login`
- **请求方法**: POST
- **Content-Type**: application/json
- **功能描述**: 用户登录获取JWT Token
- **请求参数**:
  ```json
  {
    "password": "用户密码"
  }
  ```
- **权限级别**:
  - `TT2024` - 有限权限 (read)
  - `881017` - 标准权限 (read, export)  
  - `Doolin` - 完全权限 (read, export, admin)

**请求示例**:
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"password": "TT2024"}'
```

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 28800,
  "user_level": "limited",
  "permissions": ["read"]
}
```

### 获取当前用户信息
- **接口路径**: `/api/v1/auth/me`
- **请求方法**: GET
- **认证要求**: 需要Bearer Token
- **功能描述**: 获取当前登录用户的详细信息

**请求示例**:
```bash
curl -X GET "http://localhost:8000/api/v1/auth/me" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 用户登出
- **接口路径**: `/api/v1/auth/logout`
- **请求方法**: POST
- **功能描述**: 用户登出（客户端需删除Token）

### 获取用户权限
- **接口路径**: `/api/v1/auth/permissions`
- **请求方法**: GET
- **认证要求**: 需要Bearer Token
- **功能描述**: 获取当前用户的权限列表

## 📊 查询接口

### 1. 日期筛选数据
- **接口路径**: `/api/v1/query/filter_data`
- **请求方法**: GET
- **认证要求**: 需要Bearer Token (read权限)
- **功能描述**: 根据指定日期筛选订单数据
- **请求参数**:
  - `date` (必需): 筛选日期，格式YYYY-MM-DD

**请求示例**:
```bash
curl -X GET "http://localhost:8000/api/v1/query/filter_data?date=2025-01-15" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

**响应示例**:
```json
{
  "success": true,
  "results": [
    {
      "id": 1,
      "customer_name": "张三",
      "order_date": "2025-01-15",
      "amount": 10000,
      "status": "正常"
    }
  ],
  "columns": ["id", "customer_name", "order_date", "amount", "status"],
  "total": 1,
  "timestamp": "2025-01-15T10:00:00Z"
}
```

### 2. 客户订单查询
- **接口路径**: `/api/v1/query/filter_orders_by_customer_name`
- **请求方法**: GET
- **认证要求**: 需要Bearer Token (read权限)
- **功能描述**: 根据客户姓名查询该客户的所有订单
- **请求参数**:
  - `customer_name` (必需): 客户姓名

**请求示例**:
```bash
curl -X GET "http://localhost:8000/api/v1/query/filter_orders_by_customer_name?customer_name=张三" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 3. 逾期订单查询
- **接口路径**: `/api/v1/query/overdue`
- **请求方法**: GET
- **认证要求**: 需要Bearer Token (read权限)
- **功能描述**: 查询逾期订单，支持分页和搜索
- **请求参数**:
  - `page` (可选): 页码，默认1
  - `limit` (可选): 每页数量，默认10，最大100
  - `force_refresh` (可选): 是否强制刷新缓存，默认false
  - `search_query` (可选): 搜索关键词

**请求示例**:
```bash
curl -X GET "http://localhost:8000/api/v1/query/overdue?page=1&limit=10&search_query=张三" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

**响应示例**:
```json
{
  "success": true,
  "results": [
    {
      "order_id": "ORD001",
      "customer_name": "张三",
      "overdue_days": 15,
      "amount": 5000
    }
  ],
  "columns": ["order_id", "customer_name", "overdue_days", "amount"],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  },
  "cache_info": {
    "last_update": "2025-01-15T10:00:00Z",
    "next_update": null
  },
  "timestamp": "2025-01-15T10:00:00Z"
}
```

## 📈 统计接口

### 4. 客户汇总数据
- **接口路径**: `/api/v1/stats/customer_summary`
- **请求方法**: GET
- **认证要求**: 需要Bearer Token (standard权限或以上)
- **功能描述**: 获取指定客户的订单汇总统计信息
- **请求参数**:
  - `customer_name` (必需): 客户姓名

**请求示例**:
```bash
curl -X GET "http://localhost:8000/api/v1/stats/customer_summary?customer_name=张三" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

**响应示例**:
```json
{
  "success": true,
  "customer_info": {
    "name": "张三",
    "phone": "13800138000",
    "total_orders": 5
  },
  "summary_stats": {
    "total_amount": 50000,
    "paid_amount": 30000,
    "outstanding_amount": 20000,
    "overdue_amount": 5000
  },
  "receivable_by_periods": [
    {
      "period": 1,
      "amount": 10000,
      "due_date": "2025-02-15"
    }
  ],
  "order_details": [...],
  "financial_flow": [...],
  "timestamp": "2025-01-15T10:00:00Z"
}
```

### 5. 数据汇总统计
- **接口路径**: `/api/v1/stats/summary_data`
- **请求方法**: GET
- **认证要求**: 需要Bearer Token (read权限)
- **功能描述**: 获取指定时间段的数据汇总统计
- **请求参数**:
  - `start_date` (必需): 开始日期，格式YYYY-MM-DD
  - `end_date` (必需): 结束日期，格式YYYY-MM-DD

**请求示例**:
```bash
curl -X GET "http://localhost:8000/api/v1/stats/summary_data?start_date=2025-01-01&end_date=2025-01-31" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 6. 订单汇总统计
- **接口路径**: `/api/v1/stats/order_summary`
- **请求方法**: GET
- **认证要求**: 需要Bearer Token (read权限)
- **功能描述**: 获取截止到指定日期的订单汇总统计
- **请求参数**:
  - `end_date` (必需): 结束日期，格式YYYY-MM-DD

**请求示例**:
```bash
curl -X GET "http://localhost:8000/api/v1/stats/order_summary?end_date=2025-01-31" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 🔄 兼容性接口

为了保持与原Flask版本的兼容性，提供以下别名接口：

### 兼容性查询接口
- `/api/v1/query/filter` → `/api/v1/query/filter_data`
- `/api/v1/query/customer` → `/api/v1/query/filter_orders_by_customer_name`

### 兼容性统计接口
- `/api/v1/stats/summary` → `/api/v1/stats/summary_data`

## 🛠️ 工具接口

### 健康检查
- **接口路径**: `/health`
- **请求方法**: GET
- **认证要求**: 无
- **功能描述**: 检查服务健康状态

**请求示例**:
```bash
curl -X GET "http://localhost:8000/health"
```

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": **********.0
}
```

### API状态检查
- **接口路径**: `/api/v1/stats/status`
- **请求方法**: GET
- **认证要求**: 无
- **功能描述**: 检查API服务和外部依赖状态

**响应示例**:
```json
{
  "status": "ok",
  "message": "API服务正常",
  "external_api": {
    "status": "ok",
    "external_api": {...}
  }
}
```

### Ping测试
- **接口路径**: `/api/v1/stats/ping`
- **请求方法**: GET
- **认证要求**: 无
- **功能描述**: 简单的ping测试

**响应示例**:
```json
{
  "status": "ok",
  "message": "统计API服务正常运行",
  "timestamp": "2025-01-15T00:00:00Z"
}
```

## ❌ 错误处理

### HTTP状态码说明
- `200` - 请求成功
- `400` - 请求参数错误
- `401` - 未认证或Token无效
- `403` - 权限不足
- `404` - 资源不存在
- `422` - 请求参数验证失败
- `500` - 服务器内部错误

### 错误响应格式
```json
{
  "detail": "错误描述信息"
}
```

### 常见错误示例

#### 认证失败 (401)
```json
{
  "detail": "Could not validate credentials"
}
```

#### 权限不足 (403)
```json
{
  "detail": "Insufficient permissions"
}
```

#### 参数验证失败 (422)
```json
{
  "detail": [
    {
      "loc": ["query", "date"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

## 🧪 测试工具

### 使用内置测试脚本
```bash
cd FinDataCore
python test_api.py
```

### 使用Postman测试
1. 导入API文档：访问 `http://localhost:8000/docs`
2. 点击右上角的"Download OpenAPI spec"
3. 在Postman中导入下载的JSON文件

### 使用curl测试完整流程
```bash
# 1. 登录获取Token
TOKEN=$(curl -s -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"password": "TT2024"}' | jq -r '.access_token')

echo "获取到Token: $TOKEN"

# 2. 使用Token查询数据
echo "测试日期筛选..."
curl -X GET "http://localhost:8000/api/v1/query/filter_data?date=2025-01-15" \
  -H "Authorization: Bearer $TOKEN"

# 3. 查询逾期订单
echo "测试逾期订单查询..."
curl -X GET "http://localhost:8000/api/v1/query/overdue?page=1&limit=5" \
  -H "Authorization: Bearer $TOKEN"

# 4. 客户查询
echo "测试客户查询..."
curl -X GET "http://localhost:8000/api/v1/query/filter_orders_by_customer_name?customer_name=张三" \
  -H "Authorization: Bearer $TOKEN"
```

### JavaScript测试示例
```javascript
// 登录获取Token
async function login() {
    const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            password: 'TT2024'
        })
    });

    const data = await response.json();
    return data.access_token;
}

// 使用Token查询数据
async function queryData(token) {
    const response = await fetch('/api/v1/query/filter_data?date=2025-01-15', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });

    return await response.json();
}

// 完整测试流程
async function testAPI() {
    try {
        const token = await login();
        console.log('登录成功，Token:', token);

        const data = await queryData(token);
        console.log('查询结果:', data);
    } catch (error) {
        console.error('测试失败:', error);
    }
}
```

## 📝 注意事项

1. **认证要求**: 除了健康检查和登录接口外，所有API都需要有效的JWT Token
2. **Token过期**: Token默认有效期为8小时，过期后需要重新登录
3. **权限控制**: 不同接口需要不同的权限级别，请确保使用正确的用户账号
4. **日期格式**: 所有日期参数必须使用YYYY-MM-DD格式
5. **分页限制**: 分页查询的limit参数最大值为100
6. **缓存机制**: 系统使用智能缓存，可通过force_refresh参数强制刷新
7. **并发限制**: 系统有请求频率限制，避免过于频繁的API调用
8. **字符编码**: 所有请求和响应都使用UTF-8编码
9. **时区**: 所有时间戳都使用UTC时区

## 📊 API端点总览表

| 分类 | 端点 | 方法 | 权限要求 | 功能描述 |
|------|------|------|----------|----------|
| **认证** | `/api/v1/auth/login` | POST | 无 | 用户登录 |
| | `/api/v1/auth/me` | GET | read | 获取用户信息 |
| | `/api/v1/auth/logout` | POST | read | 用户登出 |
| | `/api/v1/auth/permissions` | GET | read | 获取权限列表 |
| **查询** | `/api/v1/query/filter_data` | GET | read | 日期筛选 |
| | `/api/v1/query/filter_orders_by_customer_name` | GET | read | 客户订单查询 |
| | `/api/v1/query/overdue` | GET | read | 逾期订单查询 |
| **统计** | `/api/v1/stats/customer_summary` | GET | standard | 客户汇总 |
| | `/api/v1/stats/summary_data` | GET | read | 数据汇总 |
| | `/api/v1/stats/order_summary` | GET | read | 订单汇总 |
| **工具** | `/health` | GET | 无 | 健康检查 |
| | `/api/v1/stats/status` | GET | 无 | API状态 |
| | `/api/v1/stats/ping` | GET | 无 | Ping测试 |

## 🔗 相关链接

- **API交互文档**: http://localhost:8000/docs
- **ReDoc文档**: http://localhost:8000/redoc
- **健康检查**: http://localhost:8000/health
- **项目源码**: FinDataCore目录
- **技术支持**: 联系系统管理员

## 📞 技术支持

如果在使用API过程中遇到问题，请：

1. **检查认证**: 确保Token有效且权限足够
2. **验证参数**: 检查请求参数格式和必需字段
3. **查看日志**: 检查服务器日志获取详细错误信息
4. **测试连接**: 使用健康检查接口验证服务状态
5. **联系支持**: 如问题持续存在，请联系技术支持团队

---

**文档版本**: v1.0.0
**最后更新**: 2025年1月15日
**维护团队**: FinDataCore开发组
```
