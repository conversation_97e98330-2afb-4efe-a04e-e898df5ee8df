/**
 * 样式控制器
 * 统一管理应用样式，特别是表格状态样式
 */
const StyleController = {
    // 状态样式映射 - 统一定义不同状态的样式
    statusStyles: {
        '账单日': {
            bgClass: 'bg-warning',
            textClass: 'text-dark',
            borderClass: 'border-warning',
            bgColor: '#ffc107'
        },
    
    // 客服人员颜色缓存
    servicePersonColors: {},
    
    // 预定义的颜色方案 - 专业且易于区分的颜色
    colorSchemes: [
        { bg: '#e3f2fd', text: '#0d47a1', border: '#1976d2' }, // 蓝色系
        { bg: '#f3e5f5', text: '#4a148c', border: '#7b1fa2' }, // 紫色系
        { bg: '#e8f5e8', text: '#1b5e20', border: '#388e3c' }, // 绿色系
        { bg: '#fff3e0', text: '#e65100', border: '#f57c00' }, // 橙色系
        { bg: '#fce4ec', text: '#880e4f', border: '#c2185b' }, // 粉色系
        { bg: '#e0f2f1', text: '#004d40', border: '#00695c' }, // 青色系
        { bg: '#f1f8e9', text: '#33691e', border: '#689f38' }, // 浅绿色系
        { bg: '#fff8e1', text: '#f57f17', border: '#fbc02d' }, // 黄色系
        { bg: '#efebe9', text: '#3e2723', border: '#5d4037' }, // 棕色系
        { bg: '#e8eaf6', text: '#1a237e', border: '#3f51b5' }, // 靛蓝色系
        { bg: '#fafafa', text: '#212121', border: '#424242' }, // 灰色系
        { bg: '#e1f5fe', text: '#01579b', border: '#0288d1' }  // 浅蓝色系
    ],
    
    // 为客服人员生成唯一颜色
    getServicePersonColor: function(serviceName) {
        if (!serviceName || serviceName === '-') {
            return { bg: '#f8f9fa', text: '#6c757d', border: '#dee2e6' };
        }
        
        // 如果已经为该客服分配了颜色，直接返回
        if (this.servicePersonColors[serviceName]) {
            return this.servicePersonColors[serviceName];
        }
        
        // 使用字符串哈希算法为客服名字生成一个稳定的索引
        let hash = 0;
        for (let i = 0; i < serviceName.length; i++) {
            const char = serviceName.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        
        // 确保索引为正数，并映射到颜色方案数组
        const colorIndex = Math.abs(hash) % this.colorSchemes.length;
        const selectedColor = this.colorSchemes[colorIndex];
        
        // 缓存颜色方案
        this.servicePersonColors[serviceName] = selectedColor;
        
        return selectedColor;
        '逾期未还': {
            bgClass: 'bg-danger',
            textClass: 'text-white',
            borderClass: 'border-danger',
            bgColor: '#dc3545'
        },
        '严重逾期': {
            bgClass: 'bg-danger',
            textClass: 'text-white',
            borderClass: 'border-danger',
            bgColor: '#dc3545'
        },
        '逾期还款': {
            bgClass: 'bg-warning',
            textClass: 'text-dark',
            borderClass: 'border-warning',
            bgColor: '#ffc107'
        },
        '轻微逾期': {
            bgClass: 'bg-warning',
            textClass: 'text-dark',
            borderClass: 'border-warning',
            bgColor: '#ffc107'
        },
        '催收中': {
            bgClass: 'bg-warning',
            textClass: 'text-dark',
            borderClass: 'border-warning',
            bgColor: '#ffc107'
        },
        '提前还款': {
            bgClass: 'bg-primary',
            textClass: 'text-white',
            borderClass: 'border-primary',
            bgColor: '#0d6efd'
        },
        '按时还款': {
            bgClass: 'bg-success',
            textClass: 'text-white',
            borderClass: 'border-success',
            bgColor: '#198754'
        },
        '已结清': {
            bgClass: 'bg-success',
            textClass: 'text-white',
            borderClass: 'border-success',
            bgColor: '#198754'
        },
        '正常': {
            bgClass: 'bg-success',
            textClass: 'text-white',
            borderClass: 'border-success',
            bgColor: '#198754'
        },
        '已取消': {
            bgClass: 'bg-secondary',
            textClass: 'text-white',
            borderClass: 'border-secondary',
            bgColor: '#6c757d'
        },
        '默认': {
            bgClass: 'bg-light',
            textClass: 'text-dark',
            borderClass: 'border-secondary',
            bgColor: '#f8f9fa'
        }
    },
    
    // 表格字段类型映射
    fieldTypes: {
        // 日期字段标识
        dateFields: ['日期', '账单日期', '发放日期', '支付日期', '订单日期'],
        
        // 货币字段标识
        moneyFields: ['金额', '待收', '待收金额', '费用', '总额', '融资总额', '每期还款金额', '已还金额', '当前待收'],
        
        // 状态字段映射
        statusFields: {
            'filter': ['账单状态', '备注'], 
            'overdue': ['贷后状态'],
            'customer': ['贷后状态', '账单状态']
        },
        
        // 标识字段映射
        identifierFields: {
            'filter': ['订单编号', '客户姓名'],
            'overdue': ['订单编号', '客户姓名'],
            'customer': ['订单编号', '客户姓名']
        }
    },
    
    // 初始化
    init: function() {
        console.log('初始化样式控制器');
        this.injectCustomStyles();
    },
    
    // 注入自定义样式到页面
    injectCustomStyles: function() {
        const customStyles = `
            /* 表格通用样式 */
            .data-table thead th {
                text-align: center;
                vertical-align: middle;
                white-space: nowrap;
                background-color: #f8f9fa;
            }
            
            .data-table tbody td {
                vertical-align: middle;
            }
            
            /* 状态样式 */
            .status-badge {
                padding: 0.25rem 0.5rem;
                border-radius: 0.25rem;
                display: inline-block;
                font-size: 0.875rem;
                font-weight: 500;
                white-space: nowrap;
            }
            
            /* 长文本内容样式 */
            .remarks-content {
                max-width: 300px;
                max-height: 60px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: normal;
                position: relative;
                cursor: pointer;
                transition: max-height 0.3s ease;
                line-height: 1.5;
            }
            
            .remarks-content.expanded {
                max-height: 500px;
                max-width: 100%;
                z-index: 100;
                background-color: #fff;
                border: 1px solid #dee2e6;
                border-radius: 0.25rem;
                padding: 0.5rem;
                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            }
            
            /* 日期样式 */
            .date-badge {
                border-radius: 0.25rem;
                display: inline-block;
                font-size: 0.875rem;
                font-weight: 500;
                padding: 0.25rem 0.5rem;
            }
            
            /* 特定状态行样式 */
            tr[data-status="逾期未还"], tr[data-status="严重逾期"] {
                background-color: rgba(220, 53, 69, 0.1) !important;
            }
            
            tr[data-status="逾期还款"], tr[data-status="轻微逾期"], tr[data-status="催收中"] {
                background-color: rgba(255, 193, 7, 0.1) !important;
            }
            
            tr[data-status="按时还款"], tr[data-status="已结清"], tr[data-status="正常"] {
                background-color: rgba(25, 135, 84, 0.05) !important;
            }
            
            tr[data-status="提前还款"] {
                background-color: rgba(13, 110, 253, 0.05) !important;
            }
            
            /* 响应式调整 */
            @media (max-width: 768px) {
                .remarks-content {
                    max-width: 200px;
                }
                
                .date-badge, .status-badge {
                    font-size: 0.75rem;
                    padding: 0.2rem 0.4rem;
                }
            }
        `;
        
        // 创建样式元素并添加到页面
        const styleElement = document.createElement('style');
        styleElement.textContent = customStyles;
        document.head.appendChild(styleElement);
    },
    
    // 应用状态样式到元素
    applyStatusStyle: function(element, status) {
        if (!element || !status) return;
        
        const style = this.statusStyles[status] || this.statusStyles['默认'];
        element.classList.add(style.bgClass, style.textClass);
        
        return element;
    },
    
    // 创建状态徽章HTML
    createStatusBadge: function(content, status) {
        if (!content) return '-';
        
        const style = this.statusStyles[status] || this.statusStyles['默认'];
        return `<span class="status-badge ${style.bgClass} ${style.textClass}">${content}</span>`;
    },
    
    // 创建日期徽章HTML
    createDateBadge: function(dateText, status) {
        if (!dateText || dateText === '-') return '-';
        
        const style = this.statusStyles[status] || this.statusStyles['默认'];
        return `<span class="date-badge ${style.bgClass} ${style.textClass}">${dateText}</span>`;
    },
    
    // 应用样式增强到表格
    enhanceTable: function(tableElement, tableType) {
        if (!tableElement) return;
        
        // 处理表格行和单元格
        const rows = tableElement.querySelectorAll('tbody tr');
        
        // 获取列信息
        const headers = Array.from(tableElement.querySelectorAll('thead th')).map(th => th.textContent.trim());
        
        rows.forEach(row => {
            // 获取行状态
            const status = row.getAttribute('data-status');
            
            // 处理所有单元格
            Array.from(row.cells).forEach((cell, index) => {
                // 跳过第一个单元格（响应式控制列）
                if (index === 0) return;
                
                // 获取列名和单元格内容
                const columnName = headers[index] || '';
                const cellContent = cell.textContent.trim();
                
                // 如果内容为空，不处理
                if (!cellContent || cellContent === '-') return;
                
                // 根据不同类型的列应用不同样式
                this.enhanceCell(cell, columnName, cellContent, status, tableType);
            });
        });
    },
    
    // 增强单个单元格的样式
    enhanceCell: function(cell, columnName, content, rowStatus, tableType) {
        // 1. 处理状态类字段
        if (this.fieldTypes.statusFields[tableType]?.includes(columnName)) {
            cell.innerHTML = this.createStatusBadge(content, content);
            return;
        }
        
        // 2. 处理日期类字段
        if (this.fieldTypes.dateFields.some(field => columnName.includes(field)) && this.isDateFormat(content)) {
            cell.innerHTML = this.createDateBadge(content, rowStatus);
            return;
        }
        
        // 3. 检查是否为期数N列（客户表格特殊处理）
        if (/^期数\d+$/.test(columnName)) {
            // 提取期数中的状态信息 - 格式如 "2024-05-12（按时还款）"
            const statusMatch = content.match(/（(.+)）/);
            if (statusMatch) {
                const dateContent = content.replace(/（.+）/, '').trim();
                const periodStatus = statusMatch[1];
                
                // 使用提取的状态显示日期
                cell.innerHTML = this.createDateBadge(dateContent, periodStatus);
                
                // 添加状态提示
                const statusIndicator = document.createElement('span');
                statusIndicator.className = 'ms-1 badge bg-secondary';
                statusIndicator.textContent = periodStatus;
                cell.appendChild(statusIndicator);
                return;
            } else if (this.isDateFormat(content)) {
                // 如果只有日期没有状态
                cell.innerHTML = this.createDateBadge(content, rowStatus);
                return;
            }
        }
        
        // 4. 处理金额类字段
        if (this.fieldTypes.moneyFields.some(field => columnName.includes(field))) {
            // 尝试将内容解析为数字
            const numValue = parseFloat(content);
            if (!isNaN(numValue)) {
                // 格式化数字显示
                const formattedValue = numValue.toFixed(2);
                
                // 根据金额正负值设置不同样式
                if (numValue > 0) {
                    cell.innerHTML = `<span class="badge bg-success">${formattedValue}</span>`;
                } else if (numValue < 0) {
                    cell.innerHTML = `<span class="badge bg-danger">${formattedValue}</span>`;
                } else {
                    cell.innerHTML = `<span class="badge bg-light text-dark">0.00</span>`;
                }
                return;
            }
        }
        
        // 5. 处理备注类长文本字段
        if (columnName === '备注' || columnName === '客户信息备注' || columnName === '备注信息' || 
            columnName.includes('备注') || columnName.includes('备注信息')) {
            
            // 如果内容足够长，使用可折叠的备注内容显示
            if (content.length > 15) {
                cell.innerHTML = `<div class="remarks-content">${content}</div>`;
                
                // 添加点击事件
                const remarksContent = cell.querySelector('.remarks-content');
                if (remarksContent) {
                    remarksContent.addEventListener('click', function() {
                        this.classList.toggle('expanded');
                    });
                }
            }
            return;
        }
        
        // 6. 处理业务相关字段
        if (columnName === '业务归属' || columnName === '业务') {
            cell.innerHTML = `<span class="badge bg-info">${content}</span>`;
            return;
        }
        
        // 7. 处理客服相关字段
        if (columnName === '客服' || columnName === '客服人员' || columnName === '客服归属') {
            const serviceColor = this.getServicePersonColor(content);
            cell.innerHTML = `<span class="badge" style="background-color: ${serviceColor.bg}; color: ${serviceColor.text}; border: 1px solid ${serviceColor.border};">${content}</span>`;
            return;
        }
    },
    
    // 判断是否为日期格式
    isDateFormat: function(text) {
        // 如果不是字符串或为空，直接返回false
        if (!text || typeof text !== 'string') {
            return false;
        }
        
        // 移除可能的状态信息（例如：2024-10-14（按时还款））
        let cleanText = text.replace(/（.+）/, '');
        
        // 检查完整日期格式（YYYY-MM-DD 或 YYYY/MM/DD 或 YYYY年MM月DD日）
        if (/^\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?$/.test(cleanText)) {
            return true;
        }
        
        // 检查缩略日期格式（MM-DD-YY 或 MM/DD/YY 或 DD-MM-YY）
        if (/^\d{1,2}[-/]\d{1,2}[-/]\d{2,4}$/.test(cleanText)) {
            return true;
        }
        
        // 检查期数日期格式（年份缩写，例如25-05-08）
        if (/^\d{2}[-/]\d{1,2}[-/]\d{1,2}$/.test(cleanText)) {
            return true;
        }
        
        return false;
    }
};

// 当DOM加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    StyleController.init();
});